.class public interface abstract Landroidx/media3/exoplayer/drm/j;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(Ljava/util/UUID;Landroidx/media3/exoplayer/drm/g$a;)[B
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/drm/MediaDrmCallbackException;
        }
    .end annotation
.end method

.method public abstract b(Ljava/util/UUID;Landroidx/media3/exoplayer/drm/g$d;)[B
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/drm/MediaDrmCallbackException;
        }
    .end annotation
.end method
