.class public final synthetic Lcom/google/android/gms/internal/ads/zzcxl;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/android/gms/internal/ads/zzgdu;


# instance fields
.field public final synthetic zza:Lcom/google/android/gms/internal/ads/zzcxp;

.field public final synthetic zzb:Lcom/google/android/gms/internal/ads/zzfjc;


# direct methods
.method public synthetic constructor <init>(Lcom/google/android/gms/internal/ads/zzcxp;Lcom/google/android/gms/internal/ads/zzfjc;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/google/android/gms/internal/ads/zzcxl;->zza:Lcom/google/android/gms/internal/ads/zzcxp;

    iput-object p2, p0, Lcom/google/android/gms/internal/ads/zzcxl;->zzb:Lcom/google/android/gms/internal/ads/zzfjc;

    return-void
.end method


# virtual methods
.method public final zza(Ljava/lang/Object;)Lcom/google/common/util/concurrent/s;
    .locals 2

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzcxl;->zza:Lcom/google/android/gms/internal/ads/zzcxp;

    iget-object v1, p0, Lcom/google/android/gms/internal/ads/zzcxl;->zzb:Lcom/google/android/gms/internal/ads/zzfjc;

    check-cast p1, Lcom/google/android/gms/internal/ads/zzbze;

    invoke-virtual {v0, v1, p1}, Lcom/google/android/gms/internal/ads/zzcxp;->zzf(Lcom/google/android/gms/internal/ads/zzfjc;Lcom/google/android/gms/internal/ads/zzbze;)Lcom/google/common/util/concurrent/s;

    move-result-object p1

    return-object p1
.end method
