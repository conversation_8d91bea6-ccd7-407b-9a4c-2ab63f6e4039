.class public Landroidx/media3/exoplayer/audio/g$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/audio/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "a"
.end annotation


# instance fields
.field public a:I

.field public b:I

.field public c:I

.field public d:I

.field public e:I

.field public f:I

.field public g:I


# direct methods
.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const v0, 0x3d090

    iput v0, p0, Landroidx/media3/exoplayer/audio/g$a;->a:I

    const v1, 0xb71b0

    iput v1, p0, Landroidx/media3/exoplayer/audio/g$a;->b:I

    const/4 v1, 0x4

    iput v1, p0, Landroidx/media3/exoplayer/audio/g$a;->c:I

    iput v0, p0, Landroidx/media3/exoplayer/audio/g$a;->d:I

    const v0, 0x2faf080

    iput v0, p0, Landroidx/media3/exoplayer/audio/g$a;->e:I

    const/4 v0, 0x2

    iput v0, p0, Landroidx/media3/exoplayer/audio/g$a;->f:I

    iput v1, p0, Landroidx/media3/exoplayer/audio/g$a;->g:I

    return-void
.end method

.method public static synthetic a(Landroidx/media3/exoplayer/audio/g$a;)I
    .locals 0

    iget p0, p0, Landroidx/media3/exoplayer/audio/g$a;->a:I

    return p0
.end method

.method public static synthetic b(Landroidx/media3/exoplayer/audio/g$a;)I
    .locals 0

    iget p0, p0, Landroidx/media3/exoplayer/audio/g$a;->b:I

    return p0
.end method

.method public static synthetic c(Landroidx/media3/exoplayer/audio/g$a;)I
    .locals 0

    iget p0, p0, Landroidx/media3/exoplayer/audio/g$a;->c:I

    return p0
.end method

.method public static synthetic d(Landroidx/media3/exoplayer/audio/g$a;)I
    .locals 0

    iget p0, p0, Landroidx/media3/exoplayer/audio/g$a;->d:I

    return p0
.end method

.method public static synthetic e(Landroidx/media3/exoplayer/audio/g$a;)I
    .locals 0

    iget p0, p0, Landroidx/media3/exoplayer/audio/g$a;->e:I

    return p0
.end method

.method public static synthetic f(Landroidx/media3/exoplayer/audio/g$a;)I
    .locals 0

    iget p0, p0, Landroidx/media3/exoplayer/audio/g$a;->f:I

    return p0
.end method

.method public static synthetic g(Landroidx/media3/exoplayer/audio/g$a;)I
    .locals 0

    iget p0, p0, Landroidx/media3/exoplayer/audio/g$a;->g:I

    return p0
.end method


# virtual methods
.method public h()Landroidx/media3/exoplayer/audio/g;
    .locals 1

    new-instance v0, Landroidx/media3/exoplayer/audio/g;

    invoke-direct {v0, p0}, Landroidx/media3/exoplayer/audio/g;-><init>(Landroidx/media3/exoplayer/audio/g$a;)V

    return-object v0
.end method
