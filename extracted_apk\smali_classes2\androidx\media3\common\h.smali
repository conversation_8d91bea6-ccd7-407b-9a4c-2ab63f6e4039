.class public abstract Landroidx/media3/common/h;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/common/h0;


# instance fields
.field public final a:Landroidx/media3/common/m0$c;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Landroidx/media3/common/m0$c;

    invoke-direct {v0}, Landroidx/media3/common/m0$c;-><init>()V

    iput-object v0, p0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    return-void
.end method


# virtual methods
.method public final A()Z
    .locals 3

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v1

    if-nez v1, :cond_0

    invoke-interface {p0}, Landroidx/media3/common/h0;->u()I

    move-result v1

    iget-object v2, p0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    invoke-virtual {v0, v1, v2}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/m0$c;->f()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final C()V
    .locals 2

    const/4 v0, 0x1

    const/4 v0, 0x0

    const v1, 0x7fffffff

    invoke-interface {p0, v0, v1}, Landroidx/media3/common/h0;->G(II)V

    return-void
.end method

.method public final E(I)V
    .locals 1

    add-int/lit8 v0, p1, 0x1

    invoke-interface {p0, p1, v0}, Landroidx/media3/common/h0;->G(II)V

    return-void
.end method

.method public final F()I
    .locals 1

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/m0;->p()I

    move-result v0

    return v0
.end method

.method public final H(Landroidx/media3/common/b0;)V
    .locals 0

    invoke-static {p1}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroidx/media3/common/h;->g0(Ljava/util/List;)V

    return-void
.end method

.method public final I(Landroidx/media3/common/b0;)V
    .locals 0

    invoke-static {p1}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    move-result-object p1

    invoke-virtual {p0, p1}, Landroidx/media3/common/h;->V(Ljava/util/List;)V

    return-void
.end method

.method public final M(ILandroidx/media3/common/b0;)V
    .locals 0

    invoke-static {p2}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    move-result-object p2

    invoke-interface {p0, p1, p2}, Landroidx/media3/common/h0;->R(ILjava/util/List;)V

    return-void
.end method

.method public final N(I)Landroidx/media3/common/b0;
    .locals 2

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object v0

    iget-object v1, p0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    invoke-virtual {v0, p1, v1}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object p1

    iget-object p1, p1, Landroidx/media3/common/m0$c;->c:Landroidx/media3/common/b0;

    return-object p1
.end method

.method public final O(ILandroidx/media3/common/b0;)V
    .locals 1

    add-int/lit8 v0, p1, 0x1

    invoke-static {p2}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    move-result-object p2

    invoke-interface {p0, p1, v0, p2}, Landroidx/media3/common/h0;->D(IILjava/util/List;)V

    return-void
.end method

.method public final P()J
    .locals 3

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v1

    if-eqz v1, :cond_0

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    goto :goto_0

    :cond_0
    invoke-interface {p0}, Landroidx/media3/common/h0;->u()I

    move-result v1

    iget-object v2, p0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    invoke-virtual {v0, v1, v2}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/m0$c;->d()J

    move-result-wide v0

    :goto_0
    return-wide v0
.end method

.method public final V(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/common/b0;",
            ">;)V"
        }
    .end annotation

    const v0, 0x7fffffff

    invoke-interface {p0, v0, p1}, Landroidx/media3/common/h0;->R(ILjava/util/List;)V

    return-void
.end method

.method public final W()I
    .locals 4

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 v0, -0x1

    goto :goto_0

    :cond_0
    invoke-interface {p0}, Landroidx/media3/common/h0;->u()I

    move-result v1

    invoke-virtual {p0}, Landroidx/media3/common/h;->Y()I

    move-result v2

    invoke-interface {p0}, Landroidx/media3/common/h0;->getShuffleModeEnabled()Z

    move-result v3

    invoke-virtual {v0, v1, v2, v3}, Landroidx/media3/common/m0;->e(IIZ)I

    move-result v0

    :goto_0
    return v0
.end method

.method public final X()I
    .locals 4

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v1

    if-eqz v1, :cond_0

    const/4 v0, -0x1

    goto :goto_0

    :cond_0
    invoke-interface {p0}, Landroidx/media3/common/h0;->u()I

    move-result v1

    invoke-virtual {p0}, Landroidx/media3/common/h;->Y()I

    move-result v2

    invoke-interface {p0}, Landroidx/media3/common/h0;->getShuffleModeEnabled()Z

    move-result v3

    invoke-virtual {v0, v1, v2, v3}, Landroidx/media3/common/m0;->l(IIZ)I

    move-result v0

    :goto_0
    return v0
.end method

.method public final Y()I
    .locals 2

    invoke-interface {p0}, Landroidx/media3/common/h0;->getRepeatMode()I

    move-result v0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    const/4 v0, 0x0

    :cond_0
    return v0
.end method

.method public final Z(I)V
    .locals 6

    invoke-interface {p0}, Landroidx/media3/common/h0;->u()I

    move-result v1

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v5, 0x1

    move-object v0, p0

    move v4, p1

    invoke-virtual/range {v0 .. v5}, Landroidx/media3/common/h;->a0(IJIZ)V

    return-void
.end method

.method public abstract a0(IJIZ)V
.end method

.method public final b0(JI)V
    .locals 6

    invoke-interface {p0}, Landroidx/media3/common/h0;->u()I

    move-result v1

    const/4 v5, 0x1

    const/4 v5, 0x0

    move-object v0, p0

    move-wide v2, p1

    move v4, p3

    invoke-virtual/range {v0 .. v5}, Landroidx/media3/common/h;->a0(IJIZ)V

    return-void
.end method

.method public final c0(II)V
    .locals 6

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v5, 0x1

    const/4 v5, 0x0

    move-object v0, p0

    move v1, p1

    move v4, p2

    invoke-virtual/range {v0 .. v5}, Landroidx/media3/common/h;->a0(IJIZ)V

    return-void
.end method

.method public final d0(I)V
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/common/h;->W()I

    move-result v0

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    return-void

    :cond_0
    invoke-interface {p0}, Landroidx/media3/common/h0;->u()I

    move-result v1

    if-ne v0, v1, :cond_1

    invoke-virtual {p0, p1}, Landroidx/media3/common/h;->Z(I)V

    goto :goto_0

    :cond_1
    invoke-virtual {p0, v0, p1}, Landroidx/media3/common/h;->c0(II)V

    :goto_0
    return-void
.end method

.method public final e0(JI)V
    .locals 5

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentPosition()J

    move-result-wide v0

    add-long/2addr v0, p1

    invoke-interface {p0}, Landroidx/media3/common/h0;->getDuration()J

    move-result-wide p1

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v4, p1, v2

    if-eqz v4, :cond_0

    invoke-static {v0, v1, p1, p2}, Ljava/lang/Math;->min(JJ)J

    move-result-wide v0

    :cond_0
    const-wide/16 p1, 0x0

    invoke-static {v0, v1, p1, p2}, Ljava/lang/Math;->max(JJ)J

    move-result-wide p1

    invoke-virtual {p0, p1, p2, p3}, Landroidx/media3/common/h;->b0(JI)V

    return-void
.end method

.method public final f()V
    .locals 6

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    if-nez v0, :cond_3

    invoke-interface {p0}, Landroidx/media3/common/h0;->isPlayingAd()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Landroidx/media3/common/h;->r()Z

    move-result v0

    invoke-virtual {p0}, Landroidx/media3/common/h;->A()Z

    move-result v1

    const/4 v2, 0x7

    if-eqz v1, :cond_1

    invoke-virtual {p0}, Landroidx/media3/common/h;->t()Z

    move-result v1

    if-nez v1, :cond_1

    if-eqz v0, :cond_3

    invoke-virtual {p0, v2}, Landroidx/media3/common/h;->f0(I)V

    goto :goto_0

    :cond_1
    if-eqz v0, :cond_2

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentPosition()J

    move-result-wide v0

    invoke-interface {p0}, Landroidx/media3/common/h0;->p()J

    move-result-wide v3

    cmp-long v5, v0, v3

    if-gtz v5, :cond_2

    invoke-virtual {p0, v2}, Landroidx/media3/common/h;->f0(I)V

    goto :goto_0

    :cond_2
    const-wide/16 v0, 0x0

    invoke-virtual {p0, v0, v1, v2}, Landroidx/media3/common/h;->b0(JI)V

    :cond_3
    :goto_0
    return-void
.end method

.method public final f0(I)V
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/common/h;->X()I

    move-result v0

    const/4 v1, -0x1

    if-ne v0, v1, :cond_0

    return-void

    :cond_0
    invoke-interface {p0}, Landroidx/media3/common/h0;->u()I

    move-result v1

    if-ne v0, v1, :cond_1

    invoke-virtual {p0, p1}, Landroidx/media3/common/h;->Z(I)V

    goto :goto_0

    :cond_1
    invoke-virtual {p0, v0, p1}, Landroidx/media3/common/h;->c0(II)V

    :goto_0
    return-void
.end method

.method public final g()Z
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/common/h;->W()I

    move-result v0

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final g0(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/common/b0;",
            ">;)V"
        }
    .end annotation

    const/4 v0, 0x1

    invoke-interface {p0, p1, v0}, Landroidx/media3/common/h0;->e(Ljava/util/List;Z)V

    return-void
.end method

.method public final i(I)Z
    .locals 1

    invoke-interface {p0}, Landroidx/media3/common/h0;->o()Landroidx/media3/common/h0$b;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroidx/media3/common/h0$b;->b(I)Z

    move-result p1

    return p1
.end method

.method public final isPlaying()Z
    .locals 2

    invoke-interface {p0}, Landroidx/media3/common/h0;->getPlaybackState()I

    move-result v0

    const/4 v1, 0x3

    if-ne v0, v1, :cond_0

    invoke-interface {p0}, Landroidx/media3/common/h0;->getPlayWhenReady()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p0}, Landroidx/media3/common/h0;->k()I

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final j()Z
    .locals 3

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v1

    if-nez v1, :cond_0

    invoke-interface {p0}, Landroidx/media3/common/h0;->u()I

    move-result v1

    iget-object v2, p0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    invoke-virtual {v0, v1, v2}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object v0

    iget-boolean v0, v0, Landroidx/media3/common/m0$c;->i:Z

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final n()V
    .locals 2

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    if-nez v0, :cond_2

    invoke-interface {p0}, Landroidx/media3/common/h0;->isPlayingAd()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Landroidx/media3/common/h;->g()Z

    move-result v0

    const/16 v1, 0x9

    if-eqz v0, :cond_1

    invoke-virtual {p0, v1}, Landroidx/media3/common/h;->d0(I)V

    goto :goto_0

    :cond_1
    invoke-virtual {p0}, Landroidx/media3/common/h;->A()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-virtual {p0}, Landroidx/media3/common/h;->j()Z

    move-result v0

    if-eqz v0, :cond_2

    invoke-interface {p0}, Landroidx/media3/common/h0;->u()I

    move-result v0

    invoke-virtual {p0, v0, v1}, Landroidx/media3/common/h;->c0(II)V

    :cond_2
    :goto_0
    return-void
.end method

.method public final pause()V
    .locals 1

    const/4 v0, 0x1

    const/4 v0, 0x0

    invoke-interface {p0, v0}, Landroidx/media3/common/h0;->setPlayWhenReady(Z)V

    return-void
.end method

.method public final play()V
    .locals 1

    const/4 v0, 0x1

    invoke-interface {p0, v0}, Landroidx/media3/common/h0;->setPlayWhenReady(Z)V

    return-void
.end method

.method public final r()Z
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/common/h;->X()I

    move-result v0

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final seekTo(IJ)V
    .locals 6

    const/16 v4, 0xa

    const/4 v5, 0x1

    const/4 v5, 0x0

    move-object v0, p0

    move v1, p1

    move-wide v2, p2

    invoke-virtual/range {v0 .. v5}, Landroidx/media3/common/h;->a0(IJIZ)V

    return-void
.end method

.method public final seekTo(J)V
    .locals 1

    const/4 v0, 0x5

    invoke-virtual {p0, p1, p2, v0}, Landroidx/media3/common/h;->b0(JI)V

    return-void
.end method

.method public final seekToDefaultPosition()V
    .locals 2

    invoke-interface {p0}, Landroidx/media3/common/h0;->u()I

    move-result v0

    const/4 v1, 0x4

    invoke-virtual {p0, v0, v1}, Landroidx/media3/common/h;->c0(II)V

    return-void
.end method

.method public final setPlaybackSpeed(F)V
    .locals 1

    invoke-interface {p0}, Landroidx/media3/common/h0;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object v0

    invoke-virtual {v0, p1}, Landroidx/media3/common/g0;->b(F)Landroidx/media3/common/g0;

    move-result-object p1

    invoke-interface {p0, p1}, Landroidx/media3/common/h0;->b(Landroidx/media3/common/g0;)V

    return-void
.end method

.method public final t()Z
    .locals 3

    invoke-interface {p0}, Landroidx/media3/common/h0;->getCurrentTimeline()Landroidx/media3/common/m0;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v1

    if-nez v1, :cond_0

    invoke-interface {p0}, Landroidx/media3/common/h0;->u()I

    move-result v1

    iget-object v2, p0, Landroidx/media3/common/h;->a:Landroidx/media3/common/m0$c;

    invoke-virtual {v0, v1, v2}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object v0

    iget-boolean v0, v0, Landroidx/media3/common/m0$c;->h:Z

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final w()V
    .locals 3

    invoke-interface {p0}, Landroidx/media3/common/h0;->s()J

    move-result-wide v0

    const/16 v2, 0xc

    invoke-virtual {p0, v0, v1, v2}, Landroidx/media3/common/h;->e0(JI)V

    return-void
.end method

.method public final x()V
    .locals 3

    invoke-interface {p0}, Landroidx/media3/common/h0;->z()J

    move-result-wide v0

    neg-long v0, v0

    const/16 v2, 0xb

    invoke-virtual {p0, v0, v1, v2}, Landroidx/media3/common/h;->e0(JI)V

    return-void
.end method
