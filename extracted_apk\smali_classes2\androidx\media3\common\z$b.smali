.class public final Landroidx/media3/common/z$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/common/z;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public a:Landroidx/media3/common/k;

.field public b:I

.field public c:I

.field public d:F

.field public e:J


# direct methods
.method public constructor <init>(Landroidx/media3/common/k;II)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/common/z$b;->a:Landroidx/media3/common/k;

    iput p2, p0, Landroidx/media3/common/z$b;->b:I

    iput p3, p0, Landroidx/media3/common/z$b;->c:I

    const/high16 p1, 0x3f800000    # 1.0f

    iput p1, p0, Landroidx/media3/common/z$b;->d:F

    return-void
.end method


# virtual methods
.method public a()Landroidx/media3/common/z;
    .locals 9

    new-instance v8, Landroidx/media3/common/z;

    iget-object v1, p0, Landroidx/media3/common/z$b;->a:Landroidx/media3/common/k;

    iget v2, p0, Landroidx/media3/common/z$b;->b:I

    iget v3, p0, Landroidx/media3/common/z$b;->c:I

    iget v4, p0, Landroidx/media3/common/z$b;->d:F

    iget-wide v5, p0, Landroidx/media3/common/z$b;->e:J

    const/4 v7, 0x1

    const/4 v7, 0x0

    move-object v0, v8

    invoke-direct/range {v0 .. v7}, Landroidx/media3/common/z;-><init>(Landroidx/media3/common/k;IIFJLandroidx/media3/common/z$a;)V

    return-object v8
.end method

.method public b(F)Landroidx/media3/common/z$b;
    .locals 0

    iput p1, p0, Landroidx/media3/common/z$b;->d:F

    return-object p0
.end method
