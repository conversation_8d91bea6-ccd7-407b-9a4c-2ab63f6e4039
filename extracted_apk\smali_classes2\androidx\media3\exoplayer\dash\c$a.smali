.class public final Landroidx/media3/exoplayer/dash/c$a;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/dash/a$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/dash/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public final a:Landroidx/media3/datasource/a$a;

.field public final b:I

.field public final c:Lv2/f$a;


# direct methods
.method public constructor <init>(Landroidx/media3/datasource/a$a;)V
    .locals 1

    const/4 v0, 0x1

    invoke-direct {p0, p1, v0}, Landroidx/media3/exoplayer/dash/c$a;-><init>(Landroidx/media3/datasource/a$a;I)V

    return-void
.end method

.method public constructor <init>(Landroidx/media3/datasource/a$a;I)V
    .locals 1

    sget-object v0, Lv2/d;->k:Lv2/d$b;

    invoke-direct {p0, v0, p1, p2}, Landroidx/media3/exoplayer/dash/c$a;-><init>(Lv2/f$a;Landroidx/media3/datasource/a$a;I)V

    return-void
.end method

.method public constructor <init>(Lv2/f$a;Landroidx/media3/datasource/a$a;I)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/dash/c$a;->c:Lv2/f$a;

    iput-object p2, p0, Landroidx/media3/exoplayer/dash/c$a;->a:Landroidx/media3/datasource/a$a;

    iput p3, p0, Landroidx/media3/exoplayer/dash/c$a;->b:I

    return-void
.end method


# virtual methods
.method public bridge synthetic a(Lt3/s$a;)Landroidx/media3/exoplayer/dash/a$a;
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/dash/c$a;->f(Lt3/s$a;)Landroidx/media3/exoplayer/dash/c$a;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic b(Z)Landroidx/media3/exoplayer/dash/a$a;
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/dash/c$a;->e(Z)Landroidx/media3/exoplayer/dash/c$a;

    move-result-object p1

    return-object p1
.end method

.method public c(Landroidx/media3/common/y;)Landroidx/media3/common/y;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/c$a;->c:Lv2/f$a;

    invoke-interface {v0, p1}, Lv2/f$a;->c(Landroidx/media3/common/y;)Landroidx/media3/common/y;

    move-result-object p1

    return-object p1
.end method

.method public d(Landroidx/media3/exoplayer/upstream/n;Lm2/c;Ll2/b;I[ILx2/z;IJZLjava/util/List;Landroidx/media3/exoplayer/dash/d$c;Lh2/o;Lj2/x3;Landroidx/media3/exoplayer/upstream/f;)Landroidx/media3/exoplayer/dash/a;
    .locals 21
    .param p12    # Landroidx/media3/exoplayer/dash/d$c;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p13    # Lh2/o;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p15    # Landroidx/media3/exoplayer/upstream/f;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/exoplayer/upstream/n;",
            "Lm2/c;",
            "Ll2/b;",
            "I[I",
            "Lx2/z;",
            "IJZ",
            "Ljava/util/List<",
            "Landroidx/media3/common/y;",
            ">;",
            "Landroidx/media3/exoplayer/dash/d$c;",
            "Lh2/o;",
            "Lj2/x3;",
            "Landroidx/media3/exoplayer/upstream/f;",
            ")",
            "Landroidx/media3/exoplayer/dash/a;"
        }
    .end annotation

    move-object/from16 v0, p0

    move-object/from16 v1, p13

    iget-object v2, v0, Landroidx/media3/exoplayer/dash/c$a;->a:Landroidx/media3/datasource/a$a;

    invoke-interface {v2}, Landroidx/media3/datasource/a$a;->createDataSource()Landroidx/media3/datasource/a;

    move-result-object v12

    if-eqz v1, :cond_0

    invoke-interface {v12, v1}, Landroidx/media3/datasource/a;->c(Lh2/o;)V

    :cond_0
    new-instance v1, Landroidx/media3/exoplayer/dash/c;

    move-object v3, v1

    iget-object v4, v0, Landroidx/media3/exoplayer/dash/c$a;->c:Lv2/f$a;

    iget v15, v0, Landroidx/media3/exoplayer/dash/c$a;->b:I

    move-object/from16 v5, p1

    move-object/from16 v6, p2

    move-object/from16 v7, p3

    move/from16 v8, p4

    move-object/from16 v9, p5

    move-object/from16 v10, p6

    move/from16 v11, p7

    move-wide/from16 v13, p8

    move/from16 v16, p10

    move-object/from16 v17, p11

    move-object/from16 v18, p12

    move-object/from16 v19, p14

    move-object/from16 v20, p15

    invoke-direct/range {v3 .. v20}, Landroidx/media3/exoplayer/dash/c;-><init>(Lv2/f$a;Landroidx/media3/exoplayer/upstream/n;Lm2/c;Ll2/b;I[ILx2/z;ILandroidx/media3/datasource/a;JIZLjava/util/List;Landroidx/media3/exoplayer/dash/d$c;Lj2/x3;Landroidx/media3/exoplayer/upstream/f;)V

    return-object v1
.end method

.method public e(Z)Landroidx/media3/exoplayer/dash/c$a;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/c$a;->c:Lv2/f$a;

    invoke-interface {v0, p1}, Lv2/f$a;->b(Z)Lv2/f$a;

    return-object p0
.end method

.method public f(Lt3/s$a;)Landroidx/media3/exoplayer/dash/c$a;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/c$a;->c:Lv2/f$a;

    invoke-interface {v0, p1}, Lv2/f$a;->a(Lt3/s$a;)Lv2/f$a;

    return-object p0
.end method
