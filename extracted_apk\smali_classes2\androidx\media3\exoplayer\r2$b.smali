.class public final Landroidx/media3/exoplayer/r2$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/r2;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:Landroidx/media3/exoplayer/source/l;

.field public final b:Landroidx/media3/exoplayer/source/l$c;

.field public final c:Landroidx/media3/exoplayer/r2$a;


# direct methods
.method public constructor <init>(Landroidx/media3/exoplayer/source/l;Landroidx/media3/exoplayer/source/l$c;Landroidx/media3/exoplayer/r2$a;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/r2$b;->a:Landroidx/media3/exoplayer/source/l;

    iput-object p2, p0, Landroidx/media3/exoplayer/r2$b;->b:Landroidx/media3/exoplayer/source/l$c;

    iput-object p3, p0, Landroidx/media3/exoplayer/r2$b;->c:Landroidx/media3/exoplayer/r2$a;

    return-void
.end method
