.class public interface abstract Landroidx/media3/exoplayer/source/i$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/source/i;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract a(Landroidx/media3/exoplayer/source/l$b;Ljava/io/IOException;)V
.end method

.method public abstract b(Landroidx/media3/exoplayer/source/l$b;)V
.end method
