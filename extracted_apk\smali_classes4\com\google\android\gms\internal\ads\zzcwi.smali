.class public abstract Lcom/google/android/gms/internal/ads/zzcwi;
.super Ljava/lang/Object;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract zzb()Lcom/google/android/gms/internal/ads/zzdav;
.end method

.method public abstract zzc()Lcom/google/android/gms/internal/ads/zzdbp;
.end method

.method public abstract zzd()Lcom/google/android/gms/internal/ads/zzdbw;
.end method

.method public abstract zze()Lcom/google/android/gms/internal/ads/zzdce;
.end method

.method public abstract zzf()Lcom/google/android/gms/internal/ads/zzdiw;
.end method

.method public abstract zzi()Lcom/google/android/gms/internal/ads/zzenv;
.end method

.method public abstract zzj()Lcom/google/android/gms/internal/ads/zzeob;
.end method
