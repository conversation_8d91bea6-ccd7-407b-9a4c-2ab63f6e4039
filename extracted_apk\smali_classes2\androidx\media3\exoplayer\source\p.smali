.class public final Landroidx/media3/exoplayer/source/p;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/source/k;
.implements Lz2/u;
.implements Landroidx/media3/exoplayer/upstream/Loader$b;
.implements Landroidx/media3/exoplayer/upstream/Loader$e;
.implements Landroidx/media3/exoplayer/source/s$d;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/source/p$c;,
        Landroidx/media3/exoplayer/source/p$e;,
        Landroidx/media3/exoplayer/source/p$f;,
        Landroidx/media3/exoplayer/source/p$d;,
        Landroidx/media3/exoplayer/source/p$b;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroidx/media3/exoplayer/source/k;",
        "Lz2/u;",
        "Landroidx/media3/exoplayer/upstream/Loader$b<",
        "Landroidx/media3/exoplayer/source/p$b;",
        ">;",
        "Landroidx/media3/exoplayer/upstream/Loader$e;",
        "Landroidx/media3/exoplayer/source/s$d;"
    }
.end annotation


# static fields
.field public static final O:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public static final P:Landroidx/media3/common/y;


# instance fields
.field public A:Lz2/m0;

.field public B:J

.field public C:Z

.field public D:I

.field public E:Z

.field public F:Z

.field public G:I

.field public H:Z

.field public I:J

.field public J:J

.field public K:Z

.field public L:I

.field public M:Z

.field public N:Z

.field public final a:Landroid/net/Uri;

.field public final b:Landroidx/media3/datasource/a;

.field public final c:Landroidx/media3/exoplayer/drm/c;

.field public final d:Landroidx/media3/exoplayer/upstream/m;

.field public final f:Landroidx/media3/exoplayer/source/m$a;

.field public final g:Landroidx/media3/exoplayer/drm/b$a;

.field public final h:Landroidx/media3/exoplayer/source/p$c;

.field public final i:Landroidx/media3/exoplayer/upstream/b;

.field public final j:Ljava/lang/String;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final k:J

.field public final l:Landroidx/media3/exoplayer/upstream/Loader;

.field public final m:Landroidx/media3/exoplayer/source/o;

.field public final n:Le2/g;

.field public final o:Ljava/lang/Runnable;

.field public final p:Ljava/lang/Runnable;

.field public final q:Landroid/os/Handler;

.field public final r:Z

.field public s:Landroidx/media3/exoplayer/source/k$a;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public t:Landroidx/media3/extractor/metadata/icy/IcyHeaders;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public u:[Landroidx/media3/exoplayer/source/s;

.field public v:[Landroidx/media3/exoplayer/source/p$e;

.field public w:Z

.field public x:Z

.field public y:Z

.field public z:Landroidx/media3/exoplayer/source/p$f;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    invoke-static {}, Landroidx/media3/exoplayer/source/p;->A()Ljava/util/Map;

    move-result-object v0

    sput-object v0, Landroidx/media3/exoplayer/source/p;->O:Ljava/util/Map;

    new-instance v0, Landroidx/media3/common/y$b;

    invoke-direct {v0}, Landroidx/media3/common/y$b;-><init>()V

    const-string v1, "icy"

    invoke-virtual {v0, v1}, Landroidx/media3/common/y$b;->X(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v0

    const-string v1, "application/x-icy"

    invoke-virtual {v0, v1}, Landroidx/media3/common/y$b;->k0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object v0

    sput-object v0, Landroidx/media3/exoplayer/source/p;->P:Landroidx/media3/common/y;

    return-void
.end method

.method public constructor <init>(Landroid/net/Uri;Landroidx/media3/datasource/a;Landroidx/media3/exoplayer/source/o;Landroidx/media3/exoplayer/drm/c;Landroidx/media3/exoplayer/drm/b$a;Landroidx/media3/exoplayer/upstream/m;Landroidx/media3/exoplayer/source/m$a;Landroidx/media3/exoplayer/source/p$c;Landroidx/media3/exoplayer/upstream/b;Ljava/lang/String;IJ)V
    .locals 0
    .param p10    # Ljava/lang/String;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/source/p;->a:Landroid/net/Uri;

    iput-object p2, p0, Landroidx/media3/exoplayer/source/p;->b:Landroidx/media3/datasource/a;

    iput-object p4, p0, Landroidx/media3/exoplayer/source/p;->c:Landroidx/media3/exoplayer/drm/c;

    iput-object p5, p0, Landroidx/media3/exoplayer/source/p;->g:Landroidx/media3/exoplayer/drm/b$a;

    iput-object p6, p0, Landroidx/media3/exoplayer/source/p;->d:Landroidx/media3/exoplayer/upstream/m;

    iput-object p7, p0, Landroidx/media3/exoplayer/source/p;->f:Landroidx/media3/exoplayer/source/m$a;

    iput-object p8, p0, Landroidx/media3/exoplayer/source/p;->h:Landroidx/media3/exoplayer/source/p$c;

    iput-object p9, p0, Landroidx/media3/exoplayer/source/p;->i:Landroidx/media3/exoplayer/upstream/b;

    iput-object p10, p0, Landroidx/media3/exoplayer/source/p;->j:Ljava/lang/String;

    int-to-long p1, p11

    iput-wide p1, p0, Landroidx/media3/exoplayer/source/p;->k:J

    new-instance p1, Landroidx/media3/exoplayer/upstream/Loader;

    const-string p2, "ProgressiveMediaPeriod"

    invoke-direct {p1, p2}, Landroidx/media3/exoplayer/upstream/Loader;-><init>(Ljava/lang/String;)V

    iput-object p1, p0, Landroidx/media3/exoplayer/source/p;->l:Landroidx/media3/exoplayer/upstream/Loader;

    iput-object p3, p0, Landroidx/media3/exoplayer/source/p;->m:Landroidx/media3/exoplayer/source/o;

    iput-wide p12, p0, Landroidx/media3/exoplayer/source/p;->B:J

    const/4 p1, 0x1

    const/4 p2, 0x1

    const/4 p2, 0x0

    const-wide p3, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long p5, p12, p3

    if-eqz p5, :cond_0

    const/4 p5, 0x1

    goto :goto_0

    :cond_0
    const/4 p5, 0x1

    const/4 p5, 0x0

    :goto_0
    iput-boolean p5, p0, Landroidx/media3/exoplayer/source/p;->r:Z

    new-instance p5, Le2/g;

    invoke-direct {p5}, Le2/g;-><init>()V

    iput-object p5, p0, Landroidx/media3/exoplayer/source/p;->n:Le2/g;

    new-instance p5, Lu2/y;

    invoke-direct {p5, p0}, Lu2/y;-><init>(Landroidx/media3/exoplayer/source/p;)V

    iput-object p5, p0, Landroidx/media3/exoplayer/source/p;->o:Ljava/lang/Runnable;

    new-instance p5, Lu2/z;

    invoke-direct {p5, p0}, Lu2/z;-><init>(Landroidx/media3/exoplayer/source/p;)V

    iput-object p5, p0, Landroidx/media3/exoplayer/source/p;->p:Ljava/lang/Runnable;

    invoke-static {}, Le2/u0;->A()Landroid/os/Handler;

    move-result-object p5

    iput-object p5, p0, Landroidx/media3/exoplayer/source/p;->q:Landroid/os/Handler;

    new-array p5, p2, [Landroidx/media3/exoplayer/source/p$e;

    iput-object p5, p0, Landroidx/media3/exoplayer/source/p;->v:[Landroidx/media3/exoplayer/source/p$e;

    new-array p2, p2, [Landroidx/media3/exoplayer/source/s;

    iput-object p2, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    iput-wide p3, p0, Landroidx/media3/exoplayer/source/p;->J:J

    iput p1, p0, Landroidx/media3/exoplayer/source/p;->D:I

    return-void
.end method

.method public static A()Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    const-string v1, "Icy-MetaData"

    const-string v2, "1"

    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableMap(Ljava/util/Map;)Ljava/util/Map;

    move-result-object v0

    return-object v0
.end method

.method private E()Z
    .locals 5

    iget-wide v0, p0, Landroidx/media3/exoplayer/source/p;->J:J

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v4, v0, v2

    if-eqz v4, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method private J()V
    .locals 11

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->N:Z

    if-nez v0, :cond_a

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->x:Z

    if-nez v0, :cond_a

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->w:Z

    if-eqz v0, :cond_a

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->A:Lz2/m0;

    if-nez v0, :cond_0

    goto/16 :goto_5

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length v1, v0

    const/4 v2, 0x1

    const/4 v2, 0x0

    const/4 v3, 0x1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v1, :cond_2

    aget-object v4, v0, v3

    invoke-virtual {v4}, Landroidx/media3/exoplayer/source/s;->G()Landroidx/media3/common/y;

    move-result-object v4

    if-nez v4, :cond_1

    return-void

    :cond_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_2
    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->n:Le2/g;

    invoke-virtual {v0}, Le2/g;->d()Z

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length v0, v0

    new-array v1, v0, [Landroidx/media3/common/n0;

    new-array v3, v0, [Z

    const/4 v4, 0x1

    const/4 v4, 0x0

    :goto_1
    const/4 v5, 0x1

    if-ge v4, v0, :cond_9

    iget-object v6, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    aget-object v6, v6, v4

    invoke-virtual {v6}, Landroidx/media3/exoplayer/source/s;->G()Landroidx/media3/common/y;

    move-result-object v6

    invoke-static {v6}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Landroidx/media3/common/y;

    iget-object v7, v6, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-static {v7}, Landroidx/media3/common/f0;->o(Ljava/lang/String;)Z

    move-result v8

    if-nez v8, :cond_4

    invoke-static {v7}, Landroidx/media3/common/f0;->s(Ljava/lang/String;)Z

    move-result v7

    if-eqz v7, :cond_3

    goto :goto_2

    :cond_3
    const/4 v7, 0x1

    const/4 v7, 0x0

    goto :goto_3

    :cond_4
    :goto_2
    const/4 v7, 0x1

    :goto_3
    aput-boolean v7, v3, v4

    iget-boolean v9, p0, Landroidx/media3/exoplayer/source/p;->y:Z

    or-int/2addr v7, v9

    iput-boolean v7, p0, Landroidx/media3/exoplayer/source/p;->y:Z

    iget-object v7, p0, Landroidx/media3/exoplayer/source/p;->t:Landroidx/media3/extractor/metadata/icy/IcyHeaders;

    if-eqz v7, :cond_8

    if-nez v8, :cond_5

    iget-object v9, p0, Landroidx/media3/exoplayer/source/p;->v:[Landroidx/media3/exoplayer/source/p$e;

    aget-object v9, v9, v4

    iget-boolean v9, v9, Landroidx/media3/exoplayer/source/p$e;->b:Z

    if-eqz v9, :cond_7

    :cond_5
    iget-object v9, v6, Landroidx/media3/common/y;->k:Landroidx/media3/common/Metadata;

    if-nez v9, :cond_6

    new-instance v9, Landroidx/media3/common/Metadata;

    new-array v10, v5, [Landroidx/media3/common/Metadata$Entry;

    aput-object v7, v10, v2

    invoke-direct {v9, v10}, Landroidx/media3/common/Metadata;-><init>([Landroidx/media3/common/Metadata$Entry;)V

    goto :goto_4

    :cond_6
    new-array v10, v5, [Landroidx/media3/common/Metadata$Entry;

    aput-object v7, v10, v2

    invoke-virtual {v9, v10}, Landroidx/media3/common/Metadata;->a([Landroidx/media3/common/Metadata$Entry;)Landroidx/media3/common/Metadata;

    move-result-object v9

    :goto_4
    invoke-virtual {v6}, Landroidx/media3/common/y;->b()Landroidx/media3/common/y$b;

    move-result-object v6

    invoke-virtual {v6, v9}, Landroidx/media3/common/y$b;->d0(Landroidx/media3/common/Metadata;)Landroidx/media3/common/y$b;

    move-result-object v6

    invoke-virtual {v6}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object v6

    :cond_7
    if-eqz v8, :cond_8

    iget v8, v6, Landroidx/media3/common/y;->g:I

    const/4 v9, -0x1

    if-ne v8, v9, :cond_8

    iget v8, v6, Landroidx/media3/common/y;->h:I

    if-ne v8, v9, :cond_8

    iget v8, v7, Landroidx/media3/extractor/metadata/icy/IcyHeaders;->bitrate:I

    if-eq v8, v9, :cond_8

    invoke-virtual {v6}, Landroidx/media3/common/y;->b()Landroidx/media3/common/y$b;

    move-result-object v6

    iget v7, v7, Landroidx/media3/extractor/metadata/icy/IcyHeaders;->bitrate:I

    invoke-virtual {v6, v7}, Landroidx/media3/common/y$b;->K(I)Landroidx/media3/common/y$b;

    move-result-object v6

    invoke-virtual {v6}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object v6

    :cond_8
    iget-object v7, p0, Landroidx/media3/exoplayer/source/p;->c:Landroidx/media3/exoplayer/drm/c;

    invoke-interface {v7, v6}, Landroidx/media3/exoplayer/drm/c;->c(Landroidx/media3/common/y;)I

    move-result v7

    invoke-virtual {v6, v7}, Landroidx/media3/common/y;->c(I)Landroidx/media3/common/y;

    move-result-object v6

    new-instance v7, Landroidx/media3/common/n0;

    invoke-static {v4}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object v8

    new-array v5, v5, [Landroidx/media3/common/y;

    aput-object v6, v5, v2

    invoke-direct {v7, v8, v5}, Landroidx/media3/common/n0;-><init>(Ljava/lang/String;[Landroidx/media3/common/y;)V

    aput-object v7, v1, v4

    add-int/lit8 v4, v4, 0x1

    goto/16 :goto_1

    :cond_9
    new-instance v0, Landroidx/media3/exoplayer/source/p$f;

    new-instance v2, Lu2/k0;

    invoke-direct {v2, v1}, Lu2/k0;-><init>([Landroidx/media3/common/n0;)V

    invoke-direct {v0, v2, v3}, Landroidx/media3/exoplayer/source/p$f;-><init>(Lu2/k0;[Z)V

    iput-object v0, p0, Landroidx/media3/exoplayer/source/p;->z:Landroidx/media3/exoplayer/source/p$f;

    iput-boolean v5, p0, Landroidx/media3/exoplayer/source/p;->x:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->s:Landroidx/media3/exoplayer/source/k$a;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/source/k$a;

    invoke-interface {v0, p0}, Landroidx/media3/exoplayer/source/k$a;->g(Landroidx/media3/exoplayer/source/k;)V

    :cond_a
    :goto_5
    return-void
.end method

.method public static synthetic d(Landroidx/media3/exoplayer/source/p;Lz2/m0;)V
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/p;->I(Lz2/m0;)V

    return-void
.end method

.method public static synthetic h(Landroidx/media3/exoplayer/source/p;)V
    .locals 0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->H()V

    return-void
.end method

.method public static synthetic i(Landroidx/media3/exoplayer/source/p;)V
    .locals 0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->G()V

    return-void
.end method

.method public static synthetic j(Landroidx/media3/exoplayer/source/p;)V
    .locals 0

    invoke-direct {p0}, Landroidx/media3/exoplayer/source/p;->J()V

    return-void
.end method

.method public static synthetic l(Landroidx/media3/exoplayer/source/p;)J
    .locals 2

    iget-wide v0, p0, Landroidx/media3/exoplayer/source/p;->k:J

    return-wide v0
.end method

.method public static synthetic m(Landroidx/media3/exoplayer/source/p;)Ljava/lang/Runnable;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/source/p;->p:Ljava/lang/Runnable;

    return-object p0
.end method

.method public static synthetic p(Landroidx/media3/exoplayer/source/p;)Landroid/os/Handler;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/source/p;->q:Landroid/os/Handler;

    return-object p0
.end method

.method public static synthetic q(Landroidx/media3/exoplayer/source/p;Z)J
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/p;->C(Z)J

    move-result-wide p0

    return-wide p0
.end method

.method public static synthetic r()Ljava/util/Map;
    .locals 1

    sget-object v0, Landroidx/media3/exoplayer/source/p;->O:Ljava/util/Map;

    return-object v0
.end method

.method public static synthetic s(Landroidx/media3/exoplayer/source/p;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/source/p;->j:Ljava/lang/String;

    return-object p0
.end method

.method public static synthetic t(Landroidx/media3/exoplayer/source/p;)J
    .locals 2

    iget-wide v0, p0, Landroidx/media3/exoplayer/source/p;->B:J

    return-wide v0
.end method

.method public static synthetic u(Landroidx/media3/exoplayer/source/p;)V
    .locals 0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->O()V

    return-void
.end method

.method public static synthetic v(Landroidx/media3/exoplayer/source/p;)Landroidx/media3/extractor/metadata/icy/IcyHeaders;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/source/p;->t:Landroidx/media3/extractor/metadata/icy/IcyHeaders;

    return-object p0
.end method

.method public static synthetic w(Landroidx/media3/exoplayer/source/p;Landroidx/media3/extractor/metadata/icy/IcyHeaders;)Landroidx/media3/extractor/metadata/icy/IcyHeaders;
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/source/p;->t:Landroidx/media3/extractor/metadata/icy/IcyHeaders;

    return-object p1
.end method

.method public static synthetic x()Landroidx/media3/common/y;
    .locals 1

    sget-object v0, Landroidx/media3/exoplayer/source/p;->P:Landroidx/media3/common/y;

    return-object v0
.end method


# virtual methods
.method public final B()I
    .locals 5

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length v1, v0

    const/4 v2, 0x1

    const/4 v2, 0x0

    const/4 v3, 0x1

    const/4 v3, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v4, v0, v2

    invoke-virtual {v4}, Landroidx/media3/exoplayer/source/s;->H()I

    move-result v4

    add-int/2addr v3, v4

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return v3
.end method

.method public final C(Z)J
    .locals 5

    const-wide/high16 v0, -0x8000000000000000L

    const/4 v2, 0x1

    const/4 v2, 0x0

    :goto_0
    iget-object v3, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length v3, v3

    if-ge v2, v3, :cond_2

    if-nez p1, :cond_0

    iget-object v3, p0, Landroidx/media3/exoplayer/source/p;->z:Landroidx/media3/exoplayer/source/p$f;

    invoke-static {v3}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Landroidx/media3/exoplayer/source/p$f;

    iget-object v3, v3, Landroidx/media3/exoplayer/source/p$f;->c:[Z

    aget-boolean v3, v3, v2

    if-eqz v3, :cond_1

    :cond_0
    iget-object v3, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    aget-object v3, v3, v2

    invoke-virtual {v3}, Landroidx/media3/exoplayer/source/s;->A()J

    move-result-wide v3

    invoke-static {v0, v1, v3, v4}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v0

    :cond_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_2
    return-wide v0
.end method

.method public D()Lz2/r0;
    .locals 3

    new-instance v0, Landroidx/media3/exoplayer/source/p$e;

    const/4 v1, 0x1

    const/4 v1, 0x0

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Landroidx/media3/exoplayer/source/p$e;-><init>(IZ)V

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/source/p;->S(Landroidx/media3/exoplayer/source/p$e;)Lz2/r0;

    move-result-object v0

    return-object v0
.end method

.method public F(I)Z
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->Z()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    aget-object p1, v0, p1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->M:Z

    invoke-virtual {p1, v0}, Landroidx/media3/exoplayer/source/s;->L(Z)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x1

    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final synthetic G()V
    .locals 1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->N:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->s:Landroidx/media3/exoplayer/source/k$a;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/source/k$a;

    invoke-interface {v0, p0}, Landroidx/media3/exoplayer/source/t$a;->h(Landroidx/media3/exoplayer/source/t;)V

    :cond_0
    return-void
.end method

.method public final synthetic H()V
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->H:Z

    return-void
.end method

.method public final synthetic I(Lz2/m0;)V
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/p;->W(Lz2/m0;)V

    return-void
.end method

.method public final K(I)V
    .locals 10

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->y()V

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->z:Landroidx/media3/exoplayer/source/p$f;

    iget-object v1, v0, Landroidx/media3/exoplayer/source/p$f;->d:[Z

    aget-boolean v2, v1, p1

    if-nez v2, :cond_0

    iget-object v0, v0, Landroidx/media3/exoplayer/source/p$f;->a:Lu2/k0;

    invoke-virtual {v0, p1}, Lu2/k0;->b(I)Landroidx/media3/common/n0;

    move-result-object v0

    const/4 v2, 0x1

    const/4 v2, 0x0

    invoke-virtual {v0, v2}, Landroidx/media3/common/n0;->a(I)Landroidx/media3/common/y;

    move-result-object v5

    iget-object v3, p0, Landroidx/media3/exoplayer/source/p;->f:Landroidx/media3/exoplayer/source/m$a;

    iget-object v0, v5, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-static {v0}, Landroidx/media3/common/f0;->k(Ljava/lang/String;)I

    move-result v4

    const/4 v6, 0x1

    const/4 v6, 0x0

    const/4 v7, 0x1

    const/4 v7, 0x0

    iget-wide v8, p0, Landroidx/media3/exoplayer/source/p;->I:J

    invoke-virtual/range {v3 .. v9}, Landroidx/media3/exoplayer/source/m$a;->h(ILandroidx/media3/common/y;ILjava/lang/Object;J)V

    const/4 v0, 0x1

    aput-boolean v0, v1, p1

    :cond_0
    return-void
.end method

.method public final L(I)V
    .locals 3

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->y()V

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->z:Landroidx/media3/exoplayer/source/p$f;

    iget-object v0, v0, Landroidx/media3/exoplayer/source/p$f;->b:[Z

    iget-boolean v1, p0, Landroidx/media3/exoplayer/source/p;->K:Z

    if-eqz v1, :cond_2

    aget-boolean v0, v0, p1

    if-eqz v0, :cond_2

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    aget-object p1, v0, p1

    const/4 v0, 0x1

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Landroidx/media3/exoplayer/source/s;->L(Z)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_1

    :cond_0
    const-wide/16 v1, 0x0

    iput-wide v1, p0, Landroidx/media3/exoplayer/source/p;->J:J

    iput-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->K:Z

    const/4 p1, 0x1

    iput-boolean p1, p0, Landroidx/media3/exoplayer/source/p;->F:Z

    iput-wide v1, p0, Landroidx/media3/exoplayer/source/p;->I:J

    iput v0, p0, Landroidx/media3/exoplayer/source/p;->L:I

    iget-object p1, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length v1, p1

    :goto_0
    if-ge v0, v1, :cond_1

    aget-object v2, p1, v0

    invoke-virtual {v2}, Landroidx/media3/exoplayer/source/s;->W()V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    iget-object p1, p0, Landroidx/media3/exoplayer/source/p;->s:Landroidx/media3/exoplayer/source/k$a;

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/exoplayer/source/k$a;

    invoke-interface {p1, p0}, Landroidx/media3/exoplayer/source/t$a;->h(Landroidx/media3/exoplayer/source/t;)V

    :cond_2
    :goto_1
    return-void
.end method

.method public M()V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->l:Landroidx/media3/exoplayer/upstream/Loader;

    iget-object v1, p0, Landroidx/media3/exoplayer/source/p;->d:Landroidx/media3/exoplayer/upstream/m;

    iget v2, p0, Landroidx/media3/exoplayer/source/p;->D:I

    invoke-interface {v1, v2}, Landroidx/media3/exoplayer/upstream/m;->a(I)I

    move-result v1

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/upstream/Loader;->j(I)V

    return-void
.end method

.method public N(I)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    aget-object p1, v0, p1

    invoke-virtual {p1}, Landroidx/media3/exoplayer/source/s;->O()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->M()V

    return-void
.end method

.method public final O()V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->q:Landroid/os/Handler;

    new-instance v1, Lu2/a0;

    invoke-direct {v1, p0}, Lu2/a0;-><init>(Landroidx/media3/exoplayer/source/p;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    return-void
.end method

.method public P(Landroidx/media3/exoplayer/source/p$b;JJZ)V
    .locals 15

    move-object v0, p0

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/p$b;->b(Landroidx/media3/exoplayer/source/p$b;)Lh2/m;

    move-result-object v1

    new-instance v14, Lu2/n;

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/p$b;->c(Landroidx/media3/exoplayer/source/p$b;)J

    move-result-wide v3

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/p$b;->d(Landroidx/media3/exoplayer/source/p$b;)Lh2/g;

    move-result-object v5

    invoke-virtual {v1}, Lh2/m;->e()Landroid/net/Uri;

    move-result-object v6

    invoke-virtual {v1}, Lh2/m;->f()Ljava/util/Map;

    move-result-object v7

    invoke-virtual {v1}, Lh2/m;->d()J

    move-result-wide v12

    move-object v2, v14

    move-wide/from16 v8, p2

    move-wide/from16 v10, p4

    invoke-direct/range {v2 .. v13}, Lu2/n;-><init>(JLh2/g;Landroid/net/Uri;Ljava/util/Map;JJJ)V

    iget-object v1, v0, Landroidx/media3/exoplayer/source/p;->d:Landroidx/media3/exoplayer/upstream/m;

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/p$b;->c(Landroidx/media3/exoplayer/source/p$b;)J

    move-result-wide v2

    invoke-interface {v1, v2, v3}, Landroidx/media3/exoplayer/upstream/m;->b(J)V

    iget-object v2, v0, Landroidx/media3/exoplayer/source/p;->f:Landroidx/media3/exoplayer/source/m$a;

    const/4 v4, 0x1

    const/4 v5, -0x1

    const/4 v6, 0x1

    const/4 v6, 0x0

    const/4 v7, 0x1

    const/4 v7, 0x0

    const/4 v8, 0x1

    const/4 v8, 0x0

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/p$b;->e(Landroidx/media3/exoplayer/source/p$b;)J

    move-result-wide v9

    iget-wide v11, v0, Landroidx/media3/exoplayer/source/p;->B:J

    move-object v3, v14

    invoke-virtual/range {v2 .. v12}, Landroidx/media3/exoplayer/source/m$a;->q(Lu2/n;IILandroidx/media3/common/y;ILjava/lang/Object;JJ)V

    if-nez p6, :cond_1

    iget-object v1, v0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length v2, v1

    const/4 v3, 0x1

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_0

    aget-object v4, v1, v3

    invoke-virtual {v4}, Landroidx/media3/exoplayer/source/s;->W()V

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_0
    iget v1, v0, Landroidx/media3/exoplayer/source/p;->G:I

    if-lez v1, :cond_1

    iget-object v1, v0, Landroidx/media3/exoplayer/source/p;->s:Landroidx/media3/exoplayer/source/k$a;

    invoke-static {v1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/exoplayer/source/k$a;

    invoke-interface {v1, p0}, Landroidx/media3/exoplayer/source/t$a;->h(Landroidx/media3/exoplayer/source/t;)V

    :cond_1
    return-void
.end method

.method public Q(Landroidx/media3/exoplayer/source/p$b;JJ)V
    .locals 18

    move-object/from16 v0, p0

    iget-wide v1, v0, Landroidx/media3/exoplayer/source/p;->B:J

    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v5, 0x1

    cmp-long v6, v1, v3

    if-nez v6, :cond_1

    iget-object v1, v0, Landroidx/media3/exoplayer/source/p;->A:Lz2/m0;

    if-eqz v1, :cond_1

    invoke-interface {v1}, Lz2/m0;->isSeekable()Z

    move-result v1

    invoke-virtual {v0, v5}, Landroidx/media3/exoplayer/source/p;->C(Z)J

    move-result-wide v2

    const-wide/high16 v6, -0x8000000000000000L

    cmp-long v4, v2, v6

    if-nez v4, :cond_0

    const-wide/16 v2, 0x0

    goto :goto_0

    :cond_0
    const-wide/16 v6, 0x2710

    add-long/2addr v2, v6

    :goto_0
    iput-wide v2, v0, Landroidx/media3/exoplayer/source/p;->B:J

    iget-object v4, v0, Landroidx/media3/exoplayer/source/p;->h:Landroidx/media3/exoplayer/source/p$c;

    iget-boolean v6, v0, Landroidx/media3/exoplayer/source/p;->C:Z

    invoke-interface {v4, v2, v3, v1, v6}, Landroidx/media3/exoplayer/source/p$c;->b(JZZ)V

    :cond_1
    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/p$b;->b(Landroidx/media3/exoplayer/source/p$b;)Lh2/m;

    move-result-object v1

    new-instance v2, Lu2/n;

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/p$b;->c(Landroidx/media3/exoplayer/source/p$b;)J

    move-result-wide v7

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/p$b;->d(Landroidx/media3/exoplayer/source/p$b;)Lh2/g;

    move-result-object v9

    invoke-virtual {v1}, Lh2/m;->e()Landroid/net/Uri;

    move-result-object v10

    invoke-virtual {v1}, Lh2/m;->f()Ljava/util/Map;

    move-result-object v11

    invoke-virtual {v1}, Lh2/m;->d()J

    move-result-wide v16

    move-object v6, v2

    move-wide/from16 v12, p2

    move-wide/from16 v14, p4

    invoke-direct/range {v6 .. v17}, Lu2/n;-><init>(JLh2/g;Landroid/net/Uri;Ljava/util/Map;JJJ)V

    iget-object v1, v0, Landroidx/media3/exoplayer/source/p;->d:Landroidx/media3/exoplayer/upstream/m;

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/p$b;->c(Landroidx/media3/exoplayer/source/p$b;)J

    move-result-wide v3

    invoke-interface {v1, v3, v4}, Landroidx/media3/exoplayer/upstream/m;->b(J)V

    iget-object v6, v0, Landroidx/media3/exoplayer/source/p;->f:Landroidx/media3/exoplayer/source/m$a;

    const/4 v8, 0x1

    const/4 v9, -0x1

    const/4 v10, 0x1

    const/4 v10, 0x0

    const/4 v11, 0x1

    const/4 v11, 0x0

    const/4 v12, 0x1

    const/4 v12, 0x0

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/p$b;->e(Landroidx/media3/exoplayer/source/p$b;)J

    move-result-wide v13

    iget-wide v3, v0, Landroidx/media3/exoplayer/source/p;->B:J

    move-object v7, v2

    move-wide v15, v3

    invoke-virtual/range {v6 .. v16}, Landroidx/media3/exoplayer/source/m$a;->t(Lu2/n;IILandroidx/media3/common/y;ILjava/lang/Object;JJ)V

    iput-boolean v5, v0, Landroidx/media3/exoplayer/source/p;->M:Z

    iget-object v1, v0, Landroidx/media3/exoplayer/source/p;->s:Landroidx/media3/exoplayer/source/k$a;

    invoke-static {v1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/exoplayer/source/k$a;

    invoke-interface {v1, v0}, Landroidx/media3/exoplayer/source/t$a;->h(Landroidx/media3/exoplayer/source/t;)V

    return-void
.end method

.method public R(Landroidx/media3/exoplayer/source/p$b;JJLjava/io/IOException;I)Landroidx/media3/exoplayer/upstream/Loader$c;
    .locals 25

    move-object/from16 v0, p0

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/p$b;->b(Landroidx/media3/exoplayer/source/p$b;)Lh2/m;

    move-result-object v1

    new-instance v14, Lu2/n;

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/p$b;->c(Landroidx/media3/exoplayer/source/p$b;)J

    move-result-wide v3

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/p$b;->d(Landroidx/media3/exoplayer/source/p$b;)Lh2/g;

    move-result-object v5

    invoke-virtual {v1}, Lh2/m;->e()Landroid/net/Uri;

    move-result-object v6

    invoke-virtual {v1}, Lh2/m;->f()Ljava/util/Map;

    move-result-object v7

    invoke-virtual {v1}, Lh2/m;->d()J

    move-result-wide v12

    move-object v2, v14

    move-wide/from16 v8, p2

    move-wide/from16 v10, p4

    invoke-direct/range {v2 .. v13}, Lu2/n;-><init>(JLh2/g;Landroid/net/Uri;Ljava/util/Map;JJJ)V

    new-instance v1, Lu2/o;

    const/16 v16, 0x1

    const/16 v17, -0x1

    const/16 v18, 0x0

    const/16 v19, 0x0

    const/16 v20, 0x0

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/p$b;->e(Landroidx/media3/exoplayer/source/p$b;)J

    move-result-wide v2

    invoke-static {v2, v3}, Le2/u0;->B1(J)J

    move-result-wide v21

    iget-wide v2, v0, Landroidx/media3/exoplayer/source/p;->B:J

    invoke-static {v2, v3}, Le2/u0;->B1(J)J

    move-result-wide v23

    move-object v15, v1

    invoke-direct/range {v15 .. v24}, Lu2/o;-><init>(IILandroidx/media3/common/y;ILjava/lang/Object;JJ)V

    iget-object v2, v0, Landroidx/media3/exoplayer/source/p;->d:Landroidx/media3/exoplayer/upstream/m;

    new-instance v3, Landroidx/media3/exoplayer/upstream/m$c;

    move-object/from16 v13, p6

    move/from16 v4, p7

    invoke-direct {v3, v14, v1, v13, v4}, Landroidx/media3/exoplayer/upstream/m$c;-><init>(Lu2/n;Lu2/o;Ljava/io/IOException;I)V

    invoke-interface {v2, v3}, Landroidx/media3/exoplayer/upstream/m;->c(Landroidx/media3/exoplayer/upstream/m$c;)J

    move-result-wide v1

    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v5, 0x1

    cmp-long v6, v1, v3

    if-nez v6, :cond_0

    sget-object v1, Landroidx/media3/exoplayer/upstream/Loader;->g:Landroidx/media3/exoplayer/upstream/Loader$c;

    move-object/from16 v15, p1

    goto :goto_1

    :cond_0
    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/source/p;->B()I

    move-result v3

    iget v4, v0, Landroidx/media3/exoplayer/source/p;->L:I

    if-le v3, v4, :cond_1

    move-object/from16 v15, p1

    const/4 v4, 0x1

    goto :goto_0

    :cond_1
    const/4 v4, 0x1

    const/4 v4, 0x0

    move-object/from16 v15, p1

    :goto_0
    invoke-virtual {v0, v15, v3}, Landroidx/media3/exoplayer/source/p;->z(Landroidx/media3/exoplayer/source/p$b;I)Z

    move-result v3

    if-eqz v3, :cond_2

    invoke-static {v4, v1, v2}, Landroidx/media3/exoplayer/upstream/Loader;->g(ZJ)Landroidx/media3/exoplayer/upstream/Loader$c;

    move-result-object v1

    goto :goto_1

    :cond_2
    sget-object v1, Landroidx/media3/exoplayer/upstream/Loader;->f:Landroidx/media3/exoplayer/upstream/Loader$c;

    :goto_1
    invoke-virtual {v1}, Landroidx/media3/exoplayer/upstream/Loader$c;->c()Z

    move-result v2

    xor-int/lit8 v16, v2, 0x1

    iget-object v2, v0, Landroidx/media3/exoplayer/source/p;->f:Landroidx/media3/exoplayer/source/m$a;

    const/4 v4, 0x1

    const/4 v5, -0x1

    const/4 v6, 0x1

    const/4 v6, 0x0

    const/4 v7, 0x1

    const/4 v7, 0x0

    const/4 v8, 0x1

    const/4 v8, 0x0

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/p$b;->e(Landroidx/media3/exoplayer/source/p$b;)J

    move-result-wide v9

    iget-wide v11, v0, Landroidx/media3/exoplayer/source/p;->B:J

    move-object v3, v14

    move-object/from16 v13, p6

    move/from16 v14, v16

    invoke-virtual/range {v2 .. v14}, Landroidx/media3/exoplayer/source/m$a;->v(Lu2/n;IILandroidx/media3/common/y;ILjava/lang/Object;JJLjava/io/IOException;Z)V

    if-eqz v16, :cond_3

    iget-object v2, v0, Landroidx/media3/exoplayer/source/p;->d:Landroidx/media3/exoplayer/upstream/m;

    invoke-static/range {p1 .. p1}, Landroidx/media3/exoplayer/source/p$b;->c(Landroidx/media3/exoplayer/source/p$b;)J

    move-result-wide v3

    invoke-interface {v2, v3, v4}, Landroidx/media3/exoplayer/upstream/m;->b(J)V

    :cond_3
    return-object v1
.end method

.method public final S(Landroidx/media3/exoplayer/source/p$e;)Lz2/r0;
    .locals 4

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length v0, v0

    const/4 v1, 0x1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    iget-object v2, p0, Landroidx/media3/exoplayer/source/p;->v:[Landroidx/media3/exoplayer/source/p$e;

    aget-object v2, v2, v1

    invoke-virtual {p1, v2}, Landroidx/media3/exoplayer/source/p$e;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_0

    iget-object p1, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    aget-object p1, p1, v1

    return-object p1

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    iget-object v1, p0, Landroidx/media3/exoplayer/source/p;->i:Landroidx/media3/exoplayer/upstream/b;

    iget-object v2, p0, Landroidx/media3/exoplayer/source/p;->c:Landroidx/media3/exoplayer/drm/c;

    iget-object v3, p0, Landroidx/media3/exoplayer/source/p;->g:Landroidx/media3/exoplayer/drm/b$a;

    invoke-static {v1, v2, v3}, Landroidx/media3/exoplayer/source/s;->k(Landroidx/media3/exoplayer/upstream/b;Landroidx/media3/exoplayer/drm/c;Landroidx/media3/exoplayer/drm/b$a;)Landroidx/media3/exoplayer/source/s;

    move-result-object v1

    invoke-virtual {v1, p0}, Landroidx/media3/exoplayer/source/s;->e0(Landroidx/media3/exoplayer/source/s$d;)V

    iget-object v2, p0, Landroidx/media3/exoplayer/source/p;->v:[Landroidx/media3/exoplayer/source/p$e;

    add-int/lit8 v3, v0, 0x1

    invoke-static {v2, v3}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    move-result-object v2

    check-cast v2, [Landroidx/media3/exoplayer/source/p$e;

    aput-object p1, v2, v0

    invoke-static {v2}, Le2/u0;->j([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Landroidx/media3/exoplayer/source/p$e;

    iput-object p1, p0, Landroidx/media3/exoplayer/source/p;->v:[Landroidx/media3/exoplayer/source/p$e;

    iget-object p1, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    invoke-static {p1, v3}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Landroidx/media3/exoplayer/source/s;

    aput-object v1, p1, v0

    invoke-static {p1}, Le2/u0;->j([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Landroidx/media3/exoplayer/source/s;

    iput-object p1, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    return-object v1
.end method

.method public T(ILandroidx/media3/exoplayer/t1;Landroidx/media3/decoder/DecoderInputBuffer;I)I
    .locals 3

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->Z()Z

    move-result v0

    const/4 v1, -0x3

    if-eqz v0, :cond_0

    return v1

    :cond_0
    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/p;->K(I)V

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    aget-object v0, v0, p1

    iget-boolean v2, p0, Landroidx/media3/exoplayer/source/p;->M:Z

    invoke-virtual {v0, p2, p3, p4, v2}, Landroidx/media3/exoplayer/source/s;->T(Landroidx/media3/exoplayer/t1;Landroidx/media3/decoder/DecoderInputBuffer;IZ)I

    move-result p2

    if-ne p2, v1, :cond_1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/p;->L(I)V

    :cond_1
    return p2
.end method

.method public U()V
    .locals 4

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->x:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length v1, v0

    const/4 v2, 0x1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, v0, v2

    invoke-virtual {v3}, Landroidx/media3/exoplayer/source/s;->S()V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->l:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0, p0}, Landroidx/media3/exoplayer/upstream/Loader;->l(Landroidx/media3/exoplayer/upstream/Loader$e;)V

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->q:Landroid/os/Handler;

    const/4 v1, 0x1

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacksAndMessages(Ljava/lang/Object;)V

    iput-object v1, p0, Landroidx/media3/exoplayer/source/p;->s:Landroidx/media3/exoplayer/source/k$a;

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->N:Z

    return-void
.end method

.method public final V([ZJ)Z
    .locals 5

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length v0, v0

    const/4 v1, 0x1

    const/4 v1, 0x0

    const/4 v2, 0x1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_3

    iget-object v3, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    aget-object v3, v3, v2

    iget-boolean v4, p0, Landroidx/media3/exoplayer/source/p;->r:Z

    if-eqz v4, :cond_0

    invoke-virtual {v3}, Landroidx/media3/exoplayer/source/s;->y()I

    move-result v4

    invoke-virtual {v3, v4}, Landroidx/media3/exoplayer/source/s;->Z(I)Z

    move-result v3

    goto :goto_1

    :cond_0
    invoke-virtual {v3, p2, p3, v1}, Landroidx/media3/exoplayer/source/s;->a0(JZ)Z

    move-result v3

    :goto_1
    if-nez v3, :cond_2

    aget-boolean v3, p1, v2

    if-nez v3, :cond_1

    iget-boolean v3, p0, Landroidx/media3/exoplayer/source/p;->y:Z

    if-nez v3, :cond_2

    :cond_1
    return v1

    :cond_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_3
    const/4 p1, 0x1

    return p1
.end method

.method public final W(Lz2/m0;)V
    .locals 6

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->t:Landroidx/media3/extractor/metadata/icy/IcyHeaders;

    const-wide v1, -0x7fffffffffffffffL    # -4.9E-324

    if-nez v0, :cond_0

    move-object v0, p1

    goto :goto_0

    :cond_0
    new-instance v0, Lz2/m0$b;

    invoke-direct {v0, v1, v2}, Lz2/m0$b;-><init>(J)V

    :goto_0
    iput-object v0, p0, Landroidx/media3/exoplayer/source/p;->A:Lz2/m0;

    invoke-interface {p1}, Lz2/m0;->getDurationUs()J

    move-result-wide v3

    cmp-long v0, v3, v1

    if-nez v0, :cond_1

    iget-wide v3, p0, Landroidx/media3/exoplayer/source/p;->B:J

    cmp-long v0, v3, v1

    if-eqz v0, :cond_1

    new-instance v0, Landroidx/media3/exoplayer/source/p$a;

    iget-object v3, p0, Landroidx/media3/exoplayer/source/p;->A:Lz2/m0;

    invoke-direct {v0, p0, v3}, Landroidx/media3/exoplayer/source/p$a;-><init>(Landroidx/media3/exoplayer/source/p;Lz2/m0;)V

    iput-object v0, p0, Landroidx/media3/exoplayer/source/p;->A:Lz2/m0;

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->A:Lz2/m0;

    invoke-interface {v0}, Lz2/m0;->getDurationUs()J

    move-result-wide v3

    iput-wide v3, p0, Landroidx/media3/exoplayer/source/p;->B:J

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->H:Z

    const/4 v3, 0x1

    if-nez v0, :cond_2

    invoke-interface {p1}, Lz2/m0;->getDurationUs()J

    move-result-wide v4

    cmp-long v0, v4, v1

    if-nez v0, :cond_2

    const/4 v0, 0x1

    goto :goto_1

    :cond_2
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_1
    iput-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->C:Z

    if-eqz v0, :cond_3

    const/4 v3, 0x7

    :cond_3
    iput v3, p0, Landroidx/media3/exoplayer/source/p;->D:I

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->h:Landroidx/media3/exoplayer/source/p$c;

    iget-wide v1, p0, Landroidx/media3/exoplayer/source/p;->B:J

    invoke-interface {p1}, Lz2/m0;->isSeekable()Z

    move-result p1

    iget-boolean v3, p0, Landroidx/media3/exoplayer/source/p;->C:Z

    invoke-interface {v0, v1, v2, p1, v3}, Landroidx/media3/exoplayer/source/p$c;->b(JZZ)V

    iget-boolean p1, p0, Landroidx/media3/exoplayer/source/p;->x:Z

    if-nez p1, :cond_4

    invoke-direct {p0}, Landroidx/media3/exoplayer/source/p;->J()V

    :cond_4
    return-void
.end method

.method public X(IJ)I
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->Z()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 p1, 0x1

    const/4 p1, 0x0

    return p1

    :cond_0
    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/p;->K(I)V

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    aget-object v0, v0, p1

    iget-boolean v1, p0, Landroidx/media3/exoplayer/source/p;->M:Z

    invoke-virtual {v0, p2, p3, v1}, Landroidx/media3/exoplayer/source/s;->F(JZ)I

    move-result p2

    invoke-virtual {v0, p2}, Landroidx/media3/exoplayer/source/s;->f0(I)V

    if-nez p2, :cond_1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/source/p;->L(I)V

    :cond_1
    return p2
.end method

.method public final Y()V
    .locals 26

    move-object/from16 v7, p0

    new-instance v8, Landroidx/media3/exoplayer/source/p$b;

    iget-object v2, v7, Landroidx/media3/exoplayer/source/p;->a:Landroid/net/Uri;

    iget-object v3, v7, Landroidx/media3/exoplayer/source/p;->b:Landroidx/media3/datasource/a;

    iget-object v4, v7, Landroidx/media3/exoplayer/source/p;->m:Landroidx/media3/exoplayer/source/o;

    iget-object v6, v7, Landroidx/media3/exoplayer/source/p;->n:Le2/g;

    move-object v0, v8

    move-object/from16 v1, p0

    move-object/from16 v5, p0

    invoke-direct/range {v0 .. v6}, Landroidx/media3/exoplayer/source/p$b;-><init>(Landroidx/media3/exoplayer/source/p;Landroid/net/Uri;Landroidx/media3/datasource/a;Landroidx/media3/exoplayer/source/o;Lz2/u;Le2/g;)V

    iget-boolean v0, v7, Landroidx/media3/exoplayer/source/p;->x:Z

    if-eqz v0, :cond_2

    invoke-direct/range {p0 .. p0}, Landroidx/media3/exoplayer/source/p;->E()Z

    move-result v0

    invoke-static {v0}, Le2/a;->g(Z)V

    iget-wide v0, v7, Landroidx/media3/exoplayer/source/p;->B:J

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v4, v0, v2

    if-eqz v4, :cond_0

    iget-wide v4, v7, Landroidx/media3/exoplayer/source/p;->J:J

    cmp-long v6, v4, v0

    if-lez v6, :cond_0

    const/4 v0, 0x1

    iput-boolean v0, v7, Landroidx/media3/exoplayer/source/p;->M:Z

    iput-wide v2, v7, Landroidx/media3/exoplayer/source/p;->J:J

    return-void

    :cond_0
    iget-object v0, v7, Landroidx/media3/exoplayer/source/p;->A:Lz2/m0;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lz2/m0;

    iget-wide v4, v7, Landroidx/media3/exoplayer/source/p;->J:J

    invoke-interface {v0, v4, v5}, Lz2/m0;->getSeekPoints(J)Lz2/m0$a;

    move-result-object v0

    iget-object v0, v0, Lz2/m0$a;->a:Lz2/n0;

    iget-wide v0, v0, Lz2/n0;->b:J

    iget-wide v4, v7, Landroidx/media3/exoplayer/source/p;->J:J

    invoke-static {v8, v0, v1, v4, v5}, Landroidx/media3/exoplayer/source/p$b;->f(Landroidx/media3/exoplayer/source/p$b;JJ)V

    iget-object v0, v7, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length v1, v0

    const/4 v4, 0x1

    const/4 v4, 0x0

    :goto_0
    if-ge v4, v1, :cond_1

    aget-object v5, v0, v4

    iget-wide v9, v7, Landroidx/media3/exoplayer/source/p;->J:J

    invoke-virtual {v5, v9, v10}, Landroidx/media3/exoplayer/source/s;->c0(J)V

    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    :cond_1
    iput-wide v2, v7, Landroidx/media3/exoplayer/source/p;->J:J

    :cond_2
    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/source/p;->B()I

    move-result v0

    iput v0, v7, Landroidx/media3/exoplayer/source/p;->L:I

    iget-object v0, v7, Landroidx/media3/exoplayer/source/p;->l:Landroidx/media3/exoplayer/upstream/Loader;

    iget-object v1, v7, Landroidx/media3/exoplayer/source/p;->d:Landroidx/media3/exoplayer/upstream/m;

    iget v2, v7, Landroidx/media3/exoplayer/source/p;->D:I

    invoke-interface {v1, v2}, Landroidx/media3/exoplayer/upstream/m;->a(I)I

    move-result v1

    invoke-virtual {v0, v8, v7, v1}, Landroidx/media3/exoplayer/upstream/Loader;->m(Landroidx/media3/exoplayer/upstream/Loader$d;Landroidx/media3/exoplayer/upstream/Loader$b;I)J

    move-result-wide v13

    invoke-static {v8}, Landroidx/media3/exoplayer/source/p$b;->d(Landroidx/media3/exoplayer/source/p$b;)Lh2/g;

    move-result-object v12

    iget-object v15, v7, Landroidx/media3/exoplayer/source/p;->f:Landroidx/media3/exoplayer/source/m$a;

    new-instance v16, Lu2/n;

    invoke-static {v8}, Landroidx/media3/exoplayer/source/p$b;->c(Landroidx/media3/exoplayer/source/p$b;)J

    move-result-wide v10

    move-object/from16 v9, v16

    invoke-direct/range {v9 .. v14}, Lu2/n;-><init>(JLh2/g;J)V

    const/16 v17, 0x1

    const/16 v18, -0x1

    const/16 v19, 0x0

    const/16 v20, 0x0

    const/16 v21, 0x0

    invoke-static {v8}, Landroidx/media3/exoplayer/source/p$b;->e(Landroidx/media3/exoplayer/source/p$b;)J

    move-result-wide v22

    iget-wide v0, v7, Landroidx/media3/exoplayer/source/p;->B:J

    move-wide/from16 v24, v0

    invoke-virtual/range {v15 .. v25}, Landroidx/media3/exoplayer/source/m$a;->z(Lu2/n;IILandroidx/media3/common/y;ILjava/lang/Object;JJ)V

    return-void
.end method

.method public final Z()Z
    .locals 1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->F:Z

    if-nez v0, :cond_1

    invoke-direct {p0}, Landroidx/media3/exoplayer/source/p;->E()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method

.method public a(Landroidx/media3/exoplayer/w1;)Z
    .locals 1

    iget-boolean p1, p0, Landroidx/media3/exoplayer/source/p;->M:Z

    if-nez p1, :cond_2

    iget-object p1, p0, Landroidx/media3/exoplayer/source/p;->l:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/upstream/Loader;->h()Z

    move-result p1

    if-nez p1, :cond_2

    iget-boolean p1, p0, Landroidx/media3/exoplayer/source/p;->K:Z

    if-nez p1, :cond_2

    iget-boolean p1, p0, Landroidx/media3/exoplayer/source/p;->x:Z

    if-eqz p1, :cond_0

    iget p1, p0, Landroidx/media3/exoplayer/source/p;->G:I

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    iget-object p1, p0, Landroidx/media3/exoplayer/source/p;->n:Le2/g;

    invoke-virtual {p1}, Le2/g;->f()Z

    move-result p1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->l:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/Loader;->i()Z

    move-result v0

    if-nez v0, :cond_1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->Y()V

    const/4 p1, 0x1

    :cond_1
    return p1

    :cond_2
    :goto_0
    const/4 p1, 0x1

    const/4 p1, 0x0

    return p1
.end method

.method public b(JLandroidx/media3/exoplayer/b3;)J
    .locals 9

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->y()V

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->A:Lz2/m0;

    invoke-interface {v0}, Lz2/m0;->isSeekable()Z

    move-result v0

    if-nez v0, :cond_0

    const-wide/16 p1, 0x0

    return-wide p1

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->A:Lz2/m0;

    invoke-interface {v0, p1, p2}, Lz2/m0;->getSeekPoints(J)Lz2/m0$a;

    move-result-object v0

    iget-object v1, v0, Lz2/m0$a;->a:Lz2/n0;

    iget-wide v5, v1, Lz2/n0;->a:J

    iget-object v0, v0, Lz2/m0$a;->b:Lz2/n0;

    iget-wide v7, v0, Lz2/n0;->a:J

    move-object v2, p3

    move-wide v3, p1

    invoke-virtual/range {v2 .. v8}, Landroidx/media3/exoplayer/b3;->a(JJJ)J

    move-result-wide p1

    return-wide p1
.end method

.method public c(Landroidx/media3/common/y;)V
    .locals 1

    iget-object p1, p0, Landroidx/media3/exoplayer/source/p;->q:Landroid/os/Handler;

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->o:Ljava/lang/Runnable;

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    return-void
.end method

.method public discardBuffer(JZ)V
    .locals 5

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->r:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->y()V

    invoke-direct {p0}, Landroidx/media3/exoplayer/source/p;->E()Z

    move-result v0

    if-eqz v0, :cond_1

    return-void

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->z:Landroidx/media3/exoplayer/source/p$f;

    iget-object v0, v0, Landroidx/media3/exoplayer/source/p$f;->c:[Z

    iget-object v1, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length v1, v1

    const/4 v2, 0x1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_2

    iget-object v3, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    aget-object v3, v3, v2

    aget-boolean v4, v0, v2

    invoke-virtual {v3, p1, p2, p3, v4}, Landroidx/media3/exoplayer/source/s;->q(JZZ)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_2
    return-void
.end method

.method public e(Landroidx/media3/exoplayer/source/k$a;J)V
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/source/p;->s:Landroidx/media3/exoplayer/source/k$a;

    iget-object p1, p0, Landroidx/media3/exoplayer/source/p;->n:Le2/g;

    invoke-virtual {p1}, Le2/g;->f()Z

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->Y()V

    return-void
.end method

.method public endTracks()V
    .locals 2

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->w:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->q:Landroid/os/Handler;

    iget-object v1, p0, Landroidx/media3/exoplayer/source/p;->o:Ljava/lang/Runnable;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    return-void
.end method

.method public f([Lx2/z;[Z[Lu2/e0;[ZJ)J
    .locals 8

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->y()V

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->z:Landroidx/media3/exoplayer/source/p$f;

    iget-object v1, v0, Landroidx/media3/exoplayer/source/p$f;->a:Lu2/k0;

    iget-object v0, v0, Landroidx/media3/exoplayer/source/p$f;->c:[Z

    iget v2, p0, Landroidx/media3/exoplayer/source/p;->G:I

    const/4 v3, 0x1

    const/4 v3, 0x0

    const/4 v4, 0x1

    const/4 v4, 0x0

    :goto_0
    array-length v5, p1

    const/4 v6, 0x1

    if-ge v4, v5, :cond_2

    aget-object v5, p3, v4

    if-eqz v5, :cond_1

    aget-object v7, p1, v4

    if-eqz v7, :cond_0

    aget-boolean v7, p2, v4

    if-nez v7, :cond_1

    :cond_0
    check-cast v5, Landroidx/media3/exoplayer/source/p$d;

    invoke-static {v5}, Landroidx/media3/exoplayer/source/p$d;->a(Landroidx/media3/exoplayer/source/p$d;)I

    move-result v5

    aget-boolean v7, v0, v5

    invoke-static {v7}, Le2/a;->g(Z)V

    iget v7, p0, Landroidx/media3/exoplayer/source/p;->G:I

    sub-int/2addr v7, v6

    iput v7, p0, Landroidx/media3/exoplayer/source/p;->G:I

    aput-boolean v3, v0, v5

    const/4 v5, 0x1

    const/4 v5, 0x0

    aput-object v5, p3, v4

    :cond_1
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    :cond_2
    iget-boolean p2, p0, Landroidx/media3/exoplayer/source/p;->r:Z

    if-nez p2, :cond_4

    iget-boolean p2, p0, Landroidx/media3/exoplayer/source/p;->E:Z

    if-eqz p2, :cond_3

    if-nez v2, :cond_4

    goto :goto_1

    :cond_3
    const-wide/16 v4, 0x0

    cmp-long p2, p5, v4

    if-eqz p2, :cond_4

    :goto_1
    const/4 p2, 0x1

    goto :goto_2

    :cond_4
    const/4 p2, 0x1

    const/4 p2, 0x0

    :goto_2
    const/4 v2, 0x1

    const/4 v2, 0x0

    :goto_3
    array-length v4, p1

    if-ge v2, v4, :cond_9

    aget-object v4, p3, v2

    if-nez v4, :cond_8

    aget-object v4, p1, v2

    if-eqz v4, :cond_8

    invoke-interface {v4}, Lx2/c0;->length()I

    move-result v5

    if-ne v5, v6, :cond_5

    const/4 v5, 0x1

    goto :goto_4

    :cond_5
    const/4 v5, 0x1

    const/4 v5, 0x0

    :goto_4
    invoke-static {v5}, Le2/a;->g(Z)V

    invoke-interface {v4, v3}, Lx2/c0;->getIndexInTrackGroup(I)I

    move-result v5

    if-nez v5, :cond_6

    const/4 v5, 0x1

    goto :goto_5

    :cond_6
    const/4 v5, 0x1

    const/4 v5, 0x0

    :goto_5
    invoke-static {v5}, Le2/a;->g(Z)V

    invoke-interface {v4}, Lx2/c0;->getTrackGroup()Landroidx/media3/common/n0;

    move-result-object v4

    invoke-virtual {v1, v4}, Lu2/k0;->d(Landroidx/media3/common/n0;)I

    move-result v4

    aget-boolean v5, v0, v4

    xor-int/2addr v5, v6

    invoke-static {v5}, Le2/a;->g(Z)V

    iget v5, p0, Landroidx/media3/exoplayer/source/p;->G:I

    add-int/2addr v5, v6

    iput v5, p0, Landroidx/media3/exoplayer/source/p;->G:I

    aput-boolean v6, v0, v4

    new-instance v5, Landroidx/media3/exoplayer/source/p$d;

    invoke-direct {v5, p0, v4}, Landroidx/media3/exoplayer/source/p$d;-><init>(Landroidx/media3/exoplayer/source/p;I)V

    aput-object v5, p3, v2

    aput-boolean v6, p4, v2

    if-nez p2, :cond_8

    iget-object p2, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    aget-object p2, p2, v4

    invoke-virtual {p2}, Landroidx/media3/exoplayer/source/s;->D()I

    move-result v4

    if-eqz v4, :cond_7

    invoke-virtual {p2, p5, p6, v6}, Landroidx/media3/exoplayer/source/s;->a0(JZ)Z

    move-result p2

    if-nez p2, :cond_7

    const/4 p2, 0x1

    goto :goto_6

    :cond_7
    const/4 p2, 0x1

    const/4 p2, 0x0

    :cond_8
    :goto_6
    add-int/lit8 v2, v2, 0x1

    goto :goto_3

    :cond_9
    iget p1, p0, Landroidx/media3/exoplayer/source/p;->G:I

    if-nez p1, :cond_c

    iput-boolean v3, p0, Landroidx/media3/exoplayer/source/p;->K:Z

    iput-boolean v3, p0, Landroidx/media3/exoplayer/source/p;->F:Z

    iget-object p1, p0, Landroidx/media3/exoplayer/source/p;->l:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/upstream/Loader;->i()Z

    move-result p1

    if-eqz p1, :cond_b

    iget-object p1, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length p2, p1

    :goto_7
    if-ge v3, p2, :cond_a

    aget-object p3, p1, v3

    invoke-virtual {p3}, Landroidx/media3/exoplayer/source/s;->r()V

    add-int/lit8 v3, v3, 0x1

    goto :goto_7

    :cond_a
    iget-object p1, p0, Landroidx/media3/exoplayer/source/p;->l:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/upstream/Loader;->e()V

    goto :goto_a

    :cond_b
    iget-object p1, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length p2, p1

    :goto_8
    if-ge v3, p2, :cond_e

    aget-object p3, p1, v3

    invoke-virtual {p3}, Landroidx/media3/exoplayer/source/s;->W()V

    add-int/lit8 v3, v3, 0x1

    goto :goto_8

    :cond_c
    if-eqz p2, :cond_e

    invoke-virtual {p0, p5, p6}, Landroidx/media3/exoplayer/source/p;->seekToUs(J)J

    move-result-wide p5

    :goto_9
    array-length p1, p3

    if-ge v3, p1, :cond_e

    aget-object p1, p3, v3

    if-eqz p1, :cond_d

    aput-boolean v6, p4, v3

    :cond_d
    add-int/lit8 v3, v3, 0x1

    goto :goto_9

    :cond_e
    :goto_a
    iput-boolean v6, p0, Landroidx/media3/exoplayer/source/p;->E:Z

    return-wide p5
.end method

.method public g(Lz2/m0;)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->q:Landroid/os/Handler;

    new-instance v1, Lu2/b0;

    invoke-direct {v1, p0, p1}, Lu2/b0;-><init>(Landroidx/media3/exoplayer/source/p;Lz2/m0;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    return-void
.end method

.method public getBufferedPositionUs()J
    .locals 11

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->y()V

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->M:Z

    const-wide/high16 v1, -0x8000000000000000L

    if-nez v0, :cond_7

    iget v0, p0, Landroidx/media3/exoplayer/source/p;->G:I

    if-nez v0, :cond_0

    goto :goto_1

    :cond_0
    invoke-direct {p0}, Landroidx/media3/exoplayer/source/p;->E()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-wide v0, p0, Landroidx/media3/exoplayer/source/p;->J:J

    return-wide v0

    :cond_1
    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->y:Z

    const/4 v3, 0x1

    const/4 v3, 0x0

    const-wide v4, 0x7fffffffffffffffL

    if-eqz v0, :cond_3

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length v0, v0

    move-wide v7, v4

    const/4 v6, 0x1

    const/4 v6, 0x0

    :goto_0
    if-ge v6, v0, :cond_4

    iget-object v9, p0, Landroidx/media3/exoplayer/source/p;->z:Landroidx/media3/exoplayer/source/p$f;

    iget-object v10, v9, Landroidx/media3/exoplayer/source/p$f;->b:[Z

    aget-boolean v10, v10, v6

    if-eqz v10, :cond_2

    iget-object v9, v9, Landroidx/media3/exoplayer/source/p$f;->c:[Z

    aget-boolean v9, v9, v6

    if-eqz v9, :cond_2

    iget-object v9, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    aget-object v9, v9, v6

    invoke-virtual {v9}, Landroidx/media3/exoplayer/source/s;->K()Z

    move-result v9

    if-nez v9, :cond_2

    iget-object v9, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    aget-object v9, v9, v6

    invoke-virtual {v9}, Landroidx/media3/exoplayer/source/s;->A()J

    move-result-wide v9

    invoke-static {v7, v8, v9, v10}, Ljava/lang/Math;->min(JJ)J

    move-result-wide v7

    :cond_2
    add-int/lit8 v6, v6, 0x1

    goto :goto_0

    :cond_3
    move-wide v7, v4

    :cond_4
    cmp-long v0, v7, v4

    if-nez v0, :cond_5

    invoke-virtual {p0, v3}, Landroidx/media3/exoplayer/source/p;->C(Z)J

    move-result-wide v7

    :cond_5
    cmp-long v0, v7, v1

    if-nez v0, :cond_6

    iget-wide v7, p0, Landroidx/media3/exoplayer/source/p;->I:J

    :cond_6
    return-wide v7

    :cond_7
    :goto_1
    return-wide v1
.end method

.method public getNextLoadPositionUs()J
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->getBufferedPositionUs()J

    move-result-wide v0

    return-wide v0
.end method

.method public getTrackGroups()Lu2/k0;
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->y()V

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->z:Landroidx/media3/exoplayer/source/p$f;

    iget-object v0, v0, Landroidx/media3/exoplayer/source/p$f;->a:Lu2/k0;

    return-object v0
.end method

.method public isLoading()Z
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->l:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/Loader;->i()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->n:Le2/g;

    invoke-virtual {v0}, Le2/g;->e()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public bridge synthetic k(Landroidx/media3/exoplayer/upstream/Loader$d;JJLjava/io/IOException;I)Landroidx/media3/exoplayer/upstream/Loader$c;
    .locals 0

    check-cast p1, Landroidx/media3/exoplayer/source/p$b;

    invoke-virtual/range {p0 .. p7}, Landroidx/media3/exoplayer/source/p;->R(Landroidx/media3/exoplayer/source/p$b;JJLjava/io/IOException;I)Landroidx/media3/exoplayer/upstream/Loader$c;

    move-result-object p1

    return-object p1
.end method

.method public maybeThrowPrepareError()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->M()V

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->M:Z

    if-eqz v0, :cond_1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->x:Z

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const-string v0, "Loading finished before preparation is complete."

    const/4 v1, 0x1

    const/4 v1, 0x0

    invoke-static {v0, v1}, Landroidx/media3/common/ParserException;->createForMalformedContainer(Ljava/lang/String;Ljava/lang/Throwable;)Landroidx/media3/common/ParserException;

    move-result-object v0

    throw v0

    :cond_1
    :goto_0
    return-void
.end method

.method public bridge synthetic n(Landroidx/media3/exoplayer/upstream/Loader$d;JJ)V
    .locals 0

    check-cast p1, Landroidx/media3/exoplayer/source/p$b;

    invoke-virtual/range {p0 .. p5}, Landroidx/media3/exoplayer/source/p;->Q(Landroidx/media3/exoplayer/source/p$b;JJ)V

    return-void
.end method

.method public bridge synthetic o(Landroidx/media3/exoplayer/upstream/Loader$d;JJZ)V
    .locals 0

    check-cast p1, Landroidx/media3/exoplayer/source/p$b;

    invoke-virtual/range {p0 .. p6}, Landroidx/media3/exoplayer/source/p;->P(Landroidx/media3/exoplayer/source/p$b;JJZ)V

    return-void
.end method

.method public onLoaderReleased()V
    .locals 4

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length v1, v0

    const/4 v2, 0x1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, v0, v2

    invoke-virtual {v3}, Landroidx/media3/exoplayer/source/s;->U()V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->m:Landroidx/media3/exoplayer/source/o;

    invoke-interface {v0}, Landroidx/media3/exoplayer/source/o;->release()V

    return-void
.end method

.method public readDiscontinuity()J
    .locals 2

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->F:Z

    if-eqz v0, :cond_1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->M:Z

    if-nez v0, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->B()I

    move-result v0

    iget v1, p0, Landroidx/media3/exoplayer/source/p;->L:I

    if-le v0, v1, :cond_1

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    iput-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->F:Z

    iget-wide v0, p0, Landroidx/media3/exoplayer/source/p;->I:J

    return-wide v0

    :cond_1
    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    return-wide v0
.end method

.method public reevaluateBuffer(J)V
    .locals 0

    return-void
.end method

.method public seekToUs(J)J
    .locals 4

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->y()V

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->z:Landroidx/media3/exoplayer/source/p$f;

    iget-object v0, v0, Landroidx/media3/exoplayer/source/p$f;->b:[Z

    iget-object v1, p0, Landroidx/media3/exoplayer/source/p;->A:Lz2/m0;

    invoke-interface {v1}, Lz2/m0;->isSeekable()Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    const-wide/16 p1, 0x0

    :goto_0
    const/4 v1, 0x1

    const/4 v1, 0x0

    iput-boolean v1, p0, Landroidx/media3/exoplayer/source/p;->F:Z

    iput-wide p1, p0, Landroidx/media3/exoplayer/source/p;->I:J

    invoke-direct {p0}, Landroidx/media3/exoplayer/source/p;->E()Z

    move-result v2

    if-eqz v2, :cond_1

    iput-wide p1, p0, Landroidx/media3/exoplayer/source/p;->J:J

    return-wide p1

    :cond_1
    iget v2, p0, Landroidx/media3/exoplayer/source/p;->D:I

    const/4 v3, 0x7

    if-eq v2, v3, :cond_2

    invoke-virtual {p0, v0, p1, p2}, Landroidx/media3/exoplayer/source/p;->V([ZJ)Z

    move-result v0

    if-eqz v0, :cond_2

    return-wide p1

    :cond_2
    iput-boolean v1, p0, Landroidx/media3/exoplayer/source/p;->K:Z

    iput-wide p1, p0, Landroidx/media3/exoplayer/source/p;->J:J

    iput-boolean v1, p0, Landroidx/media3/exoplayer/source/p;->M:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->l:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/Loader;->i()Z

    move-result v0

    if-eqz v0, :cond_4

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length v2, v0

    :goto_1
    if-ge v1, v2, :cond_3

    aget-object v3, v0, v1

    invoke-virtual {v3}, Landroidx/media3/exoplayer/source/s;->r()V

    add-int/lit8 v1, v1, 0x1

    goto :goto_1

    :cond_3
    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->l:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/Loader;->e()V

    goto :goto_3

    :cond_4
    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->l:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/Loader;->f()V

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length v2, v0

    :goto_2
    if-ge v1, v2, :cond_5

    aget-object v3, v0, v1

    invoke-virtual {v3}, Landroidx/media3/exoplayer/source/s;->W()V

    add-int/lit8 v1, v1, 0x1

    goto :goto_2

    :cond_5
    :goto_3
    return-wide p1
.end method

.method public track(II)Lz2/r0;
    .locals 1

    new-instance p2, Landroidx/media3/exoplayer/source/p$e;

    const/4 v0, 0x1

    const/4 v0, 0x0

    invoke-direct {p2, p1, v0}, Landroidx/media3/exoplayer/source/p$e;-><init>(IZ)V

    invoke-virtual {p0, p2}, Landroidx/media3/exoplayer/source/p;->S(Landroidx/media3/exoplayer/source/p$e;)Lz2/r0;

    move-result-object p1

    return-object p1
.end method

.method public final y()V
    .locals 1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->x:Z

    invoke-static {v0}, Le2/a;->g(Z)V

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->z:Landroidx/media3/exoplayer/source/p$f;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->A:Lz2/m0;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method public final z(Landroidx/media3/exoplayer/source/p$b;I)Z
    .locals 6

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p;->H:Z

    const/4 v1, 0x1

    if-nez v0, :cond_3

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p;->A:Lz2/m0;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lz2/m0;->getDurationUs()J

    move-result-wide v2

    const-wide v4, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v0, v2, v4

    if-eqz v0, :cond_0

    goto :goto_1

    :cond_0
    iget-boolean p2, p0, Landroidx/media3/exoplayer/source/p;->x:Z

    const/4 v0, 0x1

    const/4 v0, 0x0

    if-eqz p2, :cond_1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/p;->Z()Z

    move-result p2

    if-nez p2, :cond_1

    iput-boolean v1, p0, Landroidx/media3/exoplayer/source/p;->K:Z

    return v0

    :cond_1
    iget-boolean p2, p0, Landroidx/media3/exoplayer/source/p;->x:Z

    iput-boolean p2, p0, Landroidx/media3/exoplayer/source/p;->F:Z

    const-wide/16 v2, 0x0

    iput-wide v2, p0, Landroidx/media3/exoplayer/source/p;->I:J

    iput v0, p0, Landroidx/media3/exoplayer/source/p;->L:I

    iget-object p2, p0, Landroidx/media3/exoplayer/source/p;->u:[Landroidx/media3/exoplayer/source/s;

    array-length v4, p2

    :goto_0
    if-ge v0, v4, :cond_2

    aget-object v5, p2, v0

    invoke-virtual {v5}, Landroidx/media3/exoplayer/source/s;->W()V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_2
    invoke-static {p1, v2, v3, v2, v3}, Landroidx/media3/exoplayer/source/p$b;->f(Landroidx/media3/exoplayer/source/p$b;JJ)V

    return v1

    :cond_3
    :goto_1
    iput p2, p0, Landroidx/media3/exoplayer/source/p;->L:I

    return v1
.end method
