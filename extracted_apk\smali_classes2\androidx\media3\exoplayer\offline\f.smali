.class public interface abstract Landroidx/media3/exoplayer/offline/f;
.super Ljava/lang/Object;


# virtual methods
.method public varargs abstract e([I)Landroidx/media3/exoplayer/offline/e;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract h(Ljava/lang/String;)Landroidx/media3/exoplayer/offline/c;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method
