.class public final Landroidx/media3/datasource/cronet/CronetDataSource$c;
.super Lorg/chromium/net/UrlRequest$Callback;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/datasource/cronet/CronetDataSource;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "c"
.end annotation


# instance fields
.field public final synthetic f:Landroidx/media3/datasource/cronet/CronetDataSource;


# direct methods
.method public constructor <init>(Landroidx/media3/datasource/cronet/CronetDataSource;)V
    .locals 0

    iput-object p1, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-direct {p0}, Lorg/chromium/net/UrlRequest$Callback;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Landroidx/media3/datasource/cronet/CronetDataSource;Landroidx/media3/datasource/cronet/CronetDataSource$a;)V
    .locals 0

    invoke-direct {p0, p1}, Landroidx/media3/datasource/cronet/CronetDataSource$c;-><init>(Landroidx/media3/datasource/cronet/CronetDataSource;)V

    return-void
.end method


# virtual methods
.method public declared-synchronized onFailed(Lorg/chromium/net/UrlRequest;Lorg/chromium/net/UrlResponseInfo;Lorg/chromium/net/CronetException;)V
    .locals 0

    monitor-enter p0

    :try_start_0
    iget-object p2, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {p2}, Landroidx/media3/datasource/cronet/CronetDataSource;->h(Landroidx/media3/datasource/cronet/CronetDataSource;)Lorg/chromium/net/UrlRequest;

    move-result-object p2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eq p1, p2, :cond_0

    monitor-exit p0

    return-void

    :cond_0
    :try_start_1
    instance-of p1, p3, Lorg/chromium/net/NetworkException;

    if-eqz p1, :cond_1

    move-object p1, p3

    check-cast p1, Lorg/chromium/net/NetworkException;

    invoke-virtual {p1}, Lorg/chromium/net/NetworkException;->getErrorCode()I

    move-result p1

    const/4 p2, 0x1

    if-ne p1, p2, :cond_1

    iget-object p1, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    new-instance p2, Ljava/net/UnknownHostException;

    invoke-direct {p2}, Ljava/net/UnknownHostException;-><init>()V

    invoke-static {p1, p2}, Landroidx/media3/datasource/cronet/CronetDataSource;->n(Landroidx/media3/datasource/cronet/CronetDataSource;Ljava/io/IOException;)Ljava/io/IOException;

    goto :goto_0

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_1
    iget-object p1, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {p1, p3}, Landroidx/media3/datasource/cronet/CronetDataSource;->n(Landroidx/media3/datasource/cronet/CronetDataSource;Ljava/io/IOException;)Ljava/io/IOException;

    :goto_0
    iget-object p1, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {p1}, Landroidx/media3/datasource/cronet/CronetDataSource;->o(Landroidx/media3/datasource/cronet/CronetDataSource;)Le2/g;

    move-result-object p1

    invoke-virtual {p1}, Le2/g;->f()Z
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return-void

    :goto_1
    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized onReadCompleted(Lorg/chromium/net/UrlRequest;Lorg/chromium/net/UrlResponseInfo;Ljava/nio/ByteBuffer;)V
    .locals 0

    monitor-enter p0

    :try_start_0
    iget-object p2, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {p2}, Landroidx/media3/datasource/cronet/CronetDataSource;->h(Landroidx/media3/datasource/cronet/CronetDataSource;)Lorg/chromium/net/UrlRequest;

    move-result-object p2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eq p1, p2, :cond_0

    monitor-exit p0

    return-void

    :cond_0
    :try_start_1
    iget-object p1, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {p1}, Landroidx/media3/datasource/cronet/CronetDataSource;->o(Landroidx/media3/datasource/cronet/CronetDataSource;)Le2/g;

    move-result-object p1

    invoke-virtual {p1}, Le2/g;->f()Z
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized onRedirectReceived(Lorg/chromium/net/UrlRequest;Lorg/chromium/net/UrlResponseInfo;Ljava/lang/String;)V
    .locals 9

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {v0}, Landroidx/media3/datasource/cronet/CronetDataSource;->h(Landroidx/media3/datasource/cronet/CronetDataSource;)Lorg/chromium/net/UrlRequest;

    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eq p1, v0, :cond_0

    monitor-exit p0

    return-void

    :cond_0
    :try_start_1
    iget-object v0, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {v0}, Landroidx/media3/datasource/cronet/CronetDataSource;->h(Landroidx/media3/datasource/cronet/CronetDataSource;)Lorg/chromium/net/UrlRequest;

    move-result-object v0

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lorg/chromium/net/UrlRequest;

    iget-object v1, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {v1}, Landroidx/media3/datasource/cronet/CronetDataSource;->m(Landroidx/media3/datasource/cronet/CronetDataSource;)Lh2/g;

    move-result-object v1

    invoke-static {v1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    move-object v7, v1

    check-cast v7, Lh2/g;

    invoke-virtual {p2}, Lorg/chromium/net/UrlResponseInfo;->getHttpStatusCode()I

    move-result v3

    iget v1, v7, Lh2/g;->c:I

    const/4 v2, 0x2

    if-ne v1, v2, :cond_2

    const/16 v1, 0x133

    if-eq v3, v1, :cond_1

    const/16 v1, 0x134

    if-ne v3, v1, :cond_2

    :cond_1
    iget-object p1, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    new-instance p3, Landroidx/media3/datasource/HttpDataSource$InvalidResponseCodeException;

    invoke-virtual {p2}, Lorg/chromium/net/UrlResponseInfo;->getHttpStatusText()Ljava/lang/String;

    move-result-object v4

    const/4 v5, 0x1

    const/4 v5, 0x0

    invoke-virtual {p2}, Lorg/chromium/net/UrlResponseInfo;->getAllHeaders()Ljava/util/Map;

    move-result-object v6

    sget-object v8, Le2/u0;->f:[B

    move-object v2, p3

    invoke-direct/range {v2 .. v8}, Landroidx/media3/datasource/HttpDataSource$InvalidResponseCodeException;-><init>(ILjava/lang/String;Ljava/io/IOException;Ljava/util/Map;Lh2/g;[B)V

    invoke-static {p1, p3}, Landroidx/media3/datasource/cronet/CronetDataSource;->n(Landroidx/media3/datasource/cronet/CronetDataSource;Ljava/io/IOException;)Ljava/io/IOException;

    iget-object p1, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {p1}, Landroidx/media3/datasource/cronet/CronetDataSource;->o(Landroidx/media3/datasource/cronet/CronetDataSource;)Le2/g;

    move-result-object p1

    invoke-virtual {p1}, Le2/g;->f()Z
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    goto/16 :goto_2

    :cond_2
    :try_start_2
    iget-object v1, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {v1}, Landroidx/media3/datasource/cronet/CronetDataSource;->p(Landroidx/media3/datasource/cronet/CronetDataSource;)Z

    move-result v1

    if-eqz v1, :cond_3

    iget-object v1, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {v1}, Landroidx/media3/datasource/cronet/CronetDataSource;->q(Landroidx/media3/datasource/cronet/CronetDataSource;)V

    :cond_3
    iget-object v1, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {v1}, Landroidx/media3/datasource/cronet/CronetDataSource;->r(Landroidx/media3/datasource/cronet/CronetDataSource;)Z

    move-result v1

    const/4 v4, 0x1

    if-eqz v1, :cond_4

    iget v1, v7, Lh2/g;->c:I

    if-ne v1, v2, :cond_4

    const/16 v1, 0x12e

    if-ne v3, v1, :cond_4

    const/4 v1, 0x1

    goto :goto_0

    :cond_4
    const/4 v1, 0x1

    const/4 v1, 0x0

    :goto_0
    if-nez v1, :cond_5

    iget-object v3, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {v3}, Landroidx/media3/datasource/cronet/CronetDataSource;->s(Landroidx/media3/datasource/cronet/CronetDataSource;)Z

    move-result v3

    if-nez v3, :cond_5

    invoke-virtual {p1}, Lorg/chromium/net/UrlRequest;->followRedirect()V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    monitor-exit p0

    return-void

    :cond_5
    :try_start_3
    invoke-virtual {p2}, Lorg/chromium/net/UrlResponseInfo;->getAllHeaders()Ljava/util/Map;

    move-result-object p2

    const-string v3, "Set-Cookie"

    invoke-interface {p2, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Ljava/util/List;

    invoke-static {p2}, Landroidx/media3/datasource/cronet/CronetDataSource;->t(Ljava/util/List;)Ljava/lang/String;

    move-result-object p2

    if-nez v1, :cond_6

    invoke-static {p2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v3

    if-eqz v3, :cond_6

    invoke-virtual {p1}, Lorg/chromium/net/UrlRequest;->followRedirect()V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    monitor-exit p0

    return-void

    :cond_6
    :try_start_4
    invoke-virtual {v0}, Lorg/chromium/net/UrlRequest;->cancel()V

    if-nez v1, :cond_7

    iget p1, v7, Lh2/g;->c:I

    if-ne p1, v2, :cond_7

    invoke-virtual {v7}, Lh2/g;->a()Lh2/g$b;

    move-result-object p1

    invoke-virtual {p1, p3}, Lh2/g$b;->j(Ljava/lang/String;)Lh2/g$b;

    move-result-object p1

    invoke-virtual {p1, v4}, Lh2/g$b;->d(I)Lh2/g$b;

    move-result-object p1

    const/4 p3, 0x1

    const/4 p3, 0x0

    invoke-virtual {p1, p3}, Lh2/g$b;->c([B)Lh2/g$b;

    move-result-object p1

    invoke-virtual {p1}, Lh2/g$b;->a()Lh2/g;

    move-result-object p1

    goto :goto_1

    :cond_7
    invoke-static {p3}, Landroid/net/Uri;->parse(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object p1

    invoke-virtual {v7, p1}, Lh2/g;->g(Landroid/net/Uri;)Lh2/g;

    move-result-object p1
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    :goto_1
    :try_start_5
    iget-object p3, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-virtual {p3, p1}, Landroidx/media3/datasource/cronet/CronetDataSource;->w(Lh2/g;)Lorg/chromium/net/UrlRequest$Builder;

    move-result-object p1
    :try_end_5
    .catch Ljava/io/IOException; {:try_start_5 .. :try_end_5} :catch_0
    .catchall {:try_start_5 .. :try_end_5} :catchall_0

    :try_start_6
    invoke-static {p1, p2}, Landroidx/media3/datasource/cronet/CronetDataSource;->i(Lorg/chromium/net/UrlRequest$Builder;Ljava/lang/String;)V

    iget-object p2, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-virtual {p1}, Lorg/chromium/net/UrlRequest$Builder;->build()Lorg/chromium/net/UrlRequest;

    move-result-object p1

    invoke-static {p2, p1}, Landroidx/media3/datasource/cronet/CronetDataSource;->j(Landroidx/media3/datasource/cronet/CronetDataSource;Lorg/chromium/net/UrlRequest;)Lorg/chromium/net/UrlRequest;

    iget-object p1, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {p1}, Landroidx/media3/datasource/cronet/CronetDataSource;->h(Landroidx/media3/datasource/cronet/CronetDataSource;)Lorg/chromium/net/UrlRequest;

    move-result-object p1

    invoke-virtual {p1}, Lorg/chromium/net/UrlRequest;->start()V
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_0

    monitor-exit p0

    return-void

    :catch_0
    move-exception p1

    :try_start_7
    iget-object p2, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {p2, p1}, Landroidx/media3/datasource/cronet/CronetDataSource;->n(Landroidx/media3/datasource/cronet/CronetDataSource;Ljava/io/IOException;)Ljava/io/IOException;
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_0

    monitor-exit p0

    return-void

    :goto_2
    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized onResponseStarted(Lorg/chromium/net/UrlRequest;Lorg/chromium/net/UrlResponseInfo;)V
    .locals 1

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {v0}, Landroidx/media3/datasource/cronet/CronetDataSource;->h(Landroidx/media3/datasource/cronet/CronetDataSource;)Lorg/chromium/net/UrlRequest;

    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eq p1, v0, :cond_0

    monitor-exit p0

    return-void

    :cond_0
    :try_start_1
    iget-object p1, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {p1, p2}, Landroidx/media3/datasource/cronet/CronetDataSource;->k(Landroidx/media3/datasource/cronet/CronetDataSource;Lorg/chromium/net/UrlResponseInfo;)Lorg/chromium/net/UrlResponseInfo;

    iget-object p1, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {p1}, Landroidx/media3/datasource/cronet/CronetDataSource;->o(Landroidx/media3/datasource/cronet/CronetDataSource;)Le2/g;

    move-result-object p1

    invoke-virtual {p1}, Le2/g;->f()Z
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized onSucceeded(Lorg/chromium/net/UrlRequest;Lorg/chromium/net/UrlResponseInfo;)V
    .locals 0

    monitor-enter p0

    :try_start_0
    iget-object p2, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {p2}, Landroidx/media3/datasource/cronet/CronetDataSource;->h(Landroidx/media3/datasource/cronet/CronetDataSource;)Lorg/chromium/net/UrlRequest;

    move-result-object p2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eq p1, p2, :cond_0

    monitor-exit p0

    return-void

    :cond_0
    :try_start_1
    iget-object p1, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    const/4 p2, 0x1

    invoke-static {p1, p2}, Landroidx/media3/datasource/cronet/CronetDataSource;->l(Landroidx/media3/datasource/cronet/CronetDataSource;Z)Z

    iget-object p1, p0, Landroidx/media3/datasource/cronet/CronetDataSource$c;->f:Landroidx/media3/datasource/cronet/CronetDataSource;

    invoke-static {p1}, Landroidx/media3/datasource/cronet/CronetDataSource;->o(Landroidx/media3/datasource/cronet/CronetDataSource;)Le2/g;

    move-result-object p1

    invoke-virtual {p1}, Le2/g;->f()Z
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method
