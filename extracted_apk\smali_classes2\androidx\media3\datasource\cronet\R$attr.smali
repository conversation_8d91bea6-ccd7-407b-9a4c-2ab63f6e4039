.class public final Landroidx/media3/datasource/cronet/R$attr;
.super Ljava/lang/Object;


# static fields
.field public static alpha:I = 0x7f040065

.field public static buttonSize:I = 0x7f0401fa

.field public static circleCrop:I = 0x7f04023c

.field public static colorScheme:I = 0x7f040288

.field public static coordinatorLayoutStyle:I = 0x7f0402ad

.field public static font:I = 0x7f04037e

.field public static fontProviderAuthority:I = 0x7f040380

.field public static fontProviderCerts:I = 0x7f040381

.field public static fontProviderFetchStrategy:I = 0x7f040382

.field public static fontProviderFetchTimeout:I = 0x7f040383

.field public static fontProviderPackage:I = 0x7f040384

.field public static fontProviderQuery:I = 0x7f040385

.field public static fontStyle:I = 0x7f040387

.field public static fontVariationSettings:I = 0x7f040388

.field public static fontWeight:I = 0x7f040389

.field public static imageAspectRatio:I = 0x7f0403c0

.field public static imageAspectRatioAdjust:I = 0x7f0403c1

.field public static keylines:I = 0x7f04043a

.field public static layout_anchor:I = 0x7f04044b

.field public static layout_anchorGravity:I = 0x7f04044c

.field public static layout_behavior:I = 0x7f04044d

.field public static layout_dodgeInsetEdges:I = 0x7f04047e

.field public static layout_insetEdge:I = 0x7f04048d

.field public static layout_keyline:I = 0x7f04048e

.field public static scopeUris:I = 0x7f0405e0

.field public static statusBarBackground:I = 0x7f040665

.field public static ttcIndex:I = 0x7f040741


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
