.class public final Landroidx/compose/ui/text/platform/n;
.super Ljava/lang/Object;


# annotations
.annotation runtime Lkotlin/Metadata;
.end annotation


# static fields
.field public static final a:Landroidx/compose/ui/text/platform/o;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Landroidx/compose/ui/text/platform/o;

    const/4 v1, 0x1

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Landroidx/compose/ui/text/platform/o;-><init>(Z)V

    sput-object v0, Landroidx/compose/ui/text/platform/n;->a:Landroidx/compose/ui/text/platform/o;

    return-void
.end method

.method public static final synthetic a()Landroidx/compose/ui/text/platform/o;
    .locals 1

    sget-object v0, Landroidx/compose/ui/text/platform/n;->a:Landroidx/compose/ui/text/platform/o;

    return-object v0
.end method
