.class public interface abstract Landroidx/media3/exoplayer/source/k$a;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/source/t$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/source/k;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroidx/media3/exoplayer/source/t$a<",
        "Landroidx/media3/exoplayer/source/k;",
        ">;"
    }
.end annotation


# virtual methods
.method public abstract g(Landroidx/media3/exoplayer/source/k;)V
.end method
