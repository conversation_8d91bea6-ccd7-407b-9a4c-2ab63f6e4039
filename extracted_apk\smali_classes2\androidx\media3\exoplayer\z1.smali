.class public final Landroidx/media3/exoplayer/z1;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/z1$a;
    }
.end annotation


# instance fields
.field public final a:Landroidx/media3/exoplayer/source/k;

.field public final b:Ljava/lang/Object;

.field public final c:[Lu2/e0;

.field public d:Z

.field public e:Z

.field public f:Landroidx/media3/exoplayer/a2;

.field public g:Z

.field public final h:[Z

.field public final i:[Landroidx/media3/exoplayer/y2;

.field public final j:Lx2/e0;

.field public final k:Landroidx/media3/exoplayer/r2;

.field public l:Landroidx/media3/exoplayer/z1;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public m:Lu2/k0;

.field public n:Lx2/f0;

.field public o:J


# direct methods
.method public constructor <init>([Landroidx/media3/exoplayer/y2;JLx2/e0;Landroidx/media3/exoplayer/upstream/b;Landroidx/media3/exoplayer/r2;Landroidx/media3/exoplayer/a2;Lx2/f0;)V
    .locals 7

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/z1;->i:[Landroidx/media3/exoplayer/y2;

    iput-wide p2, p0, Landroidx/media3/exoplayer/z1;->o:J

    iput-object p4, p0, Landroidx/media3/exoplayer/z1;->j:Lx2/e0;

    iput-object p6, p0, Landroidx/media3/exoplayer/z1;->k:Landroidx/media3/exoplayer/r2;

    iget-object v0, p7, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    iget-object p2, v0, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iput-object p2, p0, Landroidx/media3/exoplayer/z1;->b:Ljava/lang/Object;

    iput-object p7, p0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    sget-object p2, Lu2/k0;->d:Lu2/k0;

    iput-object p2, p0, Landroidx/media3/exoplayer/z1;->m:Lu2/k0;

    iput-object p8, p0, Landroidx/media3/exoplayer/z1;->n:Lx2/f0;

    array-length p2, p1

    new-array p2, p2, [Lu2/e0;

    iput-object p2, p0, Landroidx/media3/exoplayer/z1;->c:[Lu2/e0;

    array-length p1, p1

    new-array p1, p1, [Z

    iput-object p1, p0, Landroidx/media3/exoplayer/z1;->h:[Z

    iget-wide v3, p7, Landroidx/media3/exoplayer/a2;->b:J

    iget-wide v5, p7, Landroidx/media3/exoplayer/a2;->d:J

    move-object v1, p6

    move-object v2, p5

    invoke-static/range {v0 .. v6}, Landroidx/media3/exoplayer/z1;->e(Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/exoplayer/r2;Landroidx/media3/exoplayer/upstream/b;JJ)Landroidx/media3/exoplayer/source/k;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    return-void
.end method

.method public static e(Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/exoplayer/r2;Landroidx/media3/exoplayer/upstream/b;JJ)Landroidx/media3/exoplayer/source/k;
    .locals 7

    invoke-virtual {p1, p0, p2, p3, p4}, Landroidx/media3/exoplayer/r2;->h(Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/exoplayer/upstream/b;J)Landroidx/media3/exoplayer/source/k;

    move-result-object v1

    const-wide p0, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long p2, p5, p0

    if-eqz p2, :cond_0

    new-instance p0, Landroidx/media3/exoplayer/source/b;

    const/4 v2, 0x1

    const-wide/16 v3, 0x0

    move-object v0, p0

    move-wide v5, p5

    invoke-direct/range {v0 .. v6}, Landroidx/media3/exoplayer/source/b;-><init>(Landroidx/media3/exoplayer/source/k;ZJJ)V

    move-object v1, p0

    :cond_0
    return-object v1
.end method

.method public static u(Landroidx/media3/exoplayer/r2;Landroidx/media3/exoplayer/source/k;)V
    .locals 1

    :try_start_0
    instance-of v0, p1, Landroidx/media3/exoplayer/source/b;

    if-eqz v0, :cond_0

    check-cast p1, Landroidx/media3/exoplayer/source/b;

    iget-object p1, p1, Landroidx/media3/exoplayer/source/b;->a:Landroidx/media3/exoplayer/source/k;

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/r2;->A(Landroidx/media3/exoplayer/source/k;)V

    goto :goto_1

    :catch_0
    move-exception p0

    goto :goto_0

    :cond_0
    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/r2;->A(Landroidx/media3/exoplayer/source/k;)V
    :try_end_0
    .catch Ljava/lang/RuntimeException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :goto_0
    const-string p1, "MediaPeriodHolder"

    const-string v0, "Period release failed."

    invoke-static {p1, v0, p0}, Le2/o;->d(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    :goto_1
    return-void
.end method


# virtual methods
.method public A()V
    .locals 6

    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    instance-of v1, v0, Landroidx/media3/exoplayer/source/b;

    if-eqz v1, :cond_1

    iget-object v1, p0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-wide v1, v1, Landroidx/media3/exoplayer/a2;->d:J

    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v5, v1, v3

    if-nez v5, :cond_0

    const-wide/high16 v1, -0x8000000000000000L

    :cond_0
    check-cast v0, Landroidx/media3/exoplayer/source/b;

    const-wide/16 v3, 0x0

    invoke-virtual {v0, v3, v4, v1, v2}, Landroidx/media3/exoplayer/source/b;->m(JJ)V

    :cond_1
    return-void
.end method

.method public a(Lx2/f0;JZ)J
    .locals 7

    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->i:[Landroidx/media3/exoplayer/y2;

    array-length v0, v0

    new-array v6, v0, [Z

    move-object v1, p0

    move-object v2, p1

    move-wide v3, p2

    move v5, p4

    invoke-virtual/range {v1 .. v6}, Landroidx/media3/exoplayer/z1;->b(Lx2/f0;JZ[Z)J

    move-result-wide p1

    return-wide p1
.end method

.method public b(Lx2/f0;JZ[Z)J
    .locals 13

    move-object v0, p0

    move-object v1, p1

    const/4 v2, 0x1

    const/4 v2, 0x0

    const/4 v3, 0x1

    const/4 v3, 0x0

    :goto_0
    iget v4, v1, Lx2/f0;->a:I

    const/4 v5, 0x1

    if-ge v3, v4, :cond_1

    iget-object v4, v0, Landroidx/media3/exoplayer/z1;->h:[Z

    if-nez p4, :cond_0

    iget-object v6, v0, Landroidx/media3/exoplayer/z1;->n:Lx2/f0;

    invoke-virtual {p1, v6, v3}, Lx2/f0;->b(Lx2/f0;I)Z

    move-result v6

    if-eqz v6, :cond_0

    goto :goto_1

    :cond_0
    const/4 v5, 0x1

    const/4 v5, 0x0

    :goto_1
    aput-boolean v5, v4, v3

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    iget-object v3, v0, Landroidx/media3/exoplayer/z1;->c:[Lu2/e0;

    invoke-virtual {p0, v3}, Landroidx/media3/exoplayer/z1;->g([Lu2/e0;)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/z1;->f()V

    iput-object v1, v0, Landroidx/media3/exoplayer/z1;->n:Lx2/f0;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/z1;->h()V

    iget-object v6, v0, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    iget-object v7, v1, Lx2/f0;->c:[Lx2/z;

    iget-object v8, v0, Landroidx/media3/exoplayer/z1;->h:[Z

    iget-object v9, v0, Landroidx/media3/exoplayer/z1;->c:[Lu2/e0;

    move-object/from16 v10, p5

    move-wide v11, p2

    invoke-interface/range {v6 .. v12}, Landroidx/media3/exoplayer/source/k;->f([Lx2/z;[Z[Lu2/e0;[ZJ)J

    move-result-wide v3

    iget-object v6, v0, Landroidx/media3/exoplayer/z1;->c:[Lu2/e0;

    invoke-virtual {p0, v6}, Landroidx/media3/exoplayer/z1;->c([Lu2/e0;)V

    iput-boolean v2, v0, Landroidx/media3/exoplayer/z1;->e:Z

    const/4 v6, 0x1

    const/4 v6, 0x0

    :goto_2
    iget-object v7, v0, Landroidx/media3/exoplayer/z1;->c:[Lu2/e0;

    array-length v8, v7

    if-ge v6, v8, :cond_5

    aget-object v7, v7, v6

    if-eqz v7, :cond_2

    invoke-virtual {p1, v6}, Lx2/f0;->c(I)Z

    move-result v7

    invoke-static {v7}, Le2/a;->g(Z)V

    iget-object v7, v0, Landroidx/media3/exoplayer/z1;->i:[Landroidx/media3/exoplayer/y2;

    aget-object v7, v7, v6

    invoke-interface {v7}, Landroidx/media3/exoplayer/y2;->getTrackType()I

    move-result v7

    const/4 v8, -0x2

    if-eq v7, v8, :cond_4

    iput-boolean v5, v0, Landroidx/media3/exoplayer/z1;->e:Z

    goto :goto_4

    :cond_2
    iget-object v7, v1, Lx2/f0;->c:[Lx2/z;

    aget-object v7, v7, v6

    if-nez v7, :cond_3

    const/4 v7, 0x1

    goto :goto_3

    :cond_3
    const/4 v7, 0x1

    const/4 v7, 0x0

    :goto_3
    invoke-static {v7}, Le2/a;->g(Z)V

    :cond_4
    :goto_4
    add-int/lit8 v6, v6, 0x1

    goto :goto_2

    :cond_5
    return-wide v3
.end method

.method public final c([Lu2/e0;)V
    .locals 3

    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    iget-object v1, p0, Landroidx/media3/exoplayer/z1;->i:[Landroidx/media3/exoplayer/y2;

    array-length v2, v1

    if-ge v0, v2, :cond_1

    aget-object v1, v1, v0

    invoke-interface {v1}, Landroidx/media3/exoplayer/y2;->getTrackType()I

    move-result v1

    const/4 v2, -0x2

    if-ne v1, v2, :cond_0

    iget-object v1, p0, Landroidx/media3/exoplayer/z1;->n:Lx2/f0;

    invoke-virtual {v1, v0}, Lx2/f0;->c(I)Z

    move-result v1

    if-eqz v1, :cond_0

    new-instance v1, Lu2/l;

    invoke-direct {v1}, Lu2/l;-><init>()V

    aput-object v1, p1, v0

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public d(JFJ)V
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/z1;->r()Z

    move-result v0

    invoke-static {v0}, Le2/a;->g(Z)V

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/z1;->y(J)J

    move-result-wide p1

    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    new-instance v1, Landroidx/media3/exoplayer/w1$b;

    invoke-direct {v1}, Landroidx/media3/exoplayer/w1$b;-><init>()V

    invoke-virtual {v1, p1, p2}, Landroidx/media3/exoplayer/w1$b;->f(J)Landroidx/media3/exoplayer/w1$b;

    move-result-object p1

    invoke-virtual {p1, p3}, Landroidx/media3/exoplayer/w1$b;->g(F)Landroidx/media3/exoplayer/w1$b;

    move-result-object p1

    invoke-virtual {p1, p4, p5}, Landroidx/media3/exoplayer/w1$b;->e(J)Landroidx/media3/exoplayer/w1$b;

    move-result-object p1

    invoke-virtual {p1}, Landroidx/media3/exoplayer/w1$b;->d()Landroidx/media3/exoplayer/w1;

    move-result-object p1

    invoke-interface {v0, p1}, Landroidx/media3/exoplayer/source/k;->a(Landroidx/media3/exoplayer/w1;)Z

    return-void
.end method

.method public final f()V
    .locals 3

    invoke-virtual {p0}, Landroidx/media3/exoplayer/z1;->r()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    iget-object v1, p0, Landroidx/media3/exoplayer/z1;->n:Lx2/f0;

    iget v2, v1, Lx2/f0;->a:I

    if-ge v0, v2, :cond_2

    invoke-virtual {v1, v0}, Lx2/f0;->c(I)Z

    move-result v1

    iget-object v2, p0, Landroidx/media3/exoplayer/z1;->n:Lx2/f0;

    iget-object v2, v2, Lx2/f0;->c:[Lx2/z;

    aget-object v2, v2, v0

    if-eqz v1, :cond_1

    if-eqz v2, :cond_1

    invoke-interface {v2}, Lx2/z;->disable()V

    :cond_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_2
    return-void
.end method

.method public final g([Lu2/e0;)V
    .locals 3

    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    iget-object v1, p0, Landroidx/media3/exoplayer/z1;->i:[Landroidx/media3/exoplayer/y2;

    array-length v2, v1

    if-ge v0, v2, :cond_1

    aget-object v1, v1, v0

    invoke-interface {v1}, Landroidx/media3/exoplayer/y2;->getTrackType()I

    move-result v1

    const/4 v2, -0x2

    if-ne v1, v2, :cond_0

    const/4 v1, 0x1

    const/4 v1, 0x0

    aput-object v1, p1, v0

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public final h()V
    .locals 3

    invoke-virtual {p0}, Landroidx/media3/exoplayer/z1;->r()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    iget-object v1, p0, Landroidx/media3/exoplayer/z1;->n:Lx2/f0;

    iget v2, v1, Lx2/f0;->a:I

    if-ge v0, v2, :cond_2

    invoke-virtual {v1, v0}, Lx2/f0;->c(I)Z

    move-result v1

    iget-object v2, p0, Landroidx/media3/exoplayer/z1;->n:Lx2/f0;

    iget-object v2, v2, Lx2/f0;->c:[Lx2/z;

    aget-object v2, v2, v0

    if-eqz v1, :cond_1

    if-eqz v2, :cond_1

    invoke-interface {v2}, Lx2/z;->enable()V

    :cond_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_2
    return-void
.end method

.method public i()J
    .locals 5

    iget-boolean v0, p0, Landroidx/media3/exoplayer/z1;->d:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-wide v0, v0, Landroidx/media3/exoplayer/a2;->b:J

    return-wide v0

    :cond_0
    iget-boolean v0, p0, Landroidx/media3/exoplayer/z1;->e:Z

    const-wide/high16 v1, -0x8000000000000000L

    if-eqz v0, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    invoke-interface {v0}, Landroidx/media3/exoplayer/source/k;->getBufferedPositionUs()J

    move-result-wide v3

    goto :goto_0

    :cond_1
    move-wide v3, v1

    :goto_0
    cmp-long v0, v3, v1

    if-nez v0, :cond_2

    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-wide v3, v0, Landroidx/media3/exoplayer/a2;->e:J

    :cond_2
    return-wide v3
.end method

.method public j()Landroidx/media3/exoplayer/z1;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->l:Landroidx/media3/exoplayer/z1;

    return-object v0
.end method

.method public k()J
    .locals 2

    iget-boolean v0, p0, Landroidx/media3/exoplayer/z1;->d:Z

    if-nez v0, :cond_0

    const-wide/16 v0, 0x0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    invoke-interface {v0}, Landroidx/media3/exoplayer/source/k;->getNextLoadPositionUs()J

    move-result-wide v0

    :goto_0
    return-wide v0
.end method

.method public l()J
    .locals 2

    iget-wide v0, p0, Landroidx/media3/exoplayer/z1;->o:J

    return-wide v0
.end method

.method public m()J
    .locals 4

    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-wide v0, v0, Landroidx/media3/exoplayer/a2;->b:J

    iget-wide v2, p0, Landroidx/media3/exoplayer/z1;->o:J

    add-long/2addr v0, v2

    return-wide v0
.end method

.method public n()Lu2/k0;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->m:Lu2/k0;

    return-object v0
.end method

.method public o()Lx2/f0;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->n:Lx2/f0;

    return-object v0
.end method

.method public p(FLandroidx/media3/common/m0;)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/z1;->d:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    invoke-interface {v0}, Landroidx/media3/exoplayer/source/k;->getTrackGroups()Lu2/k0;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/z1;->m:Lu2/k0;

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/z1;->v(FLandroidx/media3/common/m0;)Lx2/f0;

    move-result-object p1

    iget-object p2, p0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-wide v0, p2, Landroidx/media3/exoplayer/a2;->b:J

    iget-wide v2, p2, Landroidx/media3/exoplayer/a2;->e:J

    const-wide v4, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long p2, v2, v4

    if-eqz p2, :cond_0

    cmp-long p2, v0, v2

    if-ltz p2, :cond_0

    const-wide/16 v0, 0x1

    sub-long/2addr v2, v0

    const-wide/16 v0, 0x0

    invoke-static {v0, v1, v2, v3}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v0

    :cond_0
    const/4 p2, 0x1

    const/4 p2, 0x0

    invoke-virtual {p0, p1, v0, v1, p2}, Landroidx/media3/exoplayer/z1;->a(Lx2/f0;JZ)J

    move-result-wide p1

    iget-wide v0, p0, Landroidx/media3/exoplayer/z1;->o:J

    iget-object v2, p0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-wide v3, v2, Landroidx/media3/exoplayer/a2;->b:J

    sub-long/2addr v3, p1

    add-long/2addr v0, v3

    iput-wide v0, p0, Landroidx/media3/exoplayer/z1;->o:J

    invoke-virtual {v2, p1, p2}, Landroidx/media3/exoplayer/a2;->b(J)Landroidx/media3/exoplayer/a2;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    return-void
.end method

.method public q()Z
    .locals 5

    iget-boolean v0, p0, Landroidx/media3/exoplayer/z1;->d:Z

    if-eqz v0, :cond_1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/z1;->e:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    invoke-interface {v0}, Landroidx/media3/exoplayer/source/k;->getBufferedPositionUs()J

    move-result-wide v0

    const-wide/high16 v2, -0x8000000000000000L

    cmp-long v4, v0, v2

    if-nez v4, :cond_1

    :cond_0
    const/4 v0, 0x1

    goto :goto_0

    :cond_1
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final r()Z
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->l:Landroidx/media3/exoplayer/z1;

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public s(J)V
    .locals 1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/z1;->r()Z

    move-result v0

    invoke-static {v0}, Le2/a;->g(Z)V

    iget-boolean v0, p0, Landroidx/media3/exoplayer/z1;->d:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/z1;->y(J)J

    move-result-wide p1

    invoke-interface {v0, p1, p2}, Landroidx/media3/exoplayer/source/k;->reevaluateBuffer(J)V

    :cond_0
    return-void
.end method

.method public t()V
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/z1;->f()V

    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->k:Landroidx/media3/exoplayer/r2;

    iget-object v1, p0, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    invoke-static {v0, v1}, Landroidx/media3/exoplayer/z1;->u(Landroidx/media3/exoplayer/r2;Landroidx/media3/exoplayer/source/k;)V

    return-void
.end method

.method public v(FLandroidx/media3/common/m0;)Lx2/f0;
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->j:Lx2/e0;

    iget-object v1, p0, Landroidx/media3/exoplayer/z1;->i:[Landroidx/media3/exoplayer/y2;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/z1;->n()Lu2/k0;

    move-result-object v2

    iget-object v3, p0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v3, v3, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v0, v1, v2, v3, p2}, Lx2/e0;->k([Landroidx/media3/exoplayer/y2;Lu2/k0;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0;)Lx2/f0;

    move-result-object p2

    iget-object v0, p2, Lx2/f0;->c:[Lx2/z;

    array-length v1, v0

    const/4 v2, 0x1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_1

    aget-object v3, v0, v2

    if-eqz v3, :cond_0

    invoke-interface {v3, p1}, Lx2/z;->onPlaybackSpeed(F)V

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    return-object p2
.end method

.method public w(Landroidx/media3/exoplayer/z1;)V
    .locals 1
    .param p1    # Landroidx/media3/exoplayer/z1;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Landroidx/media3/exoplayer/z1;->l:Landroidx/media3/exoplayer/z1;

    if-ne p1, v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/z1;->f()V

    iput-object p1, p0, Landroidx/media3/exoplayer/z1;->l:Landroidx/media3/exoplayer/z1;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/z1;->h()V

    return-void
.end method

.method public x(J)V
    .locals 0

    iput-wide p1, p0, Landroidx/media3/exoplayer/z1;->o:J

    return-void
.end method

.method public y(J)J
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/z1;->l()J

    move-result-wide v0

    sub-long/2addr p1, v0

    return-wide p1
.end method

.method public z(J)J
    .locals 2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/z1;->l()J

    move-result-wide v0

    add-long/2addr p1, v0

    return-wide p1
.end method
