.class final Lcom/google/android/gms/internal/ads/zzcyp;
.super Ljava/lang/Object;


# static fields
.field private static final zza:Lcom/google/android/gms/internal/ads/zzcyq;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/google/android/gms/internal/ads/zzcyq;

    invoke-direct {v0}, Lcom/google/android/gms/internal/ads/zzcyq;-><init>()V

    sput-object v0, Lcom/google/android/gms/internal/ads/zzcyp;->zza:Lcom/google/android/gms/internal/ads/zzcyq;

    return-void
.end method

.method public static bridge synthetic zza()Lcom/google/android/gms/internal/ads/zzcyq;
    .locals 1

    sget-object v0, Lcom/google/android/gms/internal/ads/zzcyp;->zza:Lcom/google/android/gms/internal/ads/zzcyq;

    return-object v0
.end method
