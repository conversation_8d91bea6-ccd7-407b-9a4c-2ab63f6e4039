<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:id="@id/fl_root" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.noober.background.view.BLConstraintLayout android:id="@id/cl_content" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="98.0dip" app:bl_corners_topRadius="8.0dip" app:bl_solid_color="@color/bg_01">
        <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="start" android:id="@id/tv_title" android:paddingTop="14.0dip" android:paddingBottom="14.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/Select_language" android:maxLines="1" android:layout_marginStart="16.0dip" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_import_text" />
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_close" android:padding="14.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@mipmap/ic_download_dialog_close" app:layout_constraintBottom_toBottomOf="@id/tv_title" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="@id/tv_title" />
        <View android:background="@color/line_01" android:layout_width="0.0dip" android:layout_height="1.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" />
        <androidx.recyclerview.widget.RecyclerView android:id="@id/rv" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_marginLeft="12.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="12.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/tv_title" />
    </com.noober.background.view.BLConstraintLayout>
</FrameLayout>
