.class public final synthetic Landroidx/media3/datasource/cache/e;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/datasource/cache/g;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lh2/g;)Ljava/lang/String;
    .locals 0

    invoke-static {p1}, Landroidx/media3/datasource/cache/f;->a(Lh2/g;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method
