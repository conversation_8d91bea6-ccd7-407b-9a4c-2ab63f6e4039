.class public final Landroidx/media3/exoplayer/s1;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/os/Hand<PERSON>$Callback;
.implements Landroidx/media3/exoplayer/source/k$a;
.implements Lx2/e0$a;
.implements Landroidx/media3/exoplayer/r2$d;
.implements Landroidx/media3/exoplayer/r$a;
.implements Landroidx/media3/exoplayer/t2$a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/s1$f;,
        Landroidx/media3/exoplayer/s1$e;,
        Landroidx/media3/exoplayer/s1$h;,
        Landroidx/media3/exoplayer/s1$b;,
        Landroidx/media3/exoplayer/s1$c;,
        Landroidx/media3/exoplayer/s1$d;,
        Landroidx/media3/exoplayer/s1$g;
    }
.end annotation


# instance fields
.field public A:Z

.field public B:Z

.field public C:Z

.field public D:Z

.field public E:J

.field public F:Z

.field public G:I

.field public H:Z

.field public I:Z

.field public J:Z

.field public K:Z

.field public L:I

.field public M:Landroidx/media3/exoplayer/s1$h;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public N:J

.field public O:I

.field public P:Z

.field public Q:Landroidx/media3/exoplayer/ExoPlaybackException;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public R:J

.field public S:J

.field public final a:[Landroidx/media3/exoplayer/w2;

.field public final b:Ljava/util/Set;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Set<",
            "Landroidx/media3/exoplayer/w2;",
            ">;"
        }
    .end annotation
.end field

.field public final c:[Landroidx/media3/exoplayer/y2;

.field public final d:Lx2/e0;

.field public final f:Lx2/f0;

.field public final g:Landroidx/media3/exoplayer/v1;

.field public final h:Landroidx/media3/exoplayer/upstream/e;

.field public final i:Le2/j;

.field public final j:Landroid/os/HandlerThread;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final k:Landroid/os/Looper;

.field public final l:Landroidx/media3/common/m0$c;

.field public final m:Landroidx/media3/common/m0$b;

.field public final n:J

.field public final o:Z

.field public final p:Landroidx/media3/exoplayer/r;

.field public final q:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Landroidx/media3/exoplayer/s1$d;",
            ">;"
        }
    .end annotation
.end field

.field public final r:Le2/d;

.field public final s:Landroidx/media3/exoplayer/s1$f;

.field public final t:Landroidx/media3/exoplayer/c2;

.field public final u:Landroidx/media3/exoplayer/r2;

.field public final v:Landroidx/media3/exoplayer/u1;

.field public final w:J

.field public x:Landroidx/media3/exoplayer/b3;

.field public y:Landroidx/media3/exoplayer/s2;

.field public z:Landroidx/media3/exoplayer/s1$e;


# direct methods
.method public constructor <init>([Landroidx/media3/exoplayer/w2;Lx2/e0;Lx2/f0;Landroidx/media3/exoplayer/v1;Landroidx/media3/exoplayer/upstream/e;IZLj2/a;Landroidx/media3/exoplayer/b3;Landroidx/media3/exoplayer/u1;JZLandroid/os/Looper;Le2/d;Landroidx/media3/exoplayer/s1$f;Lj2/x3;Landroid/os/Looper;)V
    .locals 13

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object/from16 v3, p5

    move-object/from16 v4, p8

    move-wide/from16 v5, p11

    move-object/from16 v7, p15

    move-object/from16 v8, p17

    move-object/from16 v9, p18

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    move-object/from16 v10, p16

    iput-object v10, v0, Landroidx/media3/exoplayer/s1;->s:Landroidx/media3/exoplayer/s1$f;

    iput-object v1, v0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    iput-object v2, v0, Landroidx/media3/exoplayer/s1;->d:Lx2/e0;

    move-object/from16 v10, p3

    iput-object v10, v0, Landroidx/media3/exoplayer/s1;->f:Lx2/f0;

    move-object/from16 v11, p4

    iput-object v11, v0, Landroidx/media3/exoplayer/s1;->g:Landroidx/media3/exoplayer/v1;

    iput-object v3, v0, Landroidx/media3/exoplayer/s1;->h:Landroidx/media3/exoplayer/upstream/e;

    move/from16 v12, p6

    iput v12, v0, Landroidx/media3/exoplayer/s1;->G:I

    move/from16 v12, p7

    iput-boolean v12, v0, Landroidx/media3/exoplayer/s1;->H:Z

    move-object/from16 v12, p9

    iput-object v12, v0, Landroidx/media3/exoplayer/s1;->x:Landroidx/media3/exoplayer/b3;

    move-object/from16 v12, p10

    iput-object v12, v0, Landroidx/media3/exoplayer/s1;->v:Landroidx/media3/exoplayer/u1;

    iput-wide v5, v0, Landroidx/media3/exoplayer/s1;->w:J

    iput-wide v5, v0, Landroidx/media3/exoplayer/s1;->R:J

    move/from16 v5, p13

    iput-boolean v5, v0, Landroidx/media3/exoplayer/s1;->B:Z

    iput-object v7, v0, Landroidx/media3/exoplayer/s1;->r:Le2/d;

    const-wide v5, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide v5, v0, Landroidx/media3/exoplayer/s1;->S:J

    iput-wide v5, v0, Landroidx/media3/exoplayer/s1;->E:J

    invoke-interface/range {p4 .. p4}, Landroidx/media3/exoplayer/v1;->getBackBufferDurationUs()J

    move-result-wide v5

    iput-wide v5, v0, Landroidx/media3/exoplayer/s1;->n:J

    invoke-interface/range {p4 .. p4}, Landroidx/media3/exoplayer/v1;->retainBackBufferFromKeyframe()Z

    move-result v5

    iput-boolean v5, v0, Landroidx/media3/exoplayer/s1;->o:Z

    invoke-static/range {p3 .. p3}, Landroidx/media3/exoplayer/s2;->k(Lx2/f0;)Landroidx/media3/exoplayer/s2;

    move-result-object v5

    iput-object v5, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    new-instance v6, Landroidx/media3/exoplayer/s1$e;

    invoke-direct {v6, v5}, Landroidx/media3/exoplayer/s1$e;-><init>(Landroidx/media3/exoplayer/s2;)V

    iput-object v6, v0, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    array-length v5, v1

    new-array v5, v5, [Landroidx/media3/exoplayer/y2;

    iput-object v5, v0, Landroidx/media3/exoplayer/s1;->c:[Landroidx/media3/exoplayer/y2;

    invoke-virtual {p2}, Lx2/e0;->d()Landroidx/media3/exoplayer/y2$a;

    move-result-object v5

    const/4 v6, 0x1

    const/4 v6, 0x0

    :goto_0
    array-length v10, v1

    if-ge v6, v10, :cond_1

    aget-object v10, v1, v6

    invoke-interface {v10, v6, v8, v7}, Landroidx/media3/exoplayer/w2;->o(ILj2/x3;Le2/d;)V

    iget-object v10, v0, Landroidx/media3/exoplayer/s1;->c:[Landroidx/media3/exoplayer/y2;

    aget-object v11, v1, v6

    invoke-interface {v11}, Landroidx/media3/exoplayer/w2;->getCapabilities()Landroidx/media3/exoplayer/y2;

    move-result-object v11

    aput-object v11, v10, v6

    if-eqz v5, :cond_0

    iget-object v10, v0, Landroidx/media3/exoplayer/s1;->c:[Landroidx/media3/exoplayer/y2;

    aget-object v10, v10, v6

    invoke-interface {v10, v5}, Landroidx/media3/exoplayer/y2;->m(Landroidx/media3/exoplayer/y2$a;)V

    :cond_0
    add-int/lit8 v6, v6, 0x1

    goto :goto_0

    :cond_1
    new-instance v1, Landroidx/media3/exoplayer/r;

    invoke-direct {v1, p0, v7}, Landroidx/media3/exoplayer/r;-><init>(Landroidx/media3/exoplayer/r$a;Le2/d;)V

    iput-object v1, v0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iput-object v1, v0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-static {}, Lcom/google/common/collect/Sets;->h()Ljava/util/Set;

    move-result-object v1

    iput-object v1, v0, Landroidx/media3/exoplayer/s1;->b:Ljava/util/Set;

    new-instance v1, Landroidx/media3/common/m0$c;

    invoke-direct {v1}, Landroidx/media3/common/m0$c;-><init>()V

    iput-object v1, v0, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    new-instance v1, Landroidx/media3/common/m0$b;

    invoke-direct {v1}, Landroidx/media3/common/m0$b;-><init>()V

    iput-object v1, v0, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    invoke-virtual {p2, p0, v3}, Lx2/e0;->e(Lx2/e0$a;Landroidx/media3/exoplayer/upstream/e;)V

    const/4 v1, 0x1

    iput-boolean v1, v0, Landroidx/media3/exoplayer/s1;->P:Z

    const/4 v1, 0x1

    const/4 v1, 0x0

    move-object/from16 v2, p14

    invoke-interface {v7, v2, v1}, Le2/d;->createHandler(Landroid/os/Looper;Landroid/os/Handler$Callback;)Le2/j;

    move-result-object v2

    new-instance v3, Landroidx/media3/exoplayer/c2;

    new-instance v5, Landroidx/media3/exoplayer/p1;

    invoke-direct {v5, p0}, Landroidx/media3/exoplayer/p1;-><init>(Landroidx/media3/exoplayer/s1;)V

    invoke-direct {v3, v4, v2, v5}, Landroidx/media3/exoplayer/c2;-><init>(Lj2/a;Le2/j;Landroidx/media3/exoplayer/z1$a;)V

    iput-object v3, v0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    new-instance v3, Landroidx/media3/exoplayer/r2;

    invoke-direct {v3, p0, v4, v2, v8}, Landroidx/media3/exoplayer/r2;-><init>(Landroidx/media3/exoplayer/r2$d;Lj2/a;Le2/j;Lj2/x3;)V

    iput-object v3, v0, Landroidx/media3/exoplayer/s1;->u:Landroidx/media3/exoplayer/r2;

    if-eqz v9, :cond_2

    iput-object v1, v0, Landroidx/media3/exoplayer/s1;->j:Landroid/os/HandlerThread;

    iput-object v9, v0, Landroidx/media3/exoplayer/s1;->k:Landroid/os/Looper;

    goto :goto_1

    :cond_2
    new-instance v1, Landroid/os/HandlerThread;

    const-string v2, "ExoPlayer:Playback"

    const/16 v3, -0x10

    invoke-direct {v1, v2, v3}, Landroid/os/HandlerThread;-><init>(Ljava/lang/String;I)V

    iput-object v1, v0, Landroidx/media3/exoplayer/s1;->j:Landroid/os/HandlerThread;

    invoke-virtual {v1}, Ljava/lang/Thread;->start()V

    invoke-virtual {v1}, Landroid/os/HandlerThread;->getLooper()Landroid/os/Looper;

    move-result-object v1

    iput-object v1, v0, Landroidx/media3/exoplayer/s1;->k:Landroid/os/Looper;

    :goto_1
    iget-object v1, v0, Landroidx/media3/exoplayer/s1;->k:Landroid/os/Looper;

    invoke-interface {v7, v1, p0}, Le2/d;->createHandler(Landroid/os/Looper;Landroid/os/Handler$Callback;)Le2/j;

    move-result-object v1

    iput-object v1, v0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    return-void
.end method

.method public static A(Lx2/z;)[Landroidx/media3/common/y;
    .locals 4

    const/4 v0, 0x1

    const/4 v0, 0x0

    if-eqz p0, :cond_0

    invoke-interface {p0}, Lx2/c0;->length()I

    move-result v1

    goto :goto_0

    :cond_0
    const/4 v1, 0x1

    const/4 v1, 0x0

    :goto_0
    new-array v2, v1, [Landroidx/media3/common/y;

    :goto_1
    if-ge v0, v1, :cond_1

    invoke-interface {p0, v0}, Lx2/c0;->getFormat(I)Landroidx/media3/common/y;

    move-result-object v3

    aput-object v3, v2, v0

    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    :cond_1
    return-object v2
.end method

.method public static A0(Landroidx/media3/exoplayer/s1$d;Landroidx/media3/common/m0;Landroidx/media3/common/m0;IZLandroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;)Z
    .locals 15

    move-object v0, p0

    move-object/from16 v8, p1

    move-object/from16 v1, p2

    move-object/from16 v9, p5

    move-object/from16 v10, p6

    iget-object v2, v0, Landroidx/media3/exoplayer/s1$d;->d:Ljava/lang/Object;

    const/4 v11, 0x1

    const/4 v11, 0x0

    const/4 v12, 0x1

    const-wide/high16 v13, -0x8000000000000000L

    if-nez v2, :cond_3

    iget-object v1, v0, Landroidx/media3/exoplayer/s1$d;->a:Landroidx/media3/exoplayer/t2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/t2;->f()J

    move-result-wide v1

    cmp-long v3, v1, v13

    if-nez v3, :cond_0

    const-wide v1, -0x7fffffffffffffffL    # -4.9E-324

    goto :goto_0

    :cond_0
    iget-object v1, v0, Landroidx/media3/exoplayer/s1$d;->a:Landroidx/media3/exoplayer/t2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/t2;->f()J

    move-result-wide v1

    invoke-static {v1, v2}, Le2/u0;->S0(J)J

    move-result-wide v1

    :goto_0
    new-instance v3, Landroidx/media3/exoplayer/s1$h;

    iget-object v4, v0, Landroidx/media3/exoplayer/s1$d;->a:Landroidx/media3/exoplayer/t2;

    invoke-virtual {v4}, Landroidx/media3/exoplayer/t2;->h()Landroidx/media3/common/m0;

    move-result-object v4

    iget-object v5, v0, Landroidx/media3/exoplayer/s1$d;->a:Landroidx/media3/exoplayer/t2;

    invoke-virtual {v5}, Landroidx/media3/exoplayer/t2;->d()I

    move-result v5

    invoke-direct {v3, v4, v5, v1, v2}, Landroidx/media3/exoplayer/s1$h;-><init>(Landroidx/media3/common/m0;IJ)V

    const/4 v4, 0x1

    const/4 v4, 0x0

    move-object/from16 v1, p1

    move-object v2, v3

    move v3, v4

    move/from16 v4, p3

    move/from16 v5, p4

    move-object/from16 v6, p5

    move-object/from16 v7, p6

    invoke-static/range {v1 .. v7}, Landroidx/media3/exoplayer/s1;->D0(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/s1$h;ZIZLandroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;)Landroid/util/Pair;

    move-result-object v1

    if-nez v1, :cond_1

    return v11

    :cond_1
    iget-object v2, v1, Landroid/util/Pair;->first:Ljava/lang/Object;

    invoke-virtual {v8, v2}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v2

    iget-object v3, v1, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v3, Ljava/lang/Long;

    invoke-virtual {v3}, Ljava/lang/Long;->longValue()J

    move-result-wide v3

    iget-object v1, v1, Landroid/util/Pair;->first:Ljava/lang/Object;

    invoke-virtual {p0, v2, v3, v4, v1}, Landroidx/media3/exoplayer/s1$d;->b(IJLjava/lang/Object;)V

    iget-object v1, v0, Landroidx/media3/exoplayer/s1$d;->a:Landroidx/media3/exoplayer/t2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/t2;->f()J

    move-result-wide v1

    cmp-long v3, v1, v13

    if-nez v3, :cond_2

    invoke-static {v8, p0, v9, v10}, Landroidx/media3/exoplayer/s1;->z0(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/s1$d;Landroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;)V

    :cond_2
    return v12

    :cond_3
    invoke-virtual {v8, v2}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v2

    const/4 v3, -0x1

    if-ne v2, v3, :cond_4

    return v11

    :cond_4
    iget-object v3, v0, Landroidx/media3/exoplayer/s1$d;->a:Landroidx/media3/exoplayer/t2;

    invoke-virtual {v3}, Landroidx/media3/exoplayer/t2;->f()J

    move-result-wide v3

    cmp-long v5, v3, v13

    if-nez v5, :cond_5

    invoke-static {v8, p0, v9, v10}, Landroidx/media3/exoplayer/s1;->z0(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/s1$d;Landroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;)V

    return v12

    :cond_5
    iput v2, v0, Landroidx/media3/exoplayer/s1$d;->b:I

    iget-object v2, v0, Landroidx/media3/exoplayer/s1$d;->d:Ljava/lang/Object;

    invoke-virtual {v1, v2, v10}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    iget-boolean v2, v10, Landroidx/media3/common/m0$b;->f:Z

    if-eqz v2, :cond_6

    iget v2, v10, Landroidx/media3/common/m0$b;->c:I

    invoke-virtual {v1, v2, v9}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object v2

    iget v2, v2, Landroidx/media3/common/m0$c;->o:I

    iget-object v3, v0, Landroidx/media3/exoplayer/s1$d;->d:Ljava/lang/Object;

    invoke-virtual {v1, v3}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v1

    if-ne v2, v1, :cond_6

    iget-wide v1, v0, Landroidx/media3/exoplayer/s1$d;->c:J

    invoke-virtual/range {p6 .. p6}, Landroidx/media3/common/m0$b;->o()J

    move-result-wide v3

    add-long v5, v1, v3

    iget-object v1, v0, Landroidx/media3/exoplayer/s1$d;->d:Ljava/lang/Object;

    invoke-virtual {v8, v1, v10}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v1

    iget v4, v1, Landroidx/media3/common/m0$b;->c:I

    move-object/from16 v1, p1

    move-object/from16 v2, p5

    move-object/from16 v3, p6

    invoke-virtual/range {v1 .. v6}, Landroidx/media3/common/m0;->j(Landroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;IJ)Landroid/util/Pair;

    move-result-object v1

    iget-object v2, v1, Landroid/util/Pair;->first:Ljava/lang/Object;

    invoke-virtual {v8, v2}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v2

    iget-object v3, v1, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v3, Ljava/lang/Long;

    invoke-virtual {v3}, Ljava/lang/Long;->longValue()J

    move-result-wide v3

    iget-object v1, v1, Landroid/util/Pair;->first:Ljava/lang/Object;

    invoke-virtual {p0, v2, v3, v4, v1}, Landroidx/media3/exoplayer/s1$d;->b(IJLjava/lang/Object;)V

    :cond_6
    return v12
.end method

.method public static C0(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/s2;Landroidx/media3/exoplayer/s1$h;Landroidx/media3/exoplayer/c2;IZLandroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;)Landroidx/media3/exoplayer/s1$g;
    .locals 30
    .param p2    # Landroidx/media3/exoplayer/s1$h;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    move-object/from16 v7, p0

    move-object/from16 v8, p1

    move-object/from16 v9, p2

    move/from16 v10, p5

    move-object/from16 v11, p7

    invoke-virtual/range {p0 .. p0}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    if-eqz v0, :cond_0

    new-instance v0, Landroidx/media3/exoplayer/s1$g;

    invoke-static {}, Landroidx/media3/exoplayer/s2;->l()Landroidx/media3/exoplayer/source/l$b;

    move-result-object v2

    const-wide/16 v3, 0x0

    const-wide v5, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v7, 0x1

    const/4 v7, 0x0

    const/4 v8, 0x1

    const/4 v9, 0x1

    const/4 v9, 0x0

    move-object v1, v0

    invoke-direct/range {v1 .. v9}, Landroidx/media3/exoplayer/s1$g;-><init>(Landroidx/media3/exoplayer/source/l$b;JJZZZ)V

    return-object v0

    :cond_0
    iget-object v14, v8, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v12, v14, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-static {v8, v11}, Landroidx/media3/exoplayer/s1;->V(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/m0$b;)Z

    move-result v13

    iget-object v0, v8, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v0

    if-nez v0, :cond_2

    if-eqz v13, :cond_1

    goto :goto_1

    :cond_1
    iget-wide v0, v8, Landroidx/media3/exoplayer/s2;->r:J

    :goto_0
    move-wide v15, v0

    goto :goto_2

    :cond_2
    :goto_1
    iget-wide v0, v8, Landroidx/media3/exoplayer/s2;->c:J

    goto :goto_0

    :goto_2
    const-wide v17, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v6, -0x1

    const/16 v19, 0x0

    const/16 v20, 0x1

    if-eqz v9, :cond_6

    const/4 v2, 0x1

    move-object/from16 v0, p0

    move-object/from16 v1, p2

    move/from16 v3, p4

    move/from16 v4, p5

    move-object/from16 v5, p6

    move-object/from16 v21, v14

    const/4 v14, -0x1

    move-object/from16 v6, p7

    invoke-static/range {v0 .. v6}, Landroidx/media3/exoplayer/s1;->D0(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/s1$h;ZIZLandroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;)Landroid/util/Pair;

    move-result-object v0

    if-nez v0, :cond_3

    invoke-virtual {v7, v10}, Landroidx/media3/common/m0;->a(Z)I

    move-result v0

    move v6, v0

    move-wide v0, v15

    const/4 v2, 0x1

    const/4 v2, 0x0

    const/4 v3, 0x1

    const/4 v3, 0x0

    const/4 v4, 0x1

    goto :goto_5

    :cond_3
    iget-wide v1, v9, Landroidx/media3/exoplayer/s1$h;->c:J

    cmp-long v3, v1, v17

    if-nez v3, :cond_4

    iget-object v0, v0, Landroid/util/Pair;->first:Ljava/lang/Object;

    invoke-virtual {v7, v0, v11}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v0

    iget v6, v0, Landroidx/media3/common/m0$b;->c:I

    move-wide v0, v15

    const/4 v2, 0x1

    const/4 v2, 0x0

    goto :goto_3

    :cond_4
    iget-object v12, v0, Landroid/util/Pair;->first:Ljava/lang/Object;

    iget-object v0, v0, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Long;

    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    move-result-wide v0

    const/4 v2, 0x1

    const/4 v6, -0x1

    :goto_3
    iget v3, v8, Landroidx/media3/exoplayer/s2;->e:I

    const/4 v4, 0x4

    if-ne v3, v4, :cond_5

    const/4 v3, 0x1

    goto :goto_4

    :cond_5
    const/4 v3, 0x1

    const/4 v3, 0x0

    :goto_4
    const/4 v4, 0x1

    const/4 v4, 0x0

    :goto_5
    move-object/from16 v9, p6

    move/from16 v29, v2

    move/from16 v27, v3

    move/from16 v28, v4

    move v3, v6

    move-object/from16 v6, v21

    goto/16 :goto_b

    :cond_6
    move-object/from16 v21, v14

    const/4 v14, -0x1

    iget-object v0, v8, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    if-eqz v0, :cond_7

    invoke-virtual {v7, v10}, Landroidx/media3/common/m0;->a(Z)I

    move-result v0

    :goto_6
    move-object/from16 v9, p6

    move v3, v0

    move-wide v0, v15

    move-object/from16 v6, v21

    :goto_7
    const/16 v27, 0x0

    const/16 v28, 0x0

    :goto_8
    const/16 v29, 0x0

    goto/16 :goto_b

    :cond_7
    invoke-virtual {v7, v12}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v0

    if-ne v0, v14, :cond_9

    iget-object v5, v8, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    move-object/from16 v0, p6

    move-object/from16 v1, p7

    move/from16 v2, p4

    move/from16 v3, p5

    move-object v4, v12

    move-object/from16 v6, p0

    invoke-static/range {v0 .. v6}, Landroidx/media3/exoplayer/s1;->E0(Landroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;IZLjava/lang/Object;Landroidx/media3/common/m0;Landroidx/media3/common/m0;)Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_8

    invoke-virtual {v7, v10}, Landroidx/media3/common/m0;->a(Z)I

    move-result v0

    const/4 v4, 0x1

    goto :goto_9

    :cond_8
    invoke-virtual {v7, v0, v11}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v0

    iget v0, v0, Landroidx/media3/common/m0$b;->c:I

    const/4 v4, 0x1

    const/4 v4, 0x0

    :goto_9
    move-object/from16 v9, p6

    move v3, v0

    move/from16 v28, v4

    move-wide v0, v15

    move-object/from16 v6, v21

    const/16 v27, 0x0

    goto :goto_8

    :cond_9
    cmp-long v0, v15, v17

    if-nez v0, :cond_a

    invoke-virtual {v7, v12, v11}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v0

    iget v0, v0, Landroidx/media3/common/m0$b;->c:I

    goto :goto_6

    :cond_a
    if-eqz v13, :cond_c

    iget-object v0, v8, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    move-object/from16 v6, v21

    iget-object v1, v6, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {v0, v1, v11}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    iget-object v0, v8, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget v1, v11, Landroidx/media3/common/m0$b;->c:I

    move-object/from16 v9, p6

    invoke-virtual {v0, v1, v9}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object v0

    iget v0, v0, Landroidx/media3/common/m0$c;->o:I

    iget-object v1, v8, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v2, v6, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {v1, v2}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v1

    if-ne v0, v1, :cond_b

    invoke-virtual/range {p7 .. p7}, Landroidx/media3/common/m0$b;->o()J

    move-result-wide v0

    add-long v4, v15, v0

    invoke-virtual {v7, v12, v11}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v0

    iget v3, v0, Landroidx/media3/common/m0$b;->c:I

    move-object/from16 v0, p0

    move-object/from16 v1, p6

    move-object/from16 v2, p7

    invoke-virtual/range {v0 .. v5}, Landroidx/media3/common/m0;->j(Landroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;IJ)Landroid/util/Pair;

    move-result-object v0

    iget-object v12, v0, Landroid/util/Pair;->first:Ljava/lang/Object;

    iget-object v0, v0, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Long;

    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    move-result-wide v0

    goto :goto_a

    :cond_b
    move-wide v0, v15

    :goto_a
    const/4 v3, -0x1

    const/16 v27, 0x0

    const/16 v28, 0x0

    const/16 v29, 0x1

    goto :goto_b

    :cond_c
    move-object/from16 v9, p6

    move-object/from16 v6, v21

    move-wide v0, v15

    const/4 v3, -0x1

    goto/16 :goto_7

    :goto_b
    if-eq v3, v14, :cond_d

    const-wide v4, -0x7fffffffffffffffL    # -4.9E-324

    move-object/from16 v0, p0

    move-object/from16 v1, p6

    move-object/from16 v2, p7

    invoke-virtual/range {v0 .. v5}, Landroidx/media3/common/m0;->j(Landroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;IJ)Landroid/util/Pair;

    move-result-object v0

    iget-object v12, v0, Landroid/util/Pair;->first:Ljava/lang/Object;

    iget-object v0, v0, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Long;

    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    move-result-wide v0

    move-object/from16 v2, p3

    move-wide/from16 v25, v17

    goto :goto_c

    :cond_d
    move-object/from16 v2, p3

    move-wide/from16 v25, v0

    :goto_c
    invoke-virtual {v2, v7, v12, v0, v1}, Landroidx/media3/exoplayer/c2;->F(Landroidx/media3/common/m0;Ljava/lang/Object;J)Landroidx/media3/exoplayer/source/l$b;

    move-result-object v2

    iget v3, v2, Landroidx/media3/exoplayer/source/l$b;->e:I

    if-eq v3, v14, :cond_f

    iget v4, v6, Landroidx/media3/exoplayer/source/l$b;->e:I

    if-eq v4, v14, :cond_e

    if-lt v3, v4, :cond_e

    goto :goto_d

    :cond_e
    const/4 v3, 0x1

    const/4 v3, 0x0

    goto :goto_e

    :cond_f
    :goto_d
    const/4 v3, 0x1

    :goto_e
    iget-object v4, v6, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {v4, v12}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_10

    invoke-virtual {v6}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v4

    if-nez v4, :cond_10

    invoke-virtual {v2}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v4

    if-nez v4, :cond_10

    if-eqz v3, :cond_10

    goto :goto_f

    :cond_10
    const/16 v20, 0x0

    :goto_f
    invoke-virtual {v7, v12, v11}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v17

    move v12, v13

    move-object v13, v6

    move-object v3, v6

    move-wide v14, v15

    move-object/from16 v16, v2

    move-wide/from16 v18, v25

    invoke-static/range {v12 .. v19}, Landroidx/media3/exoplayer/s1;->R(ZLandroidx/media3/exoplayer/source/l$b;JLandroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0$b;J)Z

    move-result v4

    if-nez v20, :cond_11

    if-eqz v4, :cond_12

    :cond_11
    move-object v2, v3

    :cond_12
    invoke-virtual {v2}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v4

    if-eqz v4, :cond_13

    invoke-virtual {v2, v3}, Landroidx/media3/exoplayer/source/l$b;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_14

    iget-wide v0, v8, Landroidx/media3/exoplayer/s2;->r:J

    :cond_13
    :goto_10
    move-wide/from16 v23, v0

    goto :goto_11

    :cond_14
    iget-object v0, v2, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {v7, v0, v11}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    iget v0, v2, Landroidx/media3/exoplayer/source/l$b;->c:I

    iget v1, v2, Landroidx/media3/exoplayer/source/l$b;->b:I

    invoke-virtual {v11, v1}, Landroidx/media3/common/m0$b;->l(I)I

    move-result v1

    if-ne v0, v1, :cond_15

    invoke-virtual/range {p7 .. p7}, Landroidx/media3/common/m0$b;->g()J

    move-result-wide v0

    goto :goto_10

    :cond_15
    const-wide/16 v0, 0x0

    goto :goto_10

    :goto_11
    new-instance v0, Landroidx/media3/exoplayer/s1$g;

    move-object/from16 v21, v0

    move-object/from16 v22, v2

    invoke-direct/range {v21 .. v29}, Landroidx/media3/exoplayer/s1$g;-><init>(Landroidx/media3/exoplayer/source/l$b;JJZZZ)V

    return-object v0
.end method

.method public static D0(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/s1$h;ZIZLandroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;)Landroid/util/Pair;
    .locals 12
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/common/m0;",
            "Landroidx/media3/exoplayer/s1$h;",
            "ZIZ",
            "Landroidx/media3/common/m0$c;",
            "Landroidx/media3/common/m0$b;",
            ")",
            "Landroid/util/Pair<",
            "Ljava/lang/Object;",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation

    move-object v7, p0

    move-object v0, p1

    move-object/from16 v8, p6

    iget-object v1, v0, Landroidx/media3/exoplayer/s1$h;->a:Landroidx/media3/common/m0;

    invoke-virtual {p0}, Landroidx/media3/common/m0;->q()Z

    move-result v2

    const/4 v9, 0x1

    const/4 v9, 0x0

    if-eqz v2, :cond_0

    return-object v9

    :cond_0
    invoke-virtual {v1}, Landroidx/media3/common/m0;->q()Z

    move-result v2

    if-eqz v2, :cond_1

    move-object v10, v7

    goto :goto_0

    :cond_1
    move-object v10, v1

    :goto_0
    :try_start_0
    iget v4, v0, Landroidx/media3/exoplayer/s1$h;->b:I

    iget-wide v5, v0, Landroidx/media3/exoplayer/s1$h;->c:J

    move-object v1, v10

    move-object/from16 v2, p5

    move-object/from16 v3, p6

    invoke-virtual/range {v1 .. v6}, Landroidx/media3/common/m0;->j(Landroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;IJ)Landroid/util/Pair;

    move-result-object v1
    :try_end_0
    .catch Ljava/lang/IndexOutOfBoundsException; {:try_start_0 .. :try_end_0} :catch_0

    invoke-virtual {p0, v10}, Landroidx/media3/common/m0;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    return-object v1

    :cond_2
    iget-object v2, v1, Landroid/util/Pair;->first:Ljava/lang/Object;

    invoke-virtual {p0, v2}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v2

    const/4 v3, -0x1

    if-eq v2, v3, :cond_4

    iget-object v2, v1, Landroid/util/Pair;->first:Ljava/lang/Object;

    invoke-virtual {v10, v2, v8}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v2

    iget-boolean v2, v2, Landroidx/media3/common/m0$b;->f:Z

    if-eqz v2, :cond_3

    iget v2, v8, Landroidx/media3/common/m0$b;->c:I

    move-object/from16 v11, p5

    invoke-virtual {v10, v2, v11}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object v2

    iget v2, v2, Landroidx/media3/common/m0$c;->o:I

    iget-object v3, v1, Landroid/util/Pair;->first:Ljava/lang/Object;

    invoke-virtual {v10, v3}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v3

    if-ne v2, v3, :cond_3

    iget-object v1, v1, Landroid/util/Pair;->first:Ljava/lang/Object;

    invoke-virtual {p0, v1, v8}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v1

    iget v3, v1, Landroidx/media3/common/m0$b;->c:I

    iget-wide v4, v0, Landroidx/media3/exoplayer/s1$h;->c:J

    move-object v0, p0

    move-object/from16 v1, p5

    move-object/from16 v2, p6

    invoke-virtual/range {v0 .. v5}, Landroidx/media3/common/m0;->j(Landroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;IJ)Landroid/util/Pair;

    move-result-object v1

    :cond_3
    return-object v1

    :cond_4
    move-object/from16 v11, p5

    if-eqz p2, :cond_5

    iget-object v4, v1, Landroid/util/Pair;->first:Ljava/lang/Object;

    move-object/from16 v0, p5

    move-object/from16 v1, p6

    move v2, p3

    move/from16 v3, p4

    move-object v5, v10

    move-object v6, p0

    invoke-static/range {v0 .. v6}, Landroidx/media3/exoplayer/s1;->E0(Landroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;IZLjava/lang/Object;Landroidx/media3/common/m0;Landroidx/media3/common/m0;)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_5

    invoke-virtual {p0, v0, v8}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v0

    iget v3, v0, Landroidx/media3/common/m0$b;->c:I

    const-wide v4, -0x7fffffffffffffffL    # -4.9E-324

    move-object v0, p0

    move-object/from16 v1, p5

    move-object/from16 v2, p6

    invoke-virtual/range {v0 .. v5}, Landroidx/media3/common/m0;->j(Landroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;IJ)Landroid/util/Pair;

    move-result-object v0

    return-object v0

    :catch_0
    :cond_5
    return-object v9
.end method

.method public static E0(Landroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;IZLjava/lang/Object;Landroidx/media3/common/m0;Landroidx/media3/common/m0;)Ljava/lang/Object;
    .locals 9
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    invoke-virtual {p5, p4}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result p4

    invoke-virtual {p5}, Landroidx/media3/common/m0;->i()I

    move-result v0

    const/4 v1, -0x1

    const/4 v2, 0x1

    const/4 v2, 0x0

    move v4, p4

    const/4 p4, -0x1

    :goto_0
    if-ge v2, v0, :cond_1

    if-ne p4, v1, :cond_1

    move-object v3, p5

    move-object v5, p1

    move-object v6, p0

    move v7, p2

    move v8, p3

    invoke-virtual/range {v3 .. v8}, Landroidx/media3/common/m0;->d(ILandroidx/media3/common/m0$b;Landroidx/media3/common/m0$c;IZ)I

    move-result v4

    if-ne v4, v1, :cond_0

    goto :goto_1

    :cond_0
    invoke-virtual {p5, v4}, Landroidx/media3/common/m0;->m(I)Ljava/lang/Object;

    move-result-object p4

    invoke-virtual {p6, p4}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result p4

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    :goto_1
    if-ne p4, v1, :cond_2

    const/4 p0, 0x1

    const/4 p0, 0x0

    goto :goto_2

    :cond_2
    invoke-virtual {p6, p4}, Landroidx/media3/common/m0;->m(I)Ljava/lang/Object;

    move-result-object p0

    :goto_2
    return-object p0
.end method

.method public static R(ZLandroidx/media3/exoplayer/source/l$b;JLandroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0$b;J)Z
    .locals 1

    const/4 v0, 0x1

    const/4 v0, 0x0

    if-nez p0, :cond_3

    cmp-long p0, p2, p6

    if-nez p0, :cond_3

    iget-object p0, p1, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object p2, p4, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {p0, p2}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result p0

    if-nez p0, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result p0

    const/4 p2, 0x1

    if-eqz p0, :cond_2

    iget p0, p1, Landroidx/media3/exoplayer/source/l$b;->b:I

    invoke-virtual {p5, p0}, Landroidx/media3/common/m0$b;->s(I)Z

    move-result p0

    if-eqz p0, :cond_2

    iget p0, p1, Landroidx/media3/exoplayer/source/l$b;->b:I

    iget p3, p1, Landroidx/media3/exoplayer/source/l$b;->c:I

    invoke-virtual {p5, p0, p3}, Landroidx/media3/common/m0$b;->h(II)I

    move-result p0

    const/4 p3, 0x4

    if-eq p0, p3, :cond_1

    iget p0, p1, Landroidx/media3/exoplayer/source/l$b;->b:I

    iget p1, p1, Landroidx/media3/exoplayer/source/l$b;->c:I

    invoke-virtual {p5, p0, p1}, Landroidx/media3/common/m0$b;->h(II)I

    move-result p0

    const/4 p1, 0x2

    if-eq p0, p1, :cond_1

    const/4 v0, 0x1

    :cond_1
    return v0

    :cond_2
    invoke-virtual {p4}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result p0

    if-eqz p0, :cond_3

    iget p0, p4, Landroidx/media3/exoplayer/source/l$b;->b:I

    invoke-virtual {p5, p0}, Landroidx/media3/common/m0$b;->s(I)Z

    move-result p0

    if-eqz p0, :cond_3

    const/4 v0, 0x1

    :cond_3
    :goto_0
    return v0
.end method

.method public static T(Landroidx/media3/exoplayer/w2;)Z
    .locals 0

    invoke-interface {p0}, Landroidx/media3/exoplayer/w2;->getState()I

    move-result p0

    if-eqz p0, :cond_0

    const/4 p0, 0x1

    goto :goto_0

    :cond_0
    const/4 p0, 0x1

    const/4 p0, 0x0

    :goto_0
    return p0
.end method

.method public static V(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/m0$b;)Z
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object p0, p0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {p0}, Landroidx/media3/common/m0;->q()Z

    move-result v1

    if-nez v1, :cond_1

    iget-object v0, v0, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {p0, v0, p1}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object p0

    iget-boolean p0, p0, Landroidx/media3/common/m0$b;->f:Z

    if-eqz p0, :cond_0

    goto :goto_0

    :cond_0
    const/4 p0, 0x1

    const/4 p0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p0, 0x1

    :goto_1
    return p0
.end method

.method public static synthetic e(Landroidx/media3/exoplayer/s1;Landroidx/media3/exoplayer/t2;)V
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/s1;->X(Landroidx/media3/exoplayer/t2;)V

    return-void
.end method

.method public static synthetic f(Landroidx/media3/exoplayer/s1;Landroidx/media3/exoplayer/a2;J)Landroidx/media3/exoplayer/z1;
    .locals 0

    invoke-virtual {p0, p1, p2, p3}, Landroidx/media3/exoplayer/s1;->p(Landroidx/media3/exoplayer/a2;J)Landroidx/media3/exoplayer/z1;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic i(Landroidx/media3/exoplayer/s1;)Ljava/lang/Boolean;
    .locals 0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->W()Ljava/lang/Boolean;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic j(Landroidx/media3/exoplayer/s1;Z)Z
    .locals 0

    iput-boolean p1, p0, Landroidx/media3/exoplayer/s1;->J:Z

    return p1
.end method

.method public static synthetic k(Landroidx/media3/exoplayer/s1;)Le2/j;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    return-object p0
.end method

.method public static z0(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/s1$d;Landroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;)V
    .locals 4

    iget-object v0, p1, Landroidx/media3/exoplayer/s1$d;->d:Ljava/lang/Object;

    invoke-virtual {p0, v0, p3}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v0

    iget v0, v0, Landroidx/media3/common/m0$b;->c:I

    invoke-virtual {p0, v0, p2}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object p2

    iget p2, p2, Landroidx/media3/common/m0$c;->p:I

    const/4 v0, 0x1

    invoke-virtual {p0, p2, p3, v0}, Landroidx/media3/common/m0;->g(ILandroidx/media3/common/m0$b;Z)Landroidx/media3/common/m0$b;

    move-result-object p0

    iget-object p0, p0, Landroidx/media3/common/m0$b;->b:Ljava/lang/Object;

    iget-wide v0, p3, Landroidx/media3/common/m0$b;->d:J

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long p3, v0, v2

    if-eqz p3, :cond_0

    const-wide/16 v2, 0x1

    sub-long/2addr v0, v2

    goto :goto_0

    :cond_0
    const-wide v0, 0x7fffffffffffffffL

    :goto_0
    invoke-virtual {p1, p2, v0, v1, p0}, Landroidx/media3/exoplayer/s1$d;->b(IJLjava/lang/Object;)V

    return-void
.end method


# virtual methods
.method public final B(Landroidx/media3/common/m0;Ljava/lang/Object;J)J
    .locals 4

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    invoke-virtual {p1, p2, v0}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object p2

    iget p2, p2, Landroidx/media3/common/m0$b;->c:I

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    invoke-virtual {p1, p2, v0}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    iget-wide v0, p1, Landroidx/media3/common/m0$c;->f:J

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long p2, v0, v2

    if-eqz p2, :cond_1

    invoke-virtual {p1}, Landroidx/media3/common/m0$c;->f()Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    iget-boolean p2, p1, Landroidx/media3/common/m0$c;->i:Z

    if-nez p2, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Landroidx/media3/common/m0$c;->a()J

    move-result-wide p1

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    iget-wide v0, v0, Landroidx/media3/common/m0$c;->f:J

    sub-long/2addr p1, v0

    invoke-static {p1, p2}, Le2/u0;->S0(J)J

    move-result-wide p1

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    invoke-virtual {v0}, Landroidx/media3/common/m0$b;->o()J

    move-result-wide v0

    add-long/2addr p3, v0

    sub-long/2addr p1, p3

    return-wide p1

    :cond_1
    :goto_0
    return-wide v2
.end method

.method public final B0(Landroidx/media3/common/m0;Landroidx/media3/common/m0;)V
    .locals 9

    invoke-virtual {p1}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p2}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    add-int/lit8 v0, v0, -0x1

    :goto_0
    if-ltz v0, :cond_2

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    move-object v2, v1

    check-cast v2, Landroidx/media3/exoplayer/s1$d;

    iget v5, p0, Landroidx/media3/exoplayer/s1;->G:I

    iget-boolean v6, p0, Landroidx/media3/exoplayer/s1;->H:Z

    iget-object v7, p0, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    iget-object v8, p0, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    move-object v3, p1

    move-object v4, p2

    invoke-static/range {v2 .. v8}, Landroidx/media3/exoplayer/s1;->A0(Landroidx/media3/exoplayer/s1$d;Landroidx/media3/common/m0;Landroidx/media3/common/m0;IZLandroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;)Z

    move-result v1

    if-nez v1, :cond_1

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/exoplayer/s1$d;

    iget-object v1, v1, Landroidx/media3/exoplayer/s1$d;->a:Landroidx/media3/exoplayer/t2;

    const/4 v2, 0x1

    const/4 v2, 0x0

    invoke-virtual {v1, v2}, Landroidx/media3/exoplayer/t2;->k(Z)V

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    :cond_1
    add-int/lit8 v0, v0, -0x1

    goto :goto_0

    :cond_2
    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-static {p1}, Ljava/util/Collections;->sort(Ljava/util/List;)V

    return-void
.end method

.method public final C()J
    .locals 9

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->s()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    if-nez v0, :cond_0

    const-wide/16 v0, 0x0

    return-wide v0

    :cond_0
    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->l()J

    move-result-wide v1

    iget-boolean v3, v0, Landroidx/media3/exoplayer/z1;->d:Z

    if-nez v3, :cond_1

    return-wide v1

    :cond_1
    const/4 v3, 0x1

    const/4 v3, 0x0

    :goto_0
    iget-object v4, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v5, v4

    if-ge v3, v5, :cond_5

    aget-object v4, v4, v3

    invoke-static {v4}, Landroidx/media3/exoplayer/s1;->T(Landroidx/media3/exoplayer/w2;)Z

    move-result v4

    if-eqz v4, :cond_4

    iget-object v4, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    aget-object v4, v4, v3

    invoke-interface {v4}, Landroidx/media3/exoplayer/w2;->getStream()Lu2/e0;

    move-result-object v4

    iget-object v5, v0, Landroidx/media3/exoplayer/z1;->c:[Lu2/e0;

    aget-object v5, v5, v3

    if-eq v4, v5, :cond_2

    goto :goto_1

    :cond_2
    iget-object v4, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    aget-object v4, v4, v3

    invoke-interface {v4}, Landroidx/media3/exoplayer/w2;->d()J

    move-result-wide v4

    const-wide/high16 v6, -0x8000000000000000L

    cmp-long v8, v4, v6

    if-nez v8, :cond_3

    return-wide v6

    :cond_3
    invoke-static {v4, v5, v1, v2}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v1

    :cond_4
    :goto_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_5
    return-wide v1
.end method

.method public final D(Landroidx/media3/common/m0;)Landroid/util/Pair;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/common/m0;",
            ")",
            "Landroid/util/Pair<",
            "Landroidx/media3/exoplayer/source/l$b;",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation

    invoke-virtual {p1}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    const-wide/16 v1, 0x0

    if-eqz v0, :cond_0

    invoke-static {}, Landroidx/media3/exoplayer/s2;->l()Landroidx/media3/exoplayer/source/l$b;

    move-result-object p1

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    invoke-static {p1, v0}, Landroid/util/Pair;->create(Ljava/lang/Object;Ljava/lang/Object;)Landroid/util/Pair;

    move-result-object p1

    return-object p1

    :cond_0
    iget-boolean v0, p0, Landroidx/media3/exoplayer/s1;->H:Z

    invoke-virtual {p1, v0}, Landroidx/media3/common/m0;->a(Z)I

    move-result v6

    iget-object v4, p0, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    iget-object v5, p0, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    const-wide v7, -0x7fffffffffffffffL    # -4.9E-324

    move-object v3, p1

    invoke-virtual/range {v3 .. v8}, Landroidx/media3/common/m0;->j(Landroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;IJ)Landroid/util/Pair;

    move-result-object v0

    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    iget-object v4, v0, Landroid/util/Pair;->first:Ljava/lang/Object;

    invoke-virtual {v3, p1, v4, v1, v2}, Landroidx/media3/exoplayer/c2;->F(Landroidx/media3/common/m0;Ljava/lang/Object;J)Landroidx/media3/exoplayer/source/l$b;

    move-result-object v3

    iget-object v0, v0, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Long;

    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    move-result-wide v4

    invoke-virtual {v3}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v0

    if-eqz v0, :cond_2

    iget-object v0, v3, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v4, p0, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    invoke-virtual {p1, v0, v4}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    iget p1, v3, Landroidx/media3/exoplayer/source/l$b;->c:I

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    iget v4, v3, Landroidx/media3/exoplayer/source/l$b;->b:I

    invoke-virtual {v0, v4}, Landroidx/media3/common/m0$b;->l(I)I

    move-result v0

    if-ne p1, v0, :cond_1

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    invoke-virtual {p1}, Landroidx/media3/common/m0$b;->g()J

    move-result-wide v1

    :cond_1
    move-wide v4, v1

    :cond_2
    invoke-static {v4, v5}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object p1

    invoke-static {v3, p1}, Landroid/util/Pair;->create(Ljava/lang/Object;Ljava/lang/Object;)Landroid/util/Pair;

    move-result-object p1

    return-object p1
.end method

.method public E()Landroid/os/Looper;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->k:Landroid/os/Looper;

    return-object v0
.end method

.method public final F()J
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v0, v0, Landroidx/media3/exoplayer/s2;->p:J

    invoke-virtual {p0, v0, v1}, Landroidx/media3/exoplayer/s1;->G(J)J

    move-result-wide v0

    return-wide v0
.end method

.method public final F0(JJ)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/4 v1, 0x2

    add-long/2addr p1, p3

    invoke-interface {v0, v1, p1, p2}, Le2/j;->sendEmptyMessageAtTime(IJ)Z

    return-void
.end method

.method public final G(J)J
    .locals 5

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->l()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    const-wide/16 v1, 0x0

    if-nez v0, :cond_0

    return-wide v1

    :cond_0
    iget-wide v3, p0, Landroidx/media3/exoplayer/s1;->N:J

    invoke-virtual {v0, v3, v4}, Landroidx/media3/exoplayer/z1;->y(J)J

    move-result-wide v3

    sub-long/2addr p1, v3

    invoke-static {v1, v2, p1, p2}, Ljava/lang/Math;->max(JJ)J

    move-result-wide p1

    return-wide p1
.end method

.method public G0(Landroidx/media3/common/m0;IJ)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    new-instance v1, Landroidx/media3/exoplayer/s1$h;

    invoke-direct {v1, p1, p2, p3, p4}, Landroidx/media3/exoplayer/s1$h;-><init>(Landroidx/media3/common/m0;IJ)V

    const/4 p1, 0x3

    invoke-interface {v0, p1, v1}, Le2/j;->obtainMessage(ILjava/lang/Object;)Le2/j$a;

    move-result-object p1

    invoke-interface {p1}, Le2/j$a;->a()V

    return-void
.end method

.method public final H(Landroidx/media3/exoplayer/source/k;)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/c2;->y(Landroidx/media3/exoplayer/source/k;)Z

    move-result p1

    if-nez p1, :cond_0

    return-void

    :cond_0
    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    iget-wide v0, p0, Landroidx/media3/exoplayer/s1;->N:J

    invoke-virtual {p1, v0, v1}, Landroidx/media3/exoplayer/c2;->C(J)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->Y()V

    return-void
.end method

.method public final H0(Z)V
    .locals 11
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    iget-object v0, v0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v0, v0, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v3, v1, Landroidx/media3/exoplayer/s2;->r:J

    const/4 v5, 0x1

    const/4 v6, 0x1

    const/4 v6, 0x0

    move-object v1, p0

    move-object v2, v0

    invoke-virtual/range {v1 .. v6}, Landroidx/media3/exoplayer/s1;->K0(Landroidx/media3/exoplayer/source/l$b;JZZ)J

    move-result-wide v3

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v1, v1, Landroidx/media3/exoplayer/s2;->r:J

    cmp-long v5, v3, v1

    if-eqz v5, :cond_0

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v5, v1, Landroidx/media3/exoplayer/s2;->c:J

    iget-wide v7, v1, Landroidx/media3/exoplayer/s2;->d:J

    const/4 v10, 0x5

    move-object v1, p0

    move-object v2, v0

    move v9, p1

    invoke-virtual/range {v1 .. v10}, Landroidx/media3/exoplayer/s1;->O(Landroidx/media3/exoplayer/source/l$b;JJJZI)Landroidx/media3/exoplayer/s2;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    :cond_0
    return-void
.end method

.method public final I(Ljava/io/IOException;I)V
    .locals 1

    invoke-static {p1, p2}, Landroidx/media3/exoplayer/ExoPlaybackException;->createForSource(Ljava/io/IOException;I)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object p1

    iget-object p2, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {p2}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object p2

    if-eqz p2, :cond_0

    iget-object p2, p2, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object p2, p2, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p1, p2}, Landroidx/media3/exoplayer/ExoPlaybackException;->copyWithMediaPeriodId(Landroidx/media3/exoplayer/source/l$b;)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object p1

    :cond_0
    const-string p2, "ExoPlayerImplInternal"

    const-string v0, "Playback error"

    invoke-static {p2, v0, p1}, Le2/o;->d(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    const/4 p2, 0x1

    const/4 p2, 0x0

    invoke-virtual {p0, p2, p2}, Landroidx/media3/exoplayer/s1;->o1(ZZ)V

    iget-object p2, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual {p2, p1}, Landroidx/media3/exoplayer/s2;->f(Landroidx/media3/exoplayer/ExoPlaybackException;)Landroidx/media3/exoplayer/s2;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    return-void
.end method

.method public final I0(Landroidx/media3/exoplayer/s1$h;)V
    .locals 19
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    move-object/from16 v11, p0

    move-object/from16 v0, p1

    iget-object v1, v11, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    const/4 v8, 0x1

    invoke-virtual {v1, v8}, Landroidx/media3/exoplayer/s1$e;->b(I)V

    iget-object v1, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v1, v1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    const/4 v3, 0x1

    iget v4, v11, Landroidx/media3/exoplayer/s1;->G:I

    iget-boolean v5, v11, Landroidx/media3/exoplayer/s1;->H:Z

    iget-object v6, v11, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    iget-object v7, v11, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    move-object/from16 v2, p1

    invoke-static/range {v1 .. v7}, Landroidx/media3/exoplayer/s1;->D0(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/s1$h;ZIZLandroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;)Landroid/util/Pair;

    move-result-object v1

    const-wide/16 v2, 0x0

    const-wide v4, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v6, 0x1

    const/4 v6, 0x0

    if-nez v1, :cond_0

    iget-object v7, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v7, v7, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v11, v7}, Landroidx/media3/exoplayer/s1;->D(Landroidx/media3/common/m0;)Landroid/util/Pair;

    move-result-object v7

    iget-object v9, v7, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v9, Landroidx/media3/exoplayer/source/l$b;

    iget-object v7, v7, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v7, Ljava/lang/Long;

    invoke-virtual {v7}, Ljava/lang/Long;->longValue()J

    move-result-wide v12

    iget-object v7, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v7, v7, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v7}, Landroidx/media3/common/m0;->q()Z

    move-result v7

    xor-int/2addr v7, v8

    move v10, v7

    move-wide/from16 v17, v4

    :goto_0
    move-wide v4, v12

    move-wide/from16 v12, v17

    goto :goto_4

    :cond_0
    iget-object v7, v1, Landroid/util/Pair;->first:Ljava/lang/Object;

    iget-object v9, v1, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v9, Ljava/lang/Long;

    invoke-virtual {v9}, Ljava/lang/Long;->longValue()J

    move-result-wide v12

    iget-wide v9, v0, Landroidx/media3/exoplayer/s1$h;->c:J

    cmp-long v14, v9, v4

    if-nez v14, :cond_1

    move-wide v9, v4

    goto :goto_1

    :cond_1
    move-wide v9, v12

    :goto_1
    iget-object v14, v11, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    iget-object v15, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v15, v15, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v14, v15, v7, v12, v13}, Landroidx/media3/exoplayer/c2;->F(Landroidx/media3/common/m0;Ljava/lang/Object;J)Landroidx/media3/exoplayer/source/l$b;

    move-result-object v7

    invoke-virtual {v7}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v14

    if-eqz v14, :cond_3

    iget-object v4, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v4, v4, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v5, v7, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v12, v11, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    invoke-virtual {v4, v5, v12}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    iget-object v4, v11, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    iget v5, v7, Landroidx/media3/exoplayer/source/l$b;->b:I

    invoke-virtual {v4, v5}, Landroidx/media3/common/m0$b;->l(I)I

    move-result v4

    iget v5, v7, Landroidx/media3/exoplayer/source/l$b;->c:I

    if-ne v4, v5, :cond_2

    iget-object v4, v11, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    invoke-virtual {v4}, Landroidx/media3/common/m0$b;->g()J

    move-result-wide v4

    move-wide v12, v4

    goto :goto_2

    :cond_2
    move-wide v12, v2

    :goto_2
    move-wide v4, v12

    move-wide v12, v9

    const/4 v10, 0x1

    move-object v9, v7

    goto :goto_4

    :cond_3
    iget-wide v14, v0, Landroidx/media3/exoplayer/s1$h;->c:J

    cmp-long v16, v14, v4

    if-nez v16, :cond_4

    const/4 v4, 0x1

    goto :goto_3

    :cond_4
    const/4 v4, 0x1

    const/4 v4, 0x0

    :goto_3
    move-wide/from16 v17, v9

    move v10, v4

    move-object v9, v7

    goto :goto_0

    :goto_4
    :try_start_0
    iget-object v7, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v7, v7, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v7}, Landroidx/media3/common/m0;->q()Z

    move-result v7

    if-eqz v7, :cond_5

    iput-object v0, v11, Landroidx/media3/exoplayer/s1;->M:Landroidx/media3/exoplayer/s1$h;

    goto :goto_5

    :catchall_0
    move-exception v0

    move-wide v7, v4

    goto/16 :goto_a

    :cond_5
    const/4 v0, 0x4

    if-nez v1, :cond_7

    iget-object v1, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget v1, v1, Landroidx/media3/exoplayer/s2;->e:I

    if-eq v1, v8, :cond_6

    invoke-virtual {v11, v0}, Landroidx/media3/exoplayer/s1;->g1(I)V

    :cond_6
    invoke-virtual {v11, v6, v8, v6, v8}, Landroidx/media3/exoplayer/s1;->w0(ZZZZ)V

    :goto_5
    move-wide v7, v4

    goto/16 :goto_9

    :cond_7
    iget-object v1, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v1, v1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v9, v1}, Landroidx/media3/exoplayer/source/l$b;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_a

    iget-object v1, v11, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v1

    if-eqz v1, :cond_8

    iget-boolean v7, v1, Landroidx/media3/exoplayer/z1;->d:Z

    if-eqz v7, :cond_8

    cmp-long v7, v4, v2

    if-eqz v7, :cond_8

    iget-object v1, v1, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    iget-object v2, v11, Landroidx/media3/exoplayer/s1;->x:Landroidx/media3/exoplayer/b3;

    invoke-interface {v1, v4, v5, v2}, Landroidx/media3/exoplayer/source/k;->b(JLandroidx/media3/exoplayer/b3;)J

    move-result-wide v1

    goto :goto_6

    :cond_8
    move-wide v1, v4

    :goto_6
    invoke-static {v1, v2}, Le2/u0;->B1(J)J

    move-result-wide v14

    iget-object v3, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v6, v3, Landroidx/media3/exoplayer/s2;->r:J

    invoke-static {v6, v7}, Le2/u0;->B1(J)J

    move-result-wide v6

    cmp-long v3, v14, v6

    if-nez v3, :cond_b

    iget-object v3, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget v6, v3, Landroidx/media3/exoplayer/s2;->e:I

    const/4 v7, 0x2

    if-eq v6, v7, :cond_9

    const/4 v7, 0x3

    if-ne v6, v7, :cond_b

    :cond_9
    iget-wide v7, v3, Landroidx/media3/exoplayer/s2;->r:J
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    const/4 v0, 0x2

    move-object/from16 v1, p0

    move-object v2, v9

    move-wide v3, v7

    move-wide v5, v12

    move v9, v10

    move v10, v0

    invoke-virtual/range {v1 .. v10}, Landroidx/media3/exoplayer/s1;->O(Landroidx/media3/exoplayer/source/l$b;JJJZI)Landroidx/media3/exoplayer/s2;

    move-result-object v0

    iput-object v0, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    return-void

    :cond_a
    move-wide v1, v4

    :cond_b
    :try_start_1
    iget-object v3, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget v3, v3, Landroidx/media3/exoplayer/s2;->e:I

    if-ne v3, v0, :cond_c

    const/4 v0, 0x1

    goto :goto_7

    :cond_c
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_7
    invoke-virtual {v11, v9, v1, v2, v0}, Landroidx/media3/exoplayer/s1;->J0(Landroidx/media3/exoplayer/source/l$b;JZ)J

    move-result-wide v14
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    cmp-long v0, v4, v14

    if-eqz v0, :cond_d

    goto :goto_8

    :cond_d
    const/4 v8, 0x1

    const/4 v8, 0x0

    :goto_8
    or-int/2addr v10, v8

    :try_start_2
    iget-object v0, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v4, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v5, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    const/4 v8, 0x1

    move-object/from16 v1, p0

    move-object v2, v4

    move-object v3, v9

    move-wide v6, v12

    invoke-virtual/range {v1 .. v8}, Landroidx/media3/exoplayer/s1;->w1(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JZ)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    move-wide v7, v14

    :goto_9
    const/4 v0, 0x2

    move-object/from16 v1, p0

    move-object v2, v9

    move-wide v3, v7

    move-wide v5, v12

    move v9, v10

    move v10, v0

    invoke-virtual/range {v1 .. v10}, Landroidx/media3/exoplayer/s1;->O(Landroidx/media3/exoplayer/source/l$b;JJJZI)Landroidx/media3/exoplayer/s2;

    move-result-object v0

    iput-object v0, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    return-void

    :catchall_1
    move-exception v0

    move-wide v7, v14

    :goto_a
    const/4 v14, 0x2

    move-object/from16 v1, p0

    move-object v2, v9

    move-wide v3, v7

    move-wide v5, v12

    move v9, v10

    move v10, v14

    invoke-virtual/range {v1 .. v10}, Landroidx/media3/exoplayer/s1;->O(Landroidx/media3/exoplayer/source/l$b;JJJZI)Landroidx/media3/exoplayer/s2;

    move-result-object v1

    iput-object v1, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    throw v0
.end method

.method public final J(Z)V
    .locals 5

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->l()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    if-nez v0, :cond_0

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v1, v1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    goto :goto_0

    :cond_0
    iget-object v1, v0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v1, v1, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    :goto_0
    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v2, v2, Landroidx/media3/exoplayer/s2;->k:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v2, v1}, Landroidx/media3/exoplayer/source/l$b;->equals(Ljava/lang/Object;)Z

    move-result v2

    xor-int/lit8 v2, v2, 0x1

    if-eqz v2, :cond_1

    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual {v3, v1}, Landroidx/media3/exoplayer/s2;->c(Landroidx/media3/exoplayer/source/l$b;)Landroidx/media3/exoplayer/s2;

    move-result-object v1

    iput-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    :cond_1
    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    if-nez v0, :cond_2

    iget-wide v3, v1, Landroidx/media3/exoplayer/s2;->r:J

    goto :goto_1

    :cond_2
    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->i()J

    move-result-wide v3

    :goto_1
    iput-wide v3, v1, Landroidx/media3/exoplayer/s2;->p:J

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->F()J

    move-result-wide v3

    iput-wide v3, v1, Landroidx/media3/exoplayer/s2;->q:J

    if-nez v2, :cond_3

    if-eqz p1, :cond_4

    :cond_3
    if-eqz v0, :cond_4

    iget-boolean p1, v0, Landroidx/media3/exoplayer/z1;->d:Z

    if-eqz p1, :cond_4

    iget-object p1, v0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object p1, p1, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->n()Lu2/k0;

    move-result-object v1

    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->o()Lx2/f0;

    move-result-object v0

    invoke-virtual {p0, p1, v1, v0}, Landroidx/media3/exoplayer/s1;->r1(Landroidx/media3/exoplayer/source/l$b;Lu2/k0;Lx2/f0;)V

    :cond_4
    return-void
.end method

.method public final J0(Landroidx/media3/exoplayer/source/l$b;JZ)J
    .locals 7
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/c2;->s()Landroidx/media3/exoplayer/z1;

    move-result-object v1

    if-eq v0, v1, :cond_0

    const/4 v0, 0x1

    const/4 v5, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    const/4 v5, 0x1

    const/4 v5, 0x0

    :goto_0
    move-object v1, p0

    move-object v2, p1

    move-wide v3, p2

    move v6, p4

    invoke-virtual/range {v1 .. v6}, Landroidx/media3/exoplayer/s1;->K0(Landroidx/media3/exoplayer/source/l$b;JZZ)J

    move-result-wide p1

    return-wide p1
.end method

.method public final K(Landroidx/media3/common/m0;Z)V
    .locals 27
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    move-object/from16 v11, p0

    move-object/from16 v12, p1

    iget-object v2, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v3, v11, Landroidx/media3/exoplayer/s1;->M:Landroidx/media3/exoplayer/s1$h;

    iget-object v4, v11, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    iget v5, v11, Landroidx/media3/exoplayer/s1;->G:I

    iget-boolean v6, v11, Landroidx/media3/exoplayer/s1;->H:Z

    iget-object v7, v11, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    iget-object v8, v11, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    move-object/from16 v1, p1

    invoke-static/range {v1 .. v8}, Landroidx/media3/exoplayer/s1;->C0(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/s2;Landroidx/media3/exoplayer/s1$h;Landroidx/media3/exoplayer/c2;IZLandroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;)Landroidx/media3/exoplayer/s1$g;

    move-result-object v7

    iget-object v9, v7, Landroidx/media3/exoplayer/s1$g;->a:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v13, v7, Landroidx/media3/exoplayer/s1$g;->c:J

    iget-boolean v0, v7, Landroidx/media3/exoplayer/s1$g;->d:Z

    iget-wide v5, v7, Landroidx/media3/exoplayer/s1$g;->b:J

    iget-object v1, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v1, v1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v1, v9}, Landroidx/media3/exoplayer/source/l$b;->equals(Ljava/lang/Object;)Z

    move-result v1

    const/4 v10, 0x1

    const/4 v15, 0x1

    const/4 v15, 0x0

    if-eqz v1, :cond_1

    iget-object v1, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v1, v1, Landroidx/media3/exoplayer/s2;->r:J

    cmp-long v3, v5, v1

    if-eqz v3, :cond_0

    goto :goto_0

    :cond_0
    const/16 v16, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/16 v16, 0x1

    :goto_1
    const/4 v8, 0x1

    const/4 v8, 0x0

    const/16 v17, 0x3

    const-wide v18, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v4, 0x4

    :try_start_0
    iget-boolean v1, v7, Landroidx/media3/exoplayer/s1$g;->e:Z

    if-eqz v1, :cond_3

    iget-object v1, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget v1, v1, Landroidx/media3/exoplayer/s2;->e:I

    if-eq v1, v10, :cond_2

    invoke-virtual {v11, v4}, Landroidx/media3/exoplayer/s1;->g1(I)V

    goto :goto_2

    :catchall_0
    move-exception v0

    move-object v15, v8

    const/4 v10, -0x1

    const/16 v20, 0x4

    goto/16 :goto_b

    :cond_2
    :goto_2
    invoke-virtual {v11, v15, v15, v15, v10}, Landroidx/media3/exoplayer/s1;->w0(ZZZZ)V

    :cond_3
    iget-object v1, v11, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v2, v1

    const/4 v3, 0x1

    const/4 v3, 0x0

    :goto_3
    if-ge v3, v2, :cond_4

    aget-object v4, v1, v3

    invoke-interface {v4, v12}, Landroidx/media3/exoplayer/w2;->k(Landroidx/media3/common/m0;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    add-int/lit8 v3, v3, 0x1

    const/4 v4, 0x4

    goto :goto_3

    :cond_4
    if-nez v16, :cond_6

    :try_start_1
    iget-object v1, v11, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    iget-wide v3, v11, Landroidx/media3/exoplayer/s1;->N:J

    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/s1;->C()J

    move-result-wide v22
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_2

    move-object/from16 v2, p1

    const/4 v10, -0x1

    const/16 v20, 0x4

    move-wide/from16 v25, v5

    move-wide/from16 v5, v22

    :try_start_2
    invoke-virtual/range {v1 .. v6}, Landroidx/media3/exoplayer/c2;->J(Landroidx/media3/common/m0;JJ)Z

    move-result v0

    if-nez v0, :cond_5

    invoke-virtual {v11, v15}, Landroidx/media3/exoplayer/s1;->H0(Z)V

    :cond_5
    move-wide/from16 v5, v25

    goto :goto_6

    :catchall_1
    move-exception v0

    move-object v15, v8

    move-wide/from16 v5, v25

    goto/16 :goto_b

    :catchall_2
    move-exception v0

    move-wide/from16 v25, v5

    const/4 v10, -0x1

    const/16 v20, 0x4

    :goto_4
    move-object v15, v8

    goto/16 :goto_b

    :cond_6
    move-wide/from16 v25, v5

    const/4 v10, -0x1

    const/16 v20, 0x4

    invoke-virtual/range {p1 .. p1}, Landroidx/media3/common/m0;->q()Z

    move-result v1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    if-nez v1, :cond_5

    :try_start_3
    iget-object v1, v11, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v1
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_4

    :goto_5
    if-eqz v1, :cond_8

    :try_start_4
    iget-object v2, v1, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v2, v2, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v2, v9}, Landroidx/media3/exoplayer/source/l$b;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_7

    iget-object v2, v11, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    iget-object v3, v1, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    invoke-virtual {v2, v12, v3}, Landroidx/media3/exoplayer/c2;->t(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/a2;)Landroidx/media3/exoplayer/a2;

    move-result-object v2

    iput-object v2, v1, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/z1;->A()V

    :cond_7
    invoke-virtual {v1}, Landroidx/media3/exoplayer/z1;->j()Landroidx/media3/exoplayer/z1;

    move-result-object v1
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    goto :goto_5

    :cond_8
    move-wide/from16 v5, v25

    :try_start_5
    invoke-virtual {v11, v9, v5, v6, v0}, Landroidx/media3/exoplayer/s1;->J0(Landroidx/media3/exoplayer/source/l$b;JZ)J

    move-result-wide v0
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_3

    move-wide/from16 v21, v0

    goto :goto_7

    :catchall_3
    move-exception v0

    goto :goto_4

    :catchall_4
    move-exception v0

    move-wide/from16 v5, v25

    goto :goto_4

    :goto_6
    move-wide/from16 v21, v5

    :goto_7
    iget-object v0, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v4, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v5, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-boolean v0, v7, Landroidx/media3/exoplayer/s1$g;->f:Z

    if-eqz v0, :cond_9

    move-wide/from16 v6, v21

    goto :goto_8

    :cond_9
    move-wide/from16 v6, v18

    :goto_8
    const/4 v0, 0x1

    const/4 v0, 0x0

    move-object/from16 v1, p0

    move-object/from16 v2, p1

    move-object v3, v9

    move-object v15, v8

    move v8, v0

    invoke-virtual/range {v1 .. v8}, Landroidx/media3/exoplayer/s1;->w1(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JZ)V

    if-nez v16, :cond_a

    iget-object v0, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v0, v0, Landroidx/media3/exoplayer/s2;->c:J

    cmp-long v2, v13, v0

    if-eqz v2, :cond_d

    :cond_a
    iget-object v0, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v1, v1, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    if-eqz v16, :cond_b

    if-eqz p2, :cond_b

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v2

    if-nez v2, :cond_b

    iget-object v2, v11, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    invoke-virtual {v0, v1, v2}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v0

    iget-boolean v0, v0, Landroidx/media3/common/m0$b;->f:Z

    if-nez v0, :cond_b

    const/16 v24, 0x1

    goto :goto_9

    :cond_b
    const/16 v24, 0x0

    :goto_9
    iget-object v0, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v7, v0, Landroidx/media3/exoplayer/s2;->d:J

    invoke-virtual {v12, v1}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v0

    if-ne v0, v10, :cond_c

    const/4 v10, 0x4

    goto :goto_a

    :cond_c
    const/4 v10, 0x3

    :goto_a
    move-object/from16 v1, p0

    move-object v2, v9

    move-wide/from16 v3, v21

    move-wide v5, v13

    move/from16 v9, v24

    invoke-virtual/range {v1 .. v10}, Landroidx/media3/exoplayer/s1;->O(Landroidx/media3/exoplayer/source/l$b;JJJZI)Landroidx/media3/exoplayer/s2;

    move-result-object v0

    iput-object v0, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    :cond_d
    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/s1;->x0()V

    iget-object v0, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v11, v12, v0}, Landroidx/media3/exoplayer/s1;->B0(Landroidx/media3/common/m0;Landroidx/media3/common/m0;)V

    iget-object v0, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual {v0, v12}, Landroidx/media3/exoplayer/s2;->j(Landroidx/media3/common/m0;)Landroidx/media3/exoplayer/s2;

    move-result-object v0

    iput-object v0, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual/range {p1 .. p1}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    if-nez v0, :cond_e

    iput-object v15, v11, Landroidx/media3/exoplayer/s1;->M:Landroidx/media3/exoplayer/s1$h;

    :cond_e
    const/4 v1, 0x1

    const/4 v1, 0x0

    invoke-virtual {v11, v1}, Landroidx/media3/exoplayer/s1;->J(Z)V

    return-void

    :goto_b
    iget-object v1, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v4, v1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v8, v1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-boolean v1, v7, Landroidx/media3/exoplayer/s1$g;->f:Z

    if-eqz v1, :cond_f

    move-wide/from16 v18, v5

    :cond_f
    const/16 v21, 0x0

    move-object/from16 v1, p0

    move-object/from16 v2, p1

    move-object v3, v9

    move-wide/from16 v25, v5

    move-object v5, v8

    move-wide/from16 v6, v18

    move/from16 v8, v21

    invoke-virtual/range {v1 .. v8}, Landroidx/media3/exoplayer/s1;->w1(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JZ)V

    if-nez v16, :cond_10

    iget-object v1, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v1, v1, Landroidx/media3/exoplayer/s2;->c:J

    cmp-long v3, v13, v1

    if-eqz v3, :cond_13

    :cond_10
    iget-object v1, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v2, v1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v2, v2, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v1, v1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    if-eqz v16, :cond_11

    if-eqz p2, :cond_11

    invoke-virtual {v1}, Landroidx/media3/common/m0;->q()Z

    move-result v3

    if-nez v3, :cond_11

    iget-object v3, v11, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    invoke-virtual {v1, v2, v3}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v1

    iget-boolean v1, v1, Landroidx/media3/common/m0$b;->f:Z

    if-nez v1, :cond_11

    const/16 v24, 0x1

    goto :goto_c

    :cond_11
    const/16 v24, 0x0

    :goto_c
    iget-object v1, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v7, v1, Landroidx/media3/exoplayer/s2;->d:J

    invoke-virtual {v12, v2}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v1

    if-ne v1, v10, :cond_12

    const/4 v10, 0x4

    goto :goto_d

    :cond_12
    const/4 v10, 0x3

    :goto_d
    move-object/from16 v1, p0

    move-object v2, v9

    move-wide/from16 v3, v25

    move-wide v5, v13

    move/from16 v9, v24

    invoke-virtual/range {v1 .. v10}, Landroidx/media3/exoplayer/s1;->O(Landroidx/media3/exoplayer/source/l$b;JJJZI)Landroidx/media3/exoplayer/s2;

    move-result-object v1

    iput-object v1, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    :cond_13
    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/s1;->x0()V

    iget-object v1, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v1, v1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v11, v12, v1}, Landroidx/media3/exoplayer/s1;->B0(Landroidx/media3/common/m0;Landroidx/media3/common/m0;)V

    iget-object v1, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual {v1, v12}, Landroidx/media3/exoplayer/s2;->j(Landroidx/media3/common/m0;)Landroidx/media3/exoplayer/s2;

    move-result-object v1

    iput-object v1, v11, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual/range {p1 .. p1}, Landroidx/media3/common/m0;->q()Z

    move-result v1

    if-nez v1, :cond_14

    iput-object v15, v11, Landroidx/media3/exoplayer/s1;->M:Landroidx/media3/exoplayer/s1$h;

    :cond_14
    const/4 v1, 0x1

    const/4 v1, 0x0

    invoke-virtual {v11, v1}, Landroidx/media3/exoplayer/s1;->J(Z)V

    throw v0
.end method

.method public final K0(Landroidx/media3/exoplayer/source/l$b;JZZ)J
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->p1()V

    const/4 v0, 0x1

    const/4 v1, 0x1

    const/4 v1, 0x0

    invoke-virtual {p0, v1, v0}, Landroidx/media3/exoplayer/s1;->x1(ZZ)V

    const/4 v0, 0x2

    if-nez p5, :cond_0

    iget-object p5, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget p5, p5, Landroidx/media3/exoplayer/s2;->e:I

    const/4 v2, 0x3

    if-ne p5, v2, :cond_1

    :cond_0
    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->g1(I)V

    :cond_1
    iget-object p5, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {p5}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object p5

    move-object v2, p5

    :goto_0
    if-eqz v2, :cond_3

    iget-object v3, v2, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v3, v3, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p1, v3}, Landroidx/media3/exoplayer/source/l$b;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_2

    goto :goto_1

    :cond_2
    invoke-virtual {v2}, Landroidx/media3/exoplayer/z1;->j()Landroidx/media3/exoplayer/z1;

    move-result-object v2

    goto :goto_0

    :cond_3
    :goto_1
    if-nez p4, :cond_4

    if-ne p5, v2, :cond_4

    if-eqz v2, :cond_7

    invoke-virtual {v2, p2, p3}, Landroidx/media3/exoplayer/z1;->z(J)J

    move-result-wide p4

    const-wide/16 v3, 0x0

    cmp-long p1, p4, v3

    if-gez p1, :cond_7

    :cond_4
    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length p4, p1

    const/4 p5, 0x1

    const/4 p5, 0x0

    :goto_2
    if-ge p5, p4, :cond_5

    aget-object v3, p1, p5

    invoke-virtual {p0, v3}, Landroidx/media3/exoplayer/s1;->r(Landroidx/media3/exoplayer/w2;)V

    add-int/lit8 p5, p5, 0x1

    goto :goto_2

    :cond_5
    if-eqz v2, :cond_7

    :goto_3
    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object p1

    if-eq p1, v2, :cond_6

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/c2;->b()Landroidx/media3/exoplayer/z1;

    goto :goto_3

    :cond_6
    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {p1, v2}, Landroidx/media3/exoplayer/c2;->D(Landroidx/media3/exoplayer/z1;)Z

    const-wide p4, 0xe8d4a51000L

    invoke-virtual {v2, p4, p5}, Landroidx/media3/exoplayer/z1;->x(J)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->u()V

    :cond_7
    if-eqz v2, :cond_a

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {p1, v2}, Landroidx/media3/exoplayer/c2;->D(Landroidx/media3/exoplayer/z1;)Z

    iget-boolean p1, v2, Landroidx/media3/exoplayer/z1;->d:Z

    if-nez p1, :cond_8

    iget-object p1, v2, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    invoke-virtual {p1, p2, p3}, Landroidx/media3/exoplayer/a2;->b(J)Landroidx/media3/exoplayer/a2;

    move-result-object p1

    iput-object p1, v2, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    goto :goto_4

    :cond_8
    iget-boolean p1, v2, Landroidx/media3/exoplayer/z1;->e:Z

    if-eqz p1, :cond_9

    iget-object p1, v2, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    invoke-interface {p1, p2, p3}, Landroidx/media3/exoplayer/source/k;->seekToUs(J)J

    move-result-wide p2

    iget-object p1, v2, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    iget-wide p4, p0, Landroidx/media3/exoplayer/s1;->n:J

    sub-long p4, p2, p4

    iget-boolean v2, p0, Landroidx/media3/exoplayer/s1;->o:Z

    invoke-interface {p1, p4, p5, v2}, Landroidx/media3/exoplayer/source/k;->discardBuffer(JZ)V

    :cond_9
    :goto_4
    invoke-virtual {p0, p2, p3}, Landroidx/media3/exoplayer/s1;->y0(J)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->Y()V

    goto :goto_5

    :cond_a
    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/c2;->f()V

    invoke-virtual {p0, p2, p3}, Landroidx/media3/exoplayer/s1;->y0(J)V

    :goto_5
    invoke-virtual {p0, v1}, Landroidx/media3/exoplayer/s1;->J(Z)V

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    invoke-interface {p1, v0}, Le2/j;->sendEmptyMessage(I)Z

    return-wide p2
.end method

.method public final L(Landroidx/media3/exoplayer/source/k;)V
    .locals 11
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/c2;->y(Landroidx/media3/exoplayer/source/k;)Z

    move-result p1

    if-nez p1, :cond_0

    return-void

    :cond_0
    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/c2;->l()Landroidx/media3/exoplayer/z1;

    move-result-object p1

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/r;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object v0

    iget v0, v0, Landroidx/media3/common/g0;->a:F

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v1, v1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {p1, v0, v1}, Landroidx/media3/exoplayer/z1;->p(FLandroidx/media3/common/m0;)V

    iget-object v0, p1, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v0, v0, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/z1;->n()Lu2/k0;

    move-result-object v1

    invoke-virtual {p1}, Landroidx/media3/exoplayer/z1;->o()Lx2/f0;

    move-result-object v2

    invoke-virtual {p0, v0, v1, v2}, Landroidx/media3/exoplayer/s1;->r1(Landroidx/media3/exoplayer/source/l$b;Lu2/k0;Lx2/f0;)V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    if-ne p1, v0, :cond_1

    iget-object v0, p1, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-wide v0, v0, Landroidx/media3/exoplayer/a2;->b:J

    invoke-virtual {p0, v0, v1}, Landroidx/media3/exoplayer/s1;->y0(J)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->u()V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v2, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object p1, p1, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-wide v7, p1, Landroidx/media3/exoplayer/a2;->b:J

    iget-wide v5, v0, Landroidx/media3/exoplayer/s2;->c:J

    const/4 v9, 0x1

    const/4 v9, 0x0

    const/4 v10, 0x5

    move-object v1, p0

    move-wide v3, v7

    invoke-virtual/range {v1 .. v10}, Landroidx/media3/exoplayer/s1;->O(Landroidx/media3/exoplayer/source/l$b;JJJZI)Landroidx/media3/exoplayer/s2;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    :cond_1
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->Y()V

    return-void
.end method

.method public final L0(Landroidx/media3/exoplayer/t2;)V
    .locals 9
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    invoke-virtual {p1}, Landroidx/media3/exoplayer/t2;->f()J

    move-result-wide v0

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v4, v0, v2

    if-nez v4, :cond_0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/s1;->M0(Landroidx/media3/exoplayer/t2;)V

    goto :goto_0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    new-instance v1, Landroidx/media3/exoplayer/s1$d;

    invoke-direct {v1, p1}, Landroidx/media3/exoplayer/s1$d;-><init>(Landroidx/media3/exoplayer/t2;)V

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_1
    new-instance v0, Landroidx/media3/exoplayer/s1$d;

    invoke-direct {v0, p1}, Landroidx/media3/exoplayer/s1$d;-><init>(Landroidx/media3/exoplayer/t2;)V

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v4, v1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget v5, p0, Landroidx/media3/exoplayer/s1;->G:I

    iget-boolean v6, p0, Landroidx/media3/exoplayer/s1;->H:Z

    iget-object v7, p0, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    iget-object v8, p0, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    move-object v2, v0

    move-object v3, v4

    invoke-static/range {v2 .. v8}, Landroidx/media3/exoplayer/s1;->A0(Landroidx/media3/exoplayer/s1$d;Landroidx/media3/common/m0;Landroidx/media3/common/m0;IZLandroidx/media3/common/m0$c;Landroidx/media3/common/m0$b;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-virtual {p1, v0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-static {p1}, Ljava/util/Collections;->sort(Ljava/util/List;)V

    goto :goto_0

    :cond_2
    const/4 v0, 0x1

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Landroidx/media3/exoplayer/t2;->k(Z)V

    :goto_0
    return-void
.end method

.method public final M(Landroidx/media3/common/g0;FZZ)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    if-eqz p3, :cond_1

    if-eqz p4, :cond_0

    iget-object p3, p0, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    const/4 p4, 0x1

    invoke-virtual {p3, p4}, Landroidx/media3/exoplayer/s1$e;->b(I)V

    :cond_0
    iget-object p3, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual {p3, p1}, Landroidx/media3/exoplayer/s2;->g(Landroidx/media3/common/g0;)Landroidx/media3/exoplayer/s2;

    move-result-object p3

    iput-object p3, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    :cond_1
    iget p3, p1, Landroidx/media3/common/g0;->a:F

    invoke-virtual {p0, p3}, Landroidx/media3/exoplayer/s1;->y1(F)V

    iget-object p3, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length p4, p3

    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    if-ge v0, p4, :cond_3

    aget-object v1, p3, v0

    if-eqz v1, :cond_2

    iget v2, p1, Landroidx/media3/common/g0;->a:F

    invoke-interface {v1, p2, v2}, Landroidx/media3/exoplayer/w2;->c(FF)V

    :cond_2
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_3
    return-void
.end method

.method public final M0(Landroidx/media3/exoplayer/t2;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    invoke-virtual {p1}, Landroidx/media3/exoplayer/t2;->c()Landroid/os/Looper;

    move-result-object v0

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->k:Landroid/os/Looper;

    if-ne v0, v1, :cond_1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/s1;->q(Landroidx/media3/exoplayer/t2;)V

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget p1, p1, Landroidx/media3/exoplayer/s2;->e:I

    const/4 v0, 0x3

    const/4 v1, 0x2

    if-eq p1, v0, :cond_0

    if-ne p1, v1, :cond_2

    :cond_0
    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    invoke-interface {p1, v1}, Le2/j;->sendEmptyMessage(I)Z

    goto :goto_0

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/16 v1, 0xf

    invoke-interface {v0, v1, p1}, Le2/j;->obtainMessage(ILjava/lang/Object;)Le2/j$a;

    move-result-object p1

    invoke-interface {p1}, Le2/j$a;->a()V

    :cond_2
    :goto_0
    return-void
.end method

.method public final N(Landroidx/media3/common/g0;Z)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget v0, p1, Landroidx/media3/common/g0;->a:F

    const/4 v1, 0x1

    invoke-virtual {p0, p1, v0, v1, p2}, Landroidx/media3/exoplayer/s1;->M(Landroidx/media3/common/g0;FZZ)V

    return-void
.end method

.method public final N0(Landroidx/media3/exoplayer/t2;)V
    .locals 3

    invoke-virtual {p1}, Landroidx/media3/exoplayer/t2;->c()Landroid/os/Looper;

    move-result-object v0

    invoke-virtual {v0}, Landroid/os/Looper;->getThread()Ljava/lang/Thread;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Thread;->isAlive()Z

    move-result v1

    if-nez v1, :cond_0

    const-string v0, "TAG"

    const-string v1, "Trying to send message on a dead thread."

    invoke-static {v0, v1}, Le2/o;->h(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v0, 0x1

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Landroidx/media3/exoplayer/t2;->k(Z)V

    return-void

    :cond_0
    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->r:Le2/d;

    const/4 v2, 0x1

    const/4 v2, 0x0

    invoke-interface {v1, v0, v2}, Le2/d;->createHandler(Landroid/os/Looper;Landroid/os/Handler$Callback;)Le2/j;

    move-result-object v0

    new-instance v1, Landroidx/media3/exoplayer/r1;

    invoke-direct {v1, p0, p1}, Landroidx/media3/exoplayer/r1;-><init>(Landroidx/media3/exoplayer/s1;Landroidx/media3/exoplayer/t2;)V

    invoke-interface {v0, v1}, Le2/j;->post(Ljava/lang/Runnable;)Z

    return-void
.end method

.method public final O(Landroidx/media3/exoplayer/source/l$b;JJJZI)Landroidx/media3/exoplayer/s2;
    .locals 14
    .annotation build Landroidx/annotation/CheckResult;
    .end annotation

    move-object v0, p0

    move-object v2, p1

    move-wide/from16 v5, p4

    iget-boolean v1, v0, Landroidx/media3/exoplayer/s1;->P:Z

    if-nez v1, :cond_1

    iget-object v1, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v3, v1, Landroidx/media3/exoplayer/s2;->r:J

    cmp-long v1, p2, v3

    if-nez v1, :cond_1

    iget-object v1, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v1, v1, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p1, v1}, Landroidx/media3/exoplayer/source/l$b;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x1

    const/4 v1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v1, 0x1

    :goto_1
    iput-boolean v1, v0, Landroidx/media3/exoplayer/s1;->P:Z

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->x0()V

    iget-object v1, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v3, v1, Landroidx/media3/exoplayer/s2;->h:Lu2/k0;

    iget-object v4, v1, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    iget-object v1, v1, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    iget-object v7, v0, Landroidx/media3/exoplayer/s1;->u:Landroidx/media3/exoplayer/r2;

    invoke-virtual {v7}, Landroidx/media3/exoplayer/r2;->t()Z

    move-result v7

    if-eqz v7, :cond_5

    iget-object v1, v0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v1

    if-nez v1, :cond_2

    sget-object v3, Lu2/k0;->d:Lu2/k0;

    goto :goto_2

    :cond_2
    invoke-virtual {v1}, Landroidx/media3/exoplayer/z1;->n()Lu2/k0;

    move-result-object v3

    :goto_2
    if-nez v1, :cond_3

    iget-object v4, v0, Landroidx/media3/exoplayer/s1;->f:Lx2/f0;

    goto :goto_3

    :cond_3
    invoke-virtual {v1}, Landroidx/media3/exoplayer/z1;->o()Lx2/f0;

    move-result-object v4

    :goto_3
    iget-object v7, v4, Lx2/f0;->c:[Lx2/z;

    invoke-virtual {p0, v7}, Landroidx/media3/exoplayer/s1;->y([Lx2/z;)Lcom/google/common/collect/ImmutableList;

    move-result-object v7

    if-eqz v1, :cond_4

    iget-object v8, v1, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-wide v9, v8, Landroidx/media3/exoplayer/a2;->c:J

    cmp-long v11, v9, v5

    if-eqz v11, :cond_4

    invoke-virtual {v8, v5, v6}, Landroidx/media3/exoplayer/a2;->a(J)Landroidx/media3/exoplayer/a2;

    move-result-object v8

    iput-object v8, v1, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    :cond_4
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->c0()V

    move-object v11, v3

    move-object v12, v4

    move-object v13, v7

    goto :goto_4

    :cond_5
    iget-object v7, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v7, v7, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p1, v7}, Landroidx/media3/exoplayer/source/l$b;->equals(Ljava/lang/Object;)Z

    move-result v7

    if-nez v7, :cond_6

    sget-object v1, Lu2/k0;->d:Lu2/k0;

    iget-object v3, v0, Landroidx/media3/exoplayer/s1;->f:Lx2/f0;

    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object v4

    move-object v11, v1

    move-object v12, v3

    move-object v13, v4

    goto :goto_4

    :cond_6
    move-object v13, v1

    move-object v11, v3

    move-object v12, v4

    :goto_4
    if-eqz p8, :cond_7

    iget-object v1, v0, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    move/from16 v3, p9

    invoke-virtual {v1, v3}, Landroidx/media3/exoplayer/s1$e;->e(I)V

    :cond_7
    iget-object v1, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->F()J

    move-result-wide v9

    move-object v2, p1

    move-wide/from16 v3, p2

    move-wide/from16 v5, p4

    move-wide/from16 v7, p6

    invoke-virtual/range {v1 .. v13}, Landroidx/media3/exoplayer/s2;->d(Landroidx/media3/exoplayer/source/l$b;JJJJLu2/k0;Lx2/f0;Ljava/util/List;)Landroidx/media3/exoplayer/s2;

    move-result-object v1

    return-object v1
.end method

.method public final O0(J)V
    .locals 5

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v1, v0

    const/4 v2, 0x1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_1

    aget-object v3, v0, v2

    invoke-interface {v3}, Landroidx/media3/exoplayer/w2;->getStream()Lu2/e0;

    move-result-object v4

    if-eqz v4, :cond_0

    invoke-virtual {p0, v3, p1, p2}, Landroidx/media3/exoplayer/s1;->P0(Landroidx/media3/exoplayer/w2;J)V

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public final P(Landroidx/media3/exoplayer/w2;Landroidx/media3/exoplayer/z1;)Z
    .locals 3

    invoke-virtual {p2}, Landroidx/media3/exoplayer/z1;->j()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    iget-object p2, p2, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-boolean p2, p2, Landroidx/media3/exoplayer/a2;->f:Z

    if-eqz p2, :cond_1

    iget-boolean p2, v0, Landroidx/media3/exoplayer/z1;->d:Z

    if-eqz p2, :cond_1

    instance-of p2, p1, Lw2/i;

    if-nez p2, :cond_0

    instance-of p2, p1, Ls2/c;

    if-nez p2, :cond_0

    invoke-interface {p1}, Landroidx/media3/exoplayer/w2;->d()J

    move-result-wide p1

    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->m()J

    move-result-wide v0

    cmp-long v2, p1, v0

    if-ltz v2, :cond_1

    :cond_0
    const/4 p1, 0x1

    goto :goto_0

    :cond_1
    const/4 p1, 0x1

    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final P0(Landroidx/media3/exoplayer/w2;J)V
    .locals 1

    invoke-interface {p1}, Landroidx/media3/exoplayer/w2;->setCurrentStreamFinal()V

    instance-of v0, p1, Lw2/i;

    if-eqz v0, :cond_0

    check-cast p1, Lw2/i;

    invoke-virtual {p1, p2, p3}, Lw2/i;->c0(J)V

    :cond_0
    return-void
.end method

.method public final Q()Z
    .locals 6

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->s()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    iget-boolean v1, v0, Landroidx/media3/exoplayer/z1;->d:Z

    const/4 v2, 0x1

    const/4 v2, 0x0

    if-nez v1, :cond_0

    return v2

    :cond_0
    const/4 v1, 0x1

    const/4 v1, 0x0

    :goto_0
    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v4, v3

    if-ge v1, v4, :cond_3

    aget-object v3, v3, v1

    iget-object v4, v0, Landroidx/media3/exoplayer/z1;->c:[Lu2/e0;

    aget-object v4, v4, v1

    invoke-interface {v3}, Landroidx/media3/exoplayer/w2;->getStream()Lu2/e0;

    move-result-object v5

    if-ne v5, v4, :cond_2

    if-eqz v4, :cond_1

    invoke-interface {v3}, Landroidx/media3/exoplayer/w2;->hasReadStreamToEnd()Z

    move-result v4

    if-nez v4, :cond_1

    invoke-virtual {p0, v3, v0}, Landroidx/media3/exoplayer/s1;->P(Landroidx/media3/exoplayer/w2;Landroidx/media3/exoplayer/z1;)Z

    move-result v3

    if-nez v3, :cond_1

    goto :goto_1

    :cond_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_2
    :goto_1
    return v2

    :cond_3
    const/4 v0, 0x1

    return v0
.end method

.method public final Q0(ZLjava/util/concurrent/atomic/AtomicBoolean;)V
    .locals 4
    .param p2    # Ljava/util/concurrent/atomic/AtomicBoolean;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-boolean v0, p0, Landroidx/media3/exoplayer/s1;->I:Z

    if-eq v0, p1, :cond_1

    iput-boolean p1, p0, Landroidx/media3/exoplayer/s1;->I:Z

    if-nez p1, :cond_1

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v0, p1

    const/4 v1, 0x1

    const/4 v1, 0x0

    :goto_0
    if-ge v1, v0, :cond_1

    aget-object v2, p1, v1

    invoke-static {v2}, Landroidx/media3/exoplayer/s1;->T(Landroidx/media3/exoplayer/w2;)Z

    move-result v3

    if-nez v3, :cond_0

    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->b:Ljava/util/Set;

    invoke-interface {v3, v2}, Ljava/util/Set;->remove(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-interface {v2}, Landroidx/media3/exoplayer/w2;->reset()V

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    if-eqz p2, :cond_2

    monitor-enter p0

    const/4 p1, 0x1

    :try_start_0
    invoke-virtual {p2, p1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    invoke-virtual {p0}, Ljava/lang/Object;->notifyAll()V

    monitor-exit p0

    goto :goto_1

    :catchall_0
    move-exception p1

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1

    :cond_2
    :goto_1
    return-void
.end method

.method public final R0(Landroidx/media3/common/g0;)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/16 v1, 0x10

    invoke-interface {v0, v1}, Le2/j;->removeMessages(I)V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/r;->b(Landroidx/media3/common/g0;)V

    return-void
.end method

.method public final S()Z
    .locals 6

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->l()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    const/4 v1, 0x1

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    :cond_0
    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->k()J

    move-result-wide v2

    const-wide/high16 v4, -0x8000000000000000L

    cmp-long v0, v2, v4

    if-nez v0, :cond_1

    return v1

    :cond_1
    const/4 v0, 0x1

    return v0
.end method

.method public final S0(Landroidx/media3/exoplayer/s1$b;)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/s1$e;->b(I)V

    invoke-static {p1}, Landroidx/media3/exoplayer/s1$b;->a(Landroidx/media3/exoplayer/s1$b;)I

    move-result v0

    const/4 v1, -0x1

    if-eq v0, v1, :cond_0

    new-instance v0, Landroidx/media3/exoplayer/s1$h;

    new-instance v1, Landroidx/media3/exoplayer/u2;

    invoke-static {p1}, Landroidx/media3/exoplayer/s1$b;->b(Landroidx/media3/exoplayer/s1$b;)Ljava/util/List;

    move-result-object v2

    invoke-static {p1}, Landroidx/media3/exoplayer/s1$b;->c(Landroidx/media3/exoplayer/s1$b;)Lu2/f0;

    move-result-object v3

    invoke-direct {v1, v2, v3}, Landroidx/media3/exoplayer/u2;-><init>(Ljava/util/Collection;Lu2/f0;)V

    invoke-static {p1}, Landroidx/media3/exoplayer/s1$b;->a(Landroidx/media3/exoplayer/s1$b;)I

    move-result v2

    invoke-static {p1}, Landroidx/media3/exoplayer/s1$b;->d(Landroidx/media3/exoplayer/s1$b;)J

    move-result-wide v3

    invoke-direct {v0, v1, v2, v3, v4}, Landroidx/media3/exoplayer/s1$h;-><init>(Landroidx/media3/common/m0;IJ)V

    iput-object v0, p0, Landroidx/media3/exoplayer/s1;->M:Landroidx/media3/exoplayer/s1$h;

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->u:Landroidx/media3/exoplayer/r2;

    invoke-static {p1}, Landroidx/media3/exoplayer/s1$b;->b(Landroidx/media3/exoplayer/s1$b;)Ljava/util/List;

    move-result-object v1

    invoke-static {p1}, Landroidx/media3/exoplayer/s1$b;->c(Landroidx/media3/exoplayer/s1$b;)Lu2/f0;

    move-result-object p1

    invoke-virtual {v0, v1, p1}, Landroidx/media3/exoplayer/r2;->D(Ljava/util/List;Lu2/f0;)Landroidx/media3/common/m0;

    move-result-object p1

    const/4 v0, 0x1

    const/4 v0, 0x0

    invoke-virtual {p0, p1, v0}, Landroidx/media3/exoplayer/s1;->K(Landroidx/media3/common/m0;Z)V

    return-void
.end method

.method public T0(Ljava/util/List;IJLu2/f0;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/exoplayer/r2$c;",
            ">;IJ",
            "Lu2/f0;",
            ")V"
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    new-instance v8, Landroidx/media3/exoplayer/s1$b;

    const/4 v7, 0x1

    const/4 v7, 0x0

    move-object v1, v8

    move-object v2, p1

    move-object v3, p5

    move v4, p2

    move-wide v5, p3

    invoke-direct/range {v1 .. v7}, Landroidx/media3/exoplayer/s1$b;-><init>(Ljava/util/List;Lu2/f0;IJLandroidx/media3/exoplayer/s1$a;)V

    const/16 p1, 0x11

    invoke-interface {v0, p1, v8}, Le2/j;->obtainMessage(ILjava/lang/Object;)Le2/j$a;

    move-result-object p1

    invoke-interface {p1}, Le2/j$a;->a()V

    return-void
.end method

.method public final U()Z
    .locals 5

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    iget-object v1, v0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-wide v1, v1, Landroidx/media3/exoplayer/a2;->e:J

    iget-boolean v0, v0, Landroidx/media3/exoplayer/z1;->d:Z

    if-eqz v0, :cond_1

    const-wide v3, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v0, v1, v3

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v3, v0, Landroidx/media3/exoplayer/s2;->r:J

    cmp-long v0, v3, v1

    if-ltz v0, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->j1()Z

    move-result v0

    if-nez v0, :cond_1

    :cond_0
    const/4 v0, 0x1

    goto :goto_0

    :cond_1
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final U0(Z)V
    .locals 1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/s1;->K:Z

    if-ne p1, v0, :cond_0

    return-void

    :cond_0
    iput-boolean p1, p0, Landroidx/media3/exoplayer/s1;->K:Z

    if-nez p1, :cond_1

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-boolean p1, p1, Landroidx/media3/exoplayer/s2;->o:Z

    if-eqz p1, :cond_1

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/4 v0, 0x2

    invoke-interface {p1, v0}, Le2/j;->sendEmptyMessage(I)Z

    :cond_1
    return-void
.end method

.method public final V0(Z)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iput-boolean p1, p0, Landroidx/media3/exoplayer/s1;->B:Z

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->x0()V

    iget-boolean p1, p0, Landroidx/media3/exoplayer/s1;->C:Z

    if-eqz p1, :cond_0

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/c2;->s()Landroidx/media3/exoplayer/z1;

    move-result-object p1

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    if-eq p1, v0, :cond_0

    const/4 p1, 0x1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/s1;->H0(Z)V

    const/4 p1, 0x1

    const/4 p1, 0x0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/s1;->J(Z)V

    :cond_0
    return-void
.end method

.method public final synthetic W()Ljava/lang/Boolean;
    .locals 1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/s1;->A:Z

    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    return-object v0
.end method

.method public W0(ZI)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/4 v1, 0x1

    invoke-interface {v0, v1, p1, p2}, Le2/j;->obtainMessage(III)Le2/j$a;

    move-result-object p1

    invoke-interface {p1}, Le2/j$a;->a()V

    return-void
.end method

.method public final synthetic X(Landroidx/media3/exoplayer/t2;)V
    .locals 2

    :try_start_0
    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/s1;->q(Landroidx/media3/exoplayer/t2;)V
    :try_end_0
    .catch Landroidx/media3/exoplayer/ExoPlaybackException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception p1

    const-string v0, "ExoPlayerImplInternal"

    const-string v1, "Unexpected error delivering message on external thread."

    invoke-static {v0, v1, p1}, Le2/o;->d(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    new-instance v0, Ljava/lang/RuntimeException;

    invoke-direct {v0, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/Throwable;)V

    throw v0
.end method

.method public final X0(ZIZI)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    invoke-virtual {v0, p3}, Landroidx/media3/exoplayer/s1$e;->b(I)V

    iget-object p3, p0, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    invoke-virtual {p3, p4}, Landroidx/media3/exoplayer/s1$e;->c(I)V

    iget-object p3, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual {p3, p1, p2}, Landroidx/media3/exoplayer/s2;->e(ZI)Landroidx/media3/exoplayer/s2;

    move-result-object p2

    iput-object p2, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    const/4 p2, 0x1

    const/4 p2, 0x0

    invoke-virtual {p0, p2, p2}, Landroidx/media3/exoplayer/s1;->x1(ZZ)V

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/s1;->j0(Z)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->j1()Z

    move-result p1

    if-nez p1, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->p1()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->v1()V

    goto :goto_0

    :cond_0
    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget p1, p1, Landroidx/media3/exoplayer/s2;->e:I

    const/4 p3, 0x3

    const/4 p4, 0x2

    if-ne p1, p3, :cond_1

    invoke-virtual {p0, p2, p2}, Landroidx/media3/exoplayer/s1;->x1(ZZ)V

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/r;->f()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->m1()V

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    invoke-interface {p1, p4}, Le2/j;->sendEmptyMessage(I)Z

    goto :goto_0

    :cond_1
    if-ne p1, p4, :cond_2

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    invoke-interface {p1, p4}, Le2/j;->sendEmptyMessage(I)Z

    :cond_2
    :goto_0
    return-void
.end method

.method public final Y()V
    .locals 7

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->i1()Z

    move-result v0

    iput-boolean v0, p0, Landroidx/media3/exoplayer/s1;->F:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->l()Landroidx/media3/exoplayer/z1;

    move-result-object v1

    iget-wide v2, p0, Landroidx/media3/exoplayer/s1;->N:J

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/r;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object v0

    iget v4, v0, Landroidx/media3/common/g0;->a:F

    iget-wide v5, p0, Landroidx/media3/exoplayer/s1;->E:J

    invoke-virtual/range {v1 .. v6}, Landroidx/media3/exoplayer/z1;->d(JFJ)V

    :cond_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->q1()V

    return-void
.end method

.method public Y0(Landroidx/media3/common/g0;)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/4 v1, 0x4

    invoke-interface {v0, v1, p1}, Le2/j;->obtainMessage(ILjava/lang/Object;)Le2/j$a;

    move-result-object p1

    invoke-interface {p1}, Le2/j$a;->a()V

    return-void
.end method

.method public final Z()V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/s1$e;->d(Landroidx/media3/exoplayer/s2;)V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    invoke-static {v0}, Landroidx/media3/exoplayer/s1$e;->a(Landroidx/media3/exoplayer/s1$e;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->s:Landroidx/media3/exoplayer/s1$f;

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    invoke-interface {v0, v1}, Landroidx/media3/exoplayer/s1$f;->a(Landroidx/media3/exoplayer/s1$e;)V

    new-instance v0, Landroidx/media3/exoplayer/s1$e;

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-direct {v0, v1}, Landroidx/media3/exoplayer/s1$e;-><init>(Landroidx/media3/exoplayer/s2;)V

    iput-object v0, p0, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    :cond_0
    return-void
.end method

.method public final Z0(Landroidx/media3/common/g0;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/s1;->R0(Landroidx/media3/common/g0;)V

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/r;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object p1

    const/4 v0, 0x1

    invoke-virtual {p0, p1, v0}, Landroidx/media3/exoplayer/s1;->N(Landroidx/media3/common/g0;Z)V

    return-void
.end method

.method public a()V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/16 v1, 0x16

    invoke-interface {v0, v1}, Le2/j;->sendEmptyMessage(I)Z

    return-void
.end method

.method public final a0(JJ)V
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_f

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v0

    if-eqz v0, :cond_0

    goto/16 :goto_6

    :cond_0
    iget-boolean v0, p0, Landroidx/media3/exoplayer/s1;->P:Z

    if-eqz v0, :cond_1

    const-wide/16 v0, 0x1

    sub-long/2addr p1, v0

    const/4 v0, 0x1

    const/4 v0, 0x0

    iput-boolean v0, p0, Landroidx/media3/exoplayer/s1;->P:Z

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v0, v0, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {v1, v0}, Landroidx/media3/common/m0;->b(Ljava/lang/Object;)I

    move-result v0

    iget v1, p0, Landroidx/media3/exoplayer/s1;->O:I

    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-virtual {v2}, Ljava/util/ArrayList;->size()I

    move-result v2

    invoke-static {v1, v2}, Ljava/lang/Math;->min(II)I

    move-result v1

    const/4 v2, 0x1

    const/4 v2, 0x0

    if-lez v1, :cond_2

    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    add-int/lit8 v4, v1, -0x1

    invoke-virtual {v3, v4}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Landroidx/media3/exoplayer/s1$d;

    goto :goto_0

    :cond_2
    move-object v3, v2

    :goto_0
    if-eqz v3, :cond_5

    iget v4, v3, Landroidx/media3/exoplayer/s1$d;->b:I

    if-gt v4, v0, :cond_3

    if-ne v4, v0, :cond_5

    iget-wide v3, v3, Landroidx/media3/exoplayer/s1$d;->c:J

    cmp-long v5, v3, p1

    if-lez v5, :cond_5

    :cond_3
    add-int/lit8 v3, v1, -0x1

    if-lez v3, :cond_4

    iget-object v4, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    add-int/lit8 v1, v1, -0x2

    invoke-virtual {v4, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/exoplayer/s1$d;

    goto :goto_1

    :cond_4
    move-object v1, v2

    :goto_1
    move v7, v3

    move-object v3, v1

    move v1, v7

    goto :goto_0

    :cond_5
    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-virtual {v3}, Ljava/util/ArrayList;->size()I

    move-result v3

    if-ge v1, v3, :cond_6

    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-virtual {v3, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Landroidx/media3/exoplayer/s1$d;

    goto :goto_2

    :cond_6
    move-object v3, v2

    :goto_2
    if-eqz v3, :cond_8

    iget-object v4, v3, Landroidx/media3/exoplayer/s1$d;->d:Ljava/lang/Object;

    if-eqz v4, :cond_8

    iget v4, v3, Landroidx/media3/exoplayer/s1$d;->b:I

    if-lt v4, v0, :cond_7

    if-ne v4, v0, :cond_8

    iget-wide v4, v3, Landroidx/media3/exoplayer/s1$d;->c:J

    cmp-long v6, v4, p1

    if-gtz v6, :cond_8

    :cond_7
    add-int/lit8 v1, v1, 0x1

    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-virtual {v3}, Ljava/util/ArrayList;->size()I

    move-result v3

    if-ge v1, v3, :cond_6

    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-virtual {v3, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Landroidx/media3/exoplayer/s1$d;

    goto :goto_2

    :cond_8
    :goto_3
    if-eqz v3, :cond_e

    iget-object v4, v3, Landroidx/media3/exoplayer/s1$d;->d:Ljava/lang/Object;

    if-eqz v4, :cond_e

    iget v4, v3, Landroidx/media3/exoplayer/s1$d;->b:I

    if-ne v4, v0, :cond_e

    iget-wide v4, v3, Landroidx/media3/exoplayer/s1$d;->c:J

    cmp-long v6, v4, p1

    if-lez v6, :cond_e

    cmp-long v6, v4, p3

    if-gtz v6, :cond_e

    :try_start_0
    iget-object v4, v3, Landroidx/media3/exoplayer/s1$d;->a:Landroidx/media3/exoplayer/t2;

    invoke-virtual {p0, v4}, Landroidx/media3/exoplayer/s1;->M0(Landroidx/media3/exoplayer/t2;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    iget-object v4, v3, Landroidx/media3/exoplayer/s1$d;->a:Landroidx/media3/exoplayer/t2;

    invoke-virtual {v4}, Landroidx/media3/exoplayer/t2;->b()Z

    move-result v4

    if-nez v4, :cond_a

    iget-object v3, v3, Landroidx/media3/exoplayer/s1$d;->a:Landroidx/media3/exoplayer/t2;

    invoke-virtual {v3}, Landroidx/media3/exoplayer/t2;->j()Z

    move-result v3

    if-eqz v3, :cond_9

    goto :goto_4

    :cond_9
    add-int/lit8 v1, v1, 0x1

    goto :goto_5

    :cond_a
    :goto_4
    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-virtual {v3, v1}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    :goto_5
    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-virtual {v3}, Ljava/util/ArrayList;->size()I

    move-result v3

    if-ge v1, v3, :cond_b

    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-virtual {v3, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Landroidx/media3/exoplayer/s1$d;

    goto :goto_3

    :cond_b
    move-object v3, v2

    goto :goto_3

    :catchall_0
    move-exception p1

    iget-object p2, v3, Landroidx/media3/exoplayer/s1$d;->a:Landroidx/media3/exoplayer/t2;

    invoke-virtual {p2}, Landroidx/media3/exoplayer/t2;->b()Z

    move-result p2

    if-nez p2, :cond_c

    iget-object p2, v3, Landroidx/media3/exoplayer/s1$d;->a:Landroidx/media3/exoplayer/t2;

    invoke-virtual {p2}, Landroidx/media3/exoplayer/t2;->j()Z

    move-result p2

    if-eqz p2, :cond_d

    :cond_c
    iget-object p2, p0, Landroidx/media3/exoplayer/s1;->q:Ljava/util/ArrayList;

    invoke-virtual {p2, v1}, Ljava/util/ArrayList;->remove(I)Ljava/lang/Object;

    :cond_d
    throw p1

    :cond_e
    iput v1, p0, Landroidx/media3/exoplayer/s1;->O:I

    :cond_f
    :goto_6
    return-void
.end method

.method public a1(I)V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/16 v1, 0xb

    const/4 v2, 0x1

    const/4 v2, 0x0

    invoke-interface {v0, v1, p1, v2}, Le2/j;->obtainMessage(III)Le2/j$a;

    move-result-object p1

    invoke-interface {p1}, Le2/j$a;->a()V

    return-void
.end method

.method public b(Landroidx/media3/exoplayer/w2;)V
    .locals 1

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/16 v0, 0x1a

    invoke-interface {p1, v0}, Le2/j;->sendEmptyMessage(I)Z

    return-void
.end method

.method public final b0()V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    iget-wide v1, p0, Landroidx/media3/exoplayer/s1;->N:J

    invoke-virtual {v0, v1, v2}, Landroidx/media3/exoplayer/c2;->C(J)V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->H()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    iget-wide v1, p0, Landroidx/media3/exoplayer/s1;->N:J

    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual {v0, v1, v2, v3}, Landroidx/media3/exoplayer/c2;->q(JLandroidx/media3/exoplayer/s2;)Landroidx/media3/exoplayer/a2;

    move-result-object v0

    if-eqz v0, :cond_1

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v1, v0}, Landroidx/media3/exoplayer/c2;->g(Landroidx/media3/exoplayer/a2;)Landroidx/media3/exoplayer/z1;

    move-result-object v1

    iget-object v2, v1, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    iget-wide v3, v0, Landroidx/media3/exoplayer/a2;->b:J

    invoke-interface {v2, p0, v3, v4}, Landroidx/media3/exoplayer/source/k;->e(Landroidx/media3/exoplayer/source/k$a;J)V

    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v2}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v2

    if-ne v2, v1, :cond_0

    iget-wide v0, v0, Landroidx/media3/exoplayer/a2;->b:J

    invoke-virtual {p0, v0, v1}, Landroidx/media3/exoplayer/s1;->y0(J)V

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->J(Z)V

    :cond_1
    iget-boolean v0, p0, Landroidx/media3/exoplayer/s1;->F:Z

    if-eqz v0, :cond_2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->S()Z

    move-result v0

    iput-boolean v0, p0, Landroidx/media3/exoplayer/s1;->F:Z

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->q1()V

    goto :goto_0

    :cond_2
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->Y()V

    :goto_0
    return-void
.end method

.method public final b1(I)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iput p1, p0, Landroidx/media3/exoplayer/s1;->G:I

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v1, v1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v0, v1, p1}, Landroidx/media3/exoplayer/c2;->K(Landroidx/media3/common/m0;I)Z

    move-result p1

    if-nez p1, :cond_0

    const/4 p1, 0x1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/s1;->H0(Z)V

    :cond_0
    const/4 p1, 0x1

    const/4 p1, 0x0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/s1;->J(Z)V

    return-void
.end method

.method public declared-synchronized c(Landroidx/media3/exoplayer/t2;)V
    .locals 2

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Landroidx/media3/exoplayer/s1;->A:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->k:Landroid/os/Looper;

    invoke-virtual {v0}, Landroid/os/Looper;->getThread()Ljava/lang/Thread;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Thread;->isAlive()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/16 v1, 0xe

    invoke-interface {v0, v1, p1}, Le2/j;->obtainMessage(ILjava/lang/Object;)Le2/j$a;

    move-result-object p1

    invoke-interface {p1}, Le2/j$a;->a()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    goto :goto_1

    :cond_1
    :goto_0
    :try_start_1
    const-string v0, "ExoPlayerImplInternal"

    const-string v1, "Ignoring messages sent after release."

    invoke-static {v0, v1}, Le2/o;->h(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v0, 0x1

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Landroidx/media3/exoplayer/t2;->k(Z)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return-void

    :goto_1
    monitor-exit p0

    throw p1
.end method

.method public final c0()V
    .locals 6

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    if-eqz v0, :cond_4

    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->o()Lx2/f0;

    move-result-object v0

    const/4 v1, 0x1

    const/4 v1, 0x0

    const/4 v2, 0x1

    const/4 v2, 0x0

    const/4 v3, 0x1

    const/4 v3, 0x0

    :goto_0
    iget-object v4, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v4, v4

    const/4 v5, 0x1

    if-ge v2, v4, :cond_2

    invoke-virtual {v0, v2}, Lx2/f0;->c(I)Z

    move-result v4

    if-eqz v4, :cond_1

    iget-object v4, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    aget-object v4, v4, v2

    invoke-interface {v4}, Landroidx/media3/exoplayer/w2;->getTrackType()I

    move-result v4

    if-eq v4, v5, :cond_0

    const/4 v0, 0x1

    const/4 v0, 0x0

    goto :goto_1

    :cond_0
    iget-object v4, v0, Lx2/f0;->b:[Landroidx/media3/exoplayer/z2;

    aget-object v4, v4, v2

    iget v4, v4, Landroidx/media3/exoplayer/z2;->a:I

    if-eqz v4, :cond_1

    const/4 v3, 0x1

    :cond_1
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_2
    const/4 v0, 0x1

    :goto_1
    if-eqz v3, :cond_3

    if-eqz v0, :cond_3

    const/4 v1, 0x1

    :cond_3
    invoke-virtual {p0, v1}, Landroidx/media3/exoplayer/s1;->U0(Z)V

    :cond_4
    return-void
.end method

.method public final c1(Landroidx/media3/exoplayer/b3;)V
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/s1;->x:Landroidx/media3/exoplayer/b3;

    return-void
.end method

.method public final d0()V
    .locals 14
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    const/4 v0, 0x1

    const/4 v0, 0x0

    const/4 v1, 0x1

    const/4 v1, 0x0

    :goto_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->h1()Z

    move-result v2

    if-eqz v2, :cond_3

    if-eqz v1, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->Z()V

    :cond_0
    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/c2;->b()Landroidx/media3/exoplayer/z1;

    move-result-object v1

    invoke-static {v1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/exoplayer/z1;

    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v2, v2, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v2, v2, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v3, v1, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v3, v3, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    iget-object v3, v3, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {v2, v3}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    move-result v2

    const/4 v3, 0x1

    if-eqz v2, :cond_1

    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v2, v2, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget v4, v2, Landroidx/media3/exoplayer/source/l$b;->b:I

    const/4 v5, -0x1

    if-ne v4, v5, :cond_1

    iget-object v4, v1, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v4, v4, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    iget v6, v4, Landroidx/media3/exoplayer/source/l$b;->b:I

    if-ne v6, v5, :cond_1

    iget v2, v2, Landroidx/media3/exoplayer/source/l$b;->e:I

    iget v4, v4, Landroidx/media3/exoplayer/source/l$b;->e:I

    if-eq v2, v4, :cond_1

    const/4 v2, 0x1

    goto :goto_1

    :cond_1
    const/4 v2, 0x1

    const/4 v2, 0x0

    :goto_1
    iget-object v1, v1, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v5, v1, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v10, v1, Landroidx/media3/exoplayer/a2;->b:J

    iget-wide v8, v1, Landroidx/media3/exoplayer/a2;->c:J

    xor-int/lit8 v12, v2, 0x1

    const/4 v13, 0x1

    const/4 v13, 0x0

    move-object v4, p0

    move-wide v6, v10

    invoke-virtual/range {v4 .. v13}, Landroidx/media3/exoplayer/s1;->O(Landroidx/media3/exoplayer/source/l$b;JJJZI)Landroidx/media3/exoplayer/s2;

    move-result-object v1

    iput-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->x0()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->v1()V

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget v1, v1, Landroidx/media3/exoplayer/s2;->e:I

    const/4 v2, 0x3

    if-ne v1, v2, :cond_2

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->m1()V

    :cond_2
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->n()V

    const/4 v1, 0x1

    goto :goto_0

    :cond_3
    return-void
.end method

.method public d1(Z)V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/4 v1, 0x1

    const/4 v1, 0x0

    const/16 v2, 0xc

    invoke-interface {v0, v2, p1, v1}, Le2/j;->obtainMessage(III)Le2/j$a;

    move-result-object p1

    invoke-interface {p1}, Le2/j$a;->a()V

    return-void
.end method

.method public final e0()V
    .locals 14
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->s()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->j()Landroidx/media3/exoplayer/z1;

    move-result-object v1

    const-wide v8, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v10, 0x1

    const/4 v10, 0x0

    if-eqz v1, :cond_a

    iget-boolean v1, p0, Landroidx/media3/exoplayer/s1;->C:Z

    if-eqz v1, :cond_1

    goto/16 :goto_2

    :cond_1
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->Q()Z

    move-result v1

    if-nez v1, :cond_2

    return-void

    :cond_2
    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->j()Landroidx/media3/exoplayer/z1;

    move-result-object v1

    iget-boolean v1, v1, Landroidx/media3/exoplayer/z1;->d:Z

    if-nez v1, :cond_3

    iget-wide v1, p0, Landroidx/media3/exoplayer/s1;->N:J

    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->j()Landroidx/media3/exoplayer/z1;

    move-result-object v3

    invoke-virtual {v3}, Landroidx/media3/exoplayer/z1;->m()J

    move-result-wide v3

    cmp-long v5, v1, v3

    if-gez v5, :cond_3

    return-void

    :cond_3
    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->o()Lx2/f0;

    move-result-object v11

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/c2;->c()Landroidx/media3/exoplayer/z1;

    move-result-object v12

    invoke-virtual {v12}, Landroidx/media3/exoplayer/z1;->o()Lx2/f0;

    move-result-object v13

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v3, v1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v1, v12, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v2, v1, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    iget-object v0, v0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v4, v0, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    const-wide v5, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v7, 0x1

    const/4 v7, 0x0

    move-object v0, p0

    move-object v1, v3

    invoke-virtual/range {v0 .. v7}, Landroidx/media3/exoplayer/s1;->w1(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JZ)V

    iget-boolean v0, v12, Landroidx/media3/exoplayer/z1;->d:Z

    if-eqz v0, :cond_5

    iget-object v0, v12, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    invoke-interface {v0}, Landroidx/media3/exoplayer/source/k;->readDiscontinuity()J

    move-result-wide v0

    cmp-long v2, v0, v8

    if-eqz v2, :cond_5

    invoke-virtual {v12}, Landroidx/media3/exoplayer/z1;->m()J

    move-result-wide v0

    invoke-virtual {p0, v0, v1}, Landroidx/media3/exoplayer/s1;->O0(J)V

    invoke-virtual {v12}, Landroidx/media3/exoplayer/z1;->q()Z

    move-result v0

    if-nez v0, :cond_4

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0, v12}, Landroidx/media3/exoplayer/c2;->D(Landroidx/media3/exoplayer/z1;)Z

    invoke-virtual {p0, v10}, Landroidx/media3/exoplayer/s1;->J(Z)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->Y()V

    :cond_4
    return-void

    :cond_5
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v1, v1

    if-ge v0, v1, :cond_9

    invoke-virtual {v11, v0}, Lx2/f0;->c(I)Z

    move-result v1

    invoke-virtual {v13, v0}, Lx2/f0;->c(I)Z

    move-result v2

    if-eqz v1, :cond_8

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    aget-object v1, v1, v0

    invoke-interface {v1}, Landroidx/media3/exoplayer/w2;->isCurrentStreamFinal()Z

    move-result v1

    if-nez v1, :cond_8

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->c:[Landroidx/media3/exoplayer/y2;

    aget-object v1, v1, v0

    invoke-interface {v1}, Landroidx/media3/exoplayer/y2;->getTrackType()I

    move-result v1

    const/4 v3, -0x2

    if-ne v1, v3, :cond_6

    const/4 v1, 0x1

    goto :goto_1

    :cond_6
    const/4 v1, 0x1

    const/4 v1, 0x0

    :goto_1
    iget-object v3, v11, Lx2/f0;->b:[Landroidx/media3/exoplayer/z2;

    aget-object v3, v3, v0

    iget-object v4, v13, Lx2/f0;->b:[Landroidx/media3/exoplayer/z2;

    aget-object v4, v4, v0

    if-eqz v2, :cond_7

    invoke-virtual {v4, v3}, Landroidx/media3/exoplayer/z2;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_7

    if-eqz v1, :cond_8

    :cond_7
    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    aget-object v1, v1, v0

    invoke-virtual {v12}, Landroidx/media3/exoplayer/z1;->m()J

    move-result-wide v2

    invoke-virtual {p0, v1, v2, v3}, Landroidx/media3/exoplayer/s1;->P0(Landroidx/media3/exoplayer/w2;J)V

    :cond_8
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_9
    return-void

    :cond_a
    :goto_2
    iget-object v1, v0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-boolean v1, v1, Landroidx/media3/exoplayer/a2;->i:Z

    if-nez v1, :cond_b

    iget-boolean v1, p0, Landroidx/media3/exoplayer/s1;->C:Z

    if-eqz v1, :cond_e

    :cond_b
    :goto_3
    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v2, v1

    if-ge v10, v2, :cond_e

    aget-object v1, v1, v10

    iget-object v2, v0, Landroidx/media3/exoplayer/z1;->c:[Lu2/e0;

    aget-object v2, v2, v10

    if-eqz v2, :cond_d

    invoke-interface {v1}, Landroidx/media3/exoplayer/w2;->getStream()Lu2/e0;

    move-result-object v3

    if-ne v3, v2, :cond_d

    invoke-interface {v1}, Landroidx/media3/exoplayer/w2;->hasReadStreamToEnd()Z

    move-result v2

    if-eqz v2, :cond_d

    iget-object v2, v0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-wide v2, v2, Landroidx/media3/exoplayer/a2;->e:J

    cmp-long v4, v2, v8

    if-eqz v4, :cond_c

    const-wide/high16 v4, -0x8000000000000000L

    cmp-long v6, v2, v4

    if-eqz v6, :cond_c

    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->l()J

    move-result-wide v2

    iget-object v4, v0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-wide v4, v4, Landroidx/media3/exoplayer/a2;->e:J

    add-long/2addr v2, v4

    goto :goto_4

    :cond_c
    move-wide v2, v8

    :goto_4
    invoke-virtual {p0, v1, v2, v3}, Landroidx/media3/exoplayer/s1;->P0(Landroidx/media3/exoplayer/w2;J)V

    :cond_d
    add-int/lit8 v10, v10, 0x1

    goto :goto_3

    :cond_e
    return-void
.end method

.method public final e1(Z)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iput-boolean p1, p0, Landroidx/media3/exoplayer/s1;->H:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v1, v1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v0, v1, p1}, Landroidx/media3/exoplayer/c2;->L(Landroidx/media3/common/m0;Z)Z

    move-result p1

    if-nez p1, :cond_0

    const/4 p1, 0x1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/s1;->H0(Z)V

    :cond_0
    const/4 p1, 0x1

    const/4 p1, 0x0

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/s1;->J(Z)V

    return-void
.end method

.method public final f0()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->s()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    if-eqz v0, :cond_1

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v1

    if-eq v1, v0, :cond_1

    iget-boolean v0, v0, Landroidx/media3/exoplayer/z1;->g:Z

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->t0()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->u()V

    :cond_1
    :goto_0
    return-void
.end method

.method public final f1(Lu2/f0;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/s1$e;->b(I)V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->u:Landroidx/media3/exoplayer/r2;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/r2;->E(Lu2/f0;)Landroidx/media3/common/m0;

    move-result-object p1

    const/4 v0, 0x1

    const/4 v0, 0x0

    invoke-virtual {p0, p1, v0}, Landroidx/media3/exoplayer/s1;->K(Landroidx/media3/common/m0;Z)V

    return-void
.end method

.method public g(Landroidx/media3/exoplayer/source/k;)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/16 v1, 0x8

    invoke-interface {v0, v1, p1}, Le2/j;->obtainMessage(ILjava/lang/Object;)Le2/j$a;

    move-result-object p1

    invoke-interface {p1}, Le2/j$a;->a()V

    return-void
.end method

.method public final g0()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->u:Landroidx/media3/exoplayer/r2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/r2;->i()Landroidx/media3/common/m0;

    move-result-object v0

    const/4 v1, 0x1

    invoke-virtual {p0, v0, v1}, Landroidx/media3/exoplayer/s1;->K(Landroidx/media3/common/m0;Z)V

    return-void
.end method

.method public final g1(I)V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget v1, v0, Landroidx/media3/exoplayer/s2;->e:I

    if-eq v1, p1, :cond_1

    const/4 v1, 0x2

    if-eq p1, v1, :cond_0

    const-wide v1, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide v1, p0, Landroidx/media3/exoplayer/s1;->S:J

    :cond_0
    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/s2;->h(I)Landroidx/media3/exoplayer/s2;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    :cond_1
    return-void
.end method

.method public bridge synthetic h(Landroidx/media3/exoplayer/source/t;)V
    .locals 0

    check-cast p1, Landroidx/media3/exoplayer/source/k;

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/s1;->l0(Landroidx/media3/exoplayer/source/k;)V

    return-void
.end method

.method public final h0(Landroidx/media3/exoplayer/s1$c;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/s1$e;->b(I)V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->u:Landroidx/media3/exoplayer/r2;

    iget v1, p1, Landroidx/media3/exoplayer/s1$c;->a:I

    iget v2, p1, Landroidx/media3/exoplayer/s1$c;->b:I

    iget v3, p1, Landroidx/media3/exoplayer/s1$c;->c:I

    iget-object p1, p1, Landroidx/media3/exoplayer/s1$c;->d:Lu2/f0;

    invoke-virtual {v0, v1, v2, v3, p1}, Landroidx/media3/exoplayer/r2;->w(IIILu2/f0;)Landroidx/media3/common/m0;

    move-result-object p1

    const/4 v0, 0x1

    const/4 v0, 0x0

    invoke-virtual {p0, p1, v0}, Landroidx/media3/exoplayer/s1;->K(Landroidx/media3/common/m0;Z)V

    return-void
.end method

.method public final h1()Z
    .locals 7

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->j1()Z

    move-result v0

    const/4 v1, 0x1

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    :cond_0
    iget-boolean v0, p0, Landroidx/media3/exoplayer/s1;->C:Z

    if-eqz v0, :cond_1

    return v1

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    if-nez v0, :cond_2

    return v1

    :cond_2
    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->j()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    if-eqz v0, :cond_3

    iget-wide v2, p0, Landroidx/media3/exoplayer/s1;->N:J

    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->m()J

    move-result-wide v4

    cmp-long v6, v2, v4

    if-ltz v6, :cond_3

    iget-boolean v0, v0, Landroidx/media3/exoplayer/z1;->g:Z

    if-eqz v0, :cond_3

    const/4 v1, 0x1

    :cond_3
    return v1
.end method

.method public handleMessage(Landroid/os/Message;)Z
    .locals 13

    const-string v1, "Playback error"

    const-string v2, "ExoPlayerImplInternal"

    const/16 v3, 0x3e8

    const/4 v11, 0x1

    const/4 v11, 0x0

    const/4 v12, 0x1

    :try_start_0
    iget v4, p1, Landroid/os/Message;->what:I

    packed-switch v4, :pswitch_data_0

    :pswitch_0
    return v11

    :pswitch_1
    iget v4, p1, Landroid/os/Message;->arg1:I

    iget v5, p1, Landroid/os/Message;->arg2:I

    iget-object v0, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast v0, Ljava/util/List;

    invoke-virtual {p0, v4, v5, v0}, Landroidx/media3/exoplayer/s1;->t1(IILjava/util/List;)V

    goto/16 :goto_e

    :catch_0
    move-exception v0

    goto/16 :goto_4

    :catch_1
    move-exception v0

    goto/16 :goto_5

    :catch_2
    move-exception v0

    goto/16 :goto_6

    :catch_3
    move-exception v0

    goto/16 :goto_7

    :catch_4
    move-exception v0

    goto/16 :goto_8

    :catch_5
    move-exception v0

    goto/16 :goto_a

    :catch_6
    move-exception v0

    goto/16 :goto_b

    :pswitch_2
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->v0()V

    goto/16 :goto_e

    :pswitch_3
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->o()V

    goto/16 :goto_e

    :pswitch_4
    iget v0, p1, Landroid/os/Message;->arg1:I

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->V0(Z)V

    goto/16 :goto_e

    :pswitch_5
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->g0()V

    goto/16 :goto_e

    :pswitch_6
    iget-object v0, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast v0, Lu2/f0;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->f1(Lu2/f0;)V

    goto/16 :goto_e

    :pswitch_7
    iget v4, p1, Landroid/os/Message;->arg1:I

    iget v5, p1, Landroid/os/Message;->arg2:I

    iget-object v0, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast v0, Lu2/f0;

    invoke-virtual {p0, v4, v5, v0}, Landroidx/media3/exoplayer/s1;->r0(IILu2/f0;)V

    goto/16 :goto_e

    :pswitch_8
    iget-object v0, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast v0, Landroidx/media3/exoplayer/s1$c;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->h0(Landroidx/media3/exoplayer/s1$c;)V

    goto/16 :goto_e

    :pswitch_9
    iget-object v4, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast v4, Landroidx/media3/exoplayer/s1$b;

    iget v0, p1, Landroid/os/Message;->arg1:I

    invoke-virtual {p0, v4, v0}, Landroidx/media3/exoplayer/s1;->l(Landroidx/media3/exoplayer/s1$b;I)V

    goto/16 :goto_e

    :pswitch_a
    iget-object v0, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast v0, Landroidx/media3/exoplayer/s1$b;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->S0(Landroidx/media3/exoplayer/s1$b;)V

    goto/16 :goto_e

    :pswitch_b
    iget-object v0, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast v0, Landroidx/media3/common/g0;

    invoke-virtual {p0, v0, v11}, Landroidx/media3/exoplayer/s1;->N(Landroidx/media3/common/g0;Z)V

    goto/16 :goto_e

    :pswitch_c
    iget-object v0, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast v0, Landroidx/media3/exoplayer/t2;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->N0(Landroidx/media3/exoplayer/t2;)V

    goto/16 :goto_e

    :pswitch_d
    iget-object v0, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast v0, Landroidx/media3/exoplayer/t2;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->L0(Landroidx/media3/exoplayer/t2;)V

    goto/16 :goto_e

    :pswitch_e
    iget v4, p1, Landroid/os/Message;->arg1:I

    if-eqz v4, :cond_1

    const/4 v4, 0x1

    goto :goto_1

    :cond_1
    const/4 v4, 0x1

    const/4 v4, 0x0

    :goto_1
    iget-object v0, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast v0, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {p0, v4, v0}, Landroidx/media3/exoplayer/s1;->Q0(ZLjava/util/concurrent/atomic/AtomicBoolean;)V

    goto/16 :goto_e

    :pswitch_f
    iget v0, p1, Landroid/os/Message;->arg1:I

    if-eqz v0, :cond_2

    const/4 v0, 0x1

    goto :goto_2

    :cond_2
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_2
    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->e1(Z)V

    goto/16 :goto_e

    :pswitch_10
    iget v0, p1, Landroid/os/Message;->arg1:I

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->b1(I)V

    goto/16 :goto_e

    :pswitch_11
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->u0()V

    goto/16 :goto_e

    :pswitch_12
    iget-object v0, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast v0, Landroidx/media3/exoplayer/source/k;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->H(Landroidx/media3/exoplayer/source/k;)V

    goto/16 :goto_e

    :pswitch_13
    iget-object v0, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast v0, Landroidx/media3/exoplayer/source/k;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->L(Landroidx/media3/exoplayer/source/k;)V

    goto/16 :goto_e

    :pswitch_14
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->p0()V

    return v12

    :pswitch_15
    invoke-virtual {p0, v11, v12}, Landroidx/media3/exoplayer/s1;->o1(ZZ)V

    goto/16 :goto_e

    :pswitch_16
    iget-object v0, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast v0, Landroidx/media3/exoplayer/b3;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->c1(Landroidx/media3/exoplayer/b3;)V

    goto/16 :goto_e

    :pswitch_17
    iget-object v0, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast v0, Landroidx/media3/common/g0;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->Z0(Landroidx/media3/common/g0;)V

    goto/16 :goto_e

    :pswitch_18
    iget-object v0, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    check-cast v0, Landroidx/media3/exoplayer/s1$h;

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->I0(Landroidx/media3/exoplayer/s1$h;)V

    goto/16 :goto_e

    :pswitch_19
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->s()V

    goto/16 :goto_e

    :pswitch_1a
    iget v4, p1, Landroid/os/Message;->arg1:I

    if-eqz v4, :cond_3

    const/4 v4, 0x1

    goto :goto_3

    :cond_3
    const/4 v4, 0x1

    const/4 v4, 0x0

    :goto_3
    iget v0, p1, Landroid/os/Message;->arg2:I

    invoke-virtual {p0, v4, v0, v12, v12}, Landroidx/media3/exoplayer/s1;->X0(ZIZI)V

    goto/16 :goto_e

    :pswitch_1b
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->n0()V
    :try_end_0
    .catch Landroidx/media3/exoplayer/ExoPlaybackException; {:try_start_0 .. :try_end_0} :catch_6
    .catch Landroidx/media3/exoplayer/drm/DrmSession$DrmSessionException; {:try_start_0 .. :try_end_0} :catch_5
    .catch Landroidx/media3/common/ParserException; {:try_start_0 .. :try_end_0} :catch_4
    .catch Landroidx/media3/datasource/DataSourceException; {:try_start_0 .. :try_end_0} :catch_3
    .catch Landroidx/media3/exoplayer/source/BehindLiveWindowException; {:try_start_0 .. :try_end_0} :catch_2
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/RuntimeException; {:try_start_0 .. :try_end_0} :catch_0

    goto/16 :goto_e

    :goto_4
    instance-of v4, v0, Ljava/lang/IllegalStateException;

    if-nez v4, :cond_4

    instance-of v4, v0, Ljava/lang/IllegalArgumentException;

    if-eqz v4, :cond_5

    :cond_4
    const/16 v3, 0x3ec

    :cond_5
    invoke-static {v0, v3}, Landroidx/media3/exoplayer/ExoPlaybackException;->createForUnexpected(Ljava/lang/RuntimeException;I)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object v0

    invoke-static {v2, v1, v0}, Le2/o;->d(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    invoke-virtual {p0, v12, v11}, Landroidx/media3/exoplayer/s1;->o1(ZZ)V

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual {v1, v0}, Landroidx/media3/exoplayer/s2;->f(Landroidx/media3/exoplayer/ExoPlaybackException;)Landroidx/media3/exoplayer/s2;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    goto/16 :goto_e

    :goto_5
    const/16 v1, 0x7d0

    invoke-virtual {p0, v0, v1}, Landroidx/media3/exoplayer/s1;->I(Ljava/io/IOException;I)V

    goto/16 :goto_e

    :goto_6
    const/16 v1, 0x3ea

    invoke-virtual {p0, v0, v1}, Landroidx/media3/exoplayer/s1;->I(Ljava/io/IOException;I)V

    goto/16 :goto_e

    :goto_7
    iget v1, v0, Landroidx/media3/datasource/DataSourceException;->reason:I

    invoke-virtual {p0, v0, v1}, Landroidx/media3/exoplayer/s1;->I(Ljava/io/IOException;I)V

    goto/16 :goto_e

    :goto_8
    iget v1, v0, Landroidx/media3/common/ParserException;->dataType:I

    if-ne v1, v12, :cond_7

    iget-boolean v1, v0, Landroidx/media3/common/ParserException;->contentIsMalformed:Z

    if-eqz v1, :cond_6

    const/16 v1, 0xbb9

    const/16 v3, 0xbb9

    goto :goto_9

    :cond_6
    const/16 v1, 0xbbb

    const/16 v3, 0xbbb

    goto :goto_9

    :cond_7
    const/4 v2, 0x4

    if-ne v1, v2, :cond_9

    iget-boolean v1, v0, Landroidx/media3/common/ParserException;->contentIsMalformed:Z

    if-eqz v1, :cond_8

    const/16 v1, 0xbba

    const/16 v3, 0xbba

    goto :goto_9

    :cond_8
    const/16 v1, 0xbbc

    const/16 v3, 0xbbc

    :cond_9
    :goto_9
    invoke-virtual {p0, v0, v3}, Landroidx/media3/exoplayer/s1;->I(Ljava/io/IOException;I)V

    goto/16 :goto_e

    :goto_a
    iget v1, v0, Landroidx/media3/exoplayer/drm/DrmSession$DrmSessionException;->errorCode:I

    invoke-virtual {p0, v0, v1}, Landroidx/media3/exoplayer/s1;->I(Ljava/io/IOException;I)V

    goto/16 :goto_e

    :goto_b
    iget v3, v0, Landroidx/media3/exoplayer/ExoPlaybackException;->type:I

    if-ne v3, v12, :cond_a

    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v3}, Landroidx/media3/exoplayer/c2;->s()Landroidx/media3/exoplayer/z1;

    move-result-object v3

    if-eqz v3, :cond_a

    iget-object v3, v3, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v3, v3, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v0, v3}, Landroidx/media3/exoplayer/ExoPlaybackException;->copyWithMediaPeriodId(Landroidx/media3/exoplayer/source/l$b;)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object v0

    :cond_a
    iget-boolean v3, v0, Landroidx/media3/exoplayer/ExoPlaybackException;->isRecoverable:Z

    if-eqz v3, :cond_d

    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->Q:Landroidx/media3/exoplayer/ExoPlaybackException;

    if-eqz v3, :cond_b

    iget v3, v0, Landroidx/media3/common/PlaybackException;->errorCode:I

    const/16 v4, 0x138c

    if-eq v3, v4, :cond_b

    const/16 v4, 0x138b

    if-ne v3, v4, :cond_d

    :cond_b
    const-string v1, "Recoverable renderer error"

    invoke-static {v2, v1, v0}, Le2/o;->i(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->Q:Landroidx/media3/exoplayer/ExoPlaybackException;

    if-eqz v1, :cond_c

    invoke-virtual {v1, v0}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->Q:Landroidx/media3/exoplayer/ExoPlaybackException;

    goto :goto_c

    :cond_c
    iput-object v0, p0, Landroidx/media3/exoplayer/s1;->Q:Landroidx/media3/exoplayer/ExoPlaybackException;

    :goto_c
    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/16 v2, 0x19

    invoke-interface {v1, v2, v0}, Le2/j;->obtainMessage(ILjava/lang/Object;)Le2/j$a;

    move-result-object v0

    invoke-interface {v1, v0}, Le2/j;->b(Le2/j$a;)Z

    goto :goto_e

    :cond_d
    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->Q:Landroidx/media3/exoplayer/ExoPlaybackException;

    if-eqz v3, :cond_e

    invoke-virtual {v3, v0}, Ljava/lang/Throwable;->addSuppressed(Ljava/lang/Throwable;)V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->Q:Landroidx/media3/exoplayer/ExoPlaybackException;

    :cond_e
    invoke-static {v2, v1, v0}, Le2/o;->d(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    iget v1, v0, Landroidx/media3/exoplayer/ExoPlaybackException;->type:I

    if-ne v1, v12, :cond_10

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v1

    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v2}, Landroidx/media3/exoplayer/c2;->s()Landroidx/media3/exoplayer/z1;

    move-result-object v2

    if-eq v1, v2, :cond_10

    :goto_d
    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v1

    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v2}, Landroidx/media3/exoplayer/c2;->s()Landroidx/media3/exoplayer/z1;

    move-result-object v2

    if-eq v1, v2, :cond_f

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/c2;->b()Landroidx/media3/exoplayer/z1;

    goto :goto_d

    :cond_f
    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v1

    invoke-static {v1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/exoplayer/z1;

    iget-object v1, v1, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v2, v1, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v7, v1, Landroidx/media3/exoplayer/a2;->b:J

    iget-wide v5, v1, Landroidx/media3/exoplayer/a2;->c:J

    const/4 v9, 0x1

    const/4 v10, 0x1

    const/4 v10, 0x0

    move-object v1, p0

    move-wide v3, v7

    invoke-virtual/range {v1 .. v10}, Landroidx/media3/exoplayer/s1;->O(Landroidx/media3/exoplayer/source/l$b;JJJZI)Landroidx/media3/exoplayer/s2;

    move-result-object v1

    iput-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    :cond_10
    invoke-virtual {p0, v12, v11}, Landroidx/media3/exoplayer/s1;->o1(ZZ)V

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual {v1, v0}, Landroidx/media3/exoplayer/s2;->f(Landroidx/media3/exoplayer/ExoPlaybackException;)Landroidx/media3/exoplayer/s2;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    :goto_e
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->Z()V

    return v12

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_1b
        :pswitch_1a
        :pswitch_19
        :pswitch_18
        :pswitch_17
        :pswitch_16
        :pswitch_15
        :pswitch_14
        :pswitch_13
        :pswitch_12
        :pswitch_11
        :pswitch_10
        :pswitch_f
        :pswitch_e
        :pswitch_d
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_0
        :pswitch_3
        :pswitch_2
        :pswitch_1
    .end packed-switch
.end method

.method public final i0()V
    .locals 5

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    :goto_0
    if-eqz v0, :cond_2

    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->o()Lx2/f0;

    move-result-object v1

    iget-object v1, v1, Lx2/f0;->c:[Lx2/z;

    array-length v2, v1

    const/4 v3, 0x1

    const/4 v3, 0x0

    :goto_1
    if-ge v3, v2, :cond_1

    aget-object v4, v1, v3

    if-eqz v4, :cond_0

    invoke-interface {v4}, Lx2/z;->a()V

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    :cond_1
    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->j()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    goto :goto_0

    :cond_2
    return-void
.end method

.method public final i1()Z
    .locals 12

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->S()Z

    move-result v0

    const/4 v1, 0x1

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->l()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->k()J

    move-result-wide v2

    invoke-virtual {p0, v2, v3}, Landroidx/media3/exoplayer/s1;->G(J)J

    move-result-wide v2

    iget-object v4, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v4}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v4

    if-ne v0, v4, :cond_1

    iget-wide v4, p0, Landroidx/media3/exoplayer/s1;->N:J

    invoke-virtual {v0, v4, v5}, Landroidx/media3/exoplayer/z1;->y(J)J

    move-result-wide v4

    :goto_0
    move-wide v10, v4

    goto :goto_1

    :cond_1
    iget-wide v4, p0, Landroidx/media3/exoplayer/s1;->N:J

    invoke-virtual {v0, v4, v5}, Landroidx/media3/exoplayer/z1;->y(J)J

    move-result-wide v4

    iget-object v0, v0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-wide v6, v0, Landroidx/media3/exoplayer/a2;->b:J

    sub-long/2addr v4, v6

    goto :goto_0

    :goto_1
    iget-object v4, p0, Landroidx/media3/exoplayer/s1;->g:Landroidx/media3/exoplayer/v1;

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/r;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object v0

    iget v9, v0, Landroidx/media3/common/g0;->a:F

    move-wide v5, v10

    move-wide v7, v2

    invoke-interface/range {v4 .. v9}, Landroidx/media3/exoplayer/v1;->a(JJF)Z

    move-result v0

    if-nez v0, :cond_3

    const-wide/32 v4, 0x7a120

    cmp-long v6, v2, v4

    if-gez v6, :cond_3

    iget-wide v4, p0, Landroidx/media3/exoplayer/s1;->n:J

    const-wide/16 v6, 0x0

    cmp-long v8, v4, v6

    if-gtz v8, :cond_2

    iget-boolean v4, p0, Landroidx/media3/exoplayer/s1;->o:Z

    if-eqz v4, :cond_3

    :cond_2
    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    iget-object v0, v0, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    iget-object v4, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v4, v4, Landroidx/media3/exoplayer/s2;->r:J

    invoke-interface {v0, v4, v5, v1}, Landroidx/media3/exoplayer/source/k;->discardBuffer(JZ)V

    iget-object v4, p0, Landroidx/media3/exoplayer/s1;->g:Landroidx/media3/exoplayer/v1;

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/r;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object v0

    iget v9, v0, Landroidx/media3/common/g0;->a:F

    move-wide v5, v10

    move-wide v7, v2

    invoke-interface/range {v4 .. v9}, Landroidx/media3/exoplayer/v1;->a(JJF)Z

    move-result v0

    :cond_3
    return v0
.end method

.method public final j0(Z)V
    .locals 5

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    :goto_0
    if-eqz v0, :cond_2

    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->o()Lx2/f0;

    move-result-object v1

    iget-object v1, v1, Lx2/f0;->c:[Lx2/z;

    array-length v2, v1

    const/4 v3, 0x1

    const/4 v3, 0x0

    :goto_1
    if-ge v3, v2, :cond_1

    aget-object v4, v1, v3

    if-eqz v4, :cond_0

    invoke-interface {v4, p1}, Lx2/z;->b(Z)V

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    :cond_1
    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->j()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    goto :goto_0

    :cond_2
    return-void
.end method

.method public final j1()Z
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-boolean v1, v0, Landroidx/media3/exoplayer/s2;->l:Z

    if-eqz v1, :cond_0

    iget v0, v0, Landroidx/media3/exoplayer/s2;->m:I

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final k0()V
    .locals 5

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    :goto_0
    if-eqz v0, :cond_2

    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->o()Lx2/f0;

    move-result-object v1

    iget-object v1, v1, Lx2/f0;->c:[Lx2/z;

    array-length v2, v1

    const/4 v3, 0x1

    const/4 v3, 0x0

    :goto_1
    if-ge v3, v2, :cond_1

    aget-object v4, v1, v3

    if-eqz v4, :cond_0

    invoke-interface {v4}, Lx2/z;->c()V

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    :cond_1
    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->j()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    goto :goto_0

    :cond_2
    return-void
.end method

.method public final k1(Z)Z
    .locals 13

    iget v0, p0, Landroidx/media3/exoplayer/s1;->L:I

    if-nez v0, :cond_0

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->U()Z

    move-result p1

    return p1

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    if-nez p1, :cond_1

    return v0

    :cond_1
    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-boolean p1, p1, Landroidx/media3/exoplayer/s2;->g:Z

    const/4 v1, 0x1

    if-nez p1, :cond_2

    return v1

    :cond_2
    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object p1

    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v2, v2, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v3, p1, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v3, v3, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p0, v2, v3}, Landroidx/media3/exoplayer/s1;->l1(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;)Z

    move-result v2

    if-eqz v2, :cond_3

    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->v:Landroidx/media3/exoplayer/u1;

    invoke-interface {v2}, Landroidx/media3/exoplayer/u1;->b()J

    move-result-wide v2

    :goto_0
    move-wide v11, v2

    goto :goto_1

    :cond_3
    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    goto :goto_0

    :goto_1
    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v2}, Landroidx/media3/exoplayer/c2;->l()Landroidx/media3/exoplayer/z1;

    move-result-object v2

    invoke-virtual {v2}, Landroidx/media3/exoplayer/z1;->q()Z

    move-result v3

    if-eqz v3, :cond_4

    iget-object v3, v2, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-boolean v3, v3, Landroidx/media3/exoplayer/a2;->i:Z

    if-eqz v3, :cond_4

    const/4 v3, 0x1

    goto :goto_2

    :cond_4
    const/4 v3, 0x1

    const/4 v3, 0x0

    :goto_2
    iget-object v4, v2, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v4, v4, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v4}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v4

    if-eqz v4, :cond_5

    iget-boolean v2, v2, Landroidx/media3/exoplayer/z1;->d:Z

    if-nez v2, :cond_5

    const/4 v2, 0x1

    goto :goto_3

    :cond_5
    const/4 v2, 0x1

    const/4 v2, 0x0

    :goto_3
    if-nez v3, :cond_6

    if-nez v2, :cond_6

    iget-object v4, p0, Landroidx/media3/exoplayer/s1;->g:Landroidx/media3/exoplayer/v1;

    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v5, v2, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object p1, p1, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v6, p1, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->F()J

    move-result-wide v7

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/r;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object p1

    iget v9, p1, Landroidx/media3/common/g0;->a:F

    iget-boolean v10, p0, Landroidx/media3/exoplayer/s1;->D:Z

    invoke-interface/range {v4 .. v12}, Landroidx/media3/exoplayer/v1;->c(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JFZJ)Z

    move-result p1

    if-eqz p1, :cond_7

    :cond_6
    const/4 v0, 0x1

    :cond_7
    return v0
.end method

.method public final l(Landroidx/media3/exoplayer/s1$b;I)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/s1$e;->b(I)V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->u:Landroidx/media3/exoplayer/r2;

    const/4 v1, -0x1

    if-ne p2, v1, :cond_0

    invoke-virtual {v0}, Landroidx/media3/exoplayer/r2;->r()I

    move-result p2

    :cond_0
    invoke-static {p1}, Landroidx/media3/exoplayer/s1$b;->b(Landroidx/media3/exoplayer/s1$b;)Ljava/util/List;

    move-result-object v1

    invoke-static {p1}, Landroidx/media3/exoplayer/s1$b;->c(Landroidx/media3/exoplayer/s1$b;)Lu2/f0;

    move-result-object p1

    invoke-virtual {v0, p2, v1, p1}, Landroidx/media3/exoplayer/r2;->f(ILjava/util/List;Lu2/f0;)Landroidx/media3/common/m0;

    move-result-object p1

    const/4 p2, 0x1

    const/4 p2, 0x0

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/s1;->K(Landroidx/media3/common/m0;Z)V

    return-void
.end method

.method public l0(Landroidx/media3/exoplayer/source/k;)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/16 v1, 0x9

    invoke-interface {v0, v1, p1}, Le2/j;->obtainMessage(ILjava/lang/Object;)Le2/j$a;

    move-result-object p1

    invoke-interface {p1}, Le2/j$a;->a()V

    return-void
.end method

.method public final l1(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;)Z
    .locals 4

    invoke-virtual {p2}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v0

    const/4 v1, 0x1

    const/4 v1, 0x0

    if-nez v0, :cond_1

    invoke-virtual {p1}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object p2, p2, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    invoke-virtual {p1, p2, v0}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object p2

    iget p2, p2, Landroidx/media3/common/m0$b;->c:I

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    invoke-virtual {p1, p2, v0}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    invoke-virtual {p1}, Landroidx/media3/common/m0$c;->f()Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    iget-boolean p2, p1, Landroidx/media3/common/m0$c;->i:Z

    if-eqz p2, :cond_1

    iget-wide p1, p1, Landroidx/media3/common/m0$c;->f:J

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v0, p1, v2

    if-eqz v0, :cond_1

    const/4 v1, 0x1

    :cond_1
    :goto_0
    return v1
.end method

.method public m(ILjava/util/List;Lu2/f0;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Landroidx/media3/exoplayer/r2$c;",
            ">;",
            "Lu2/f0;",
            ")V"
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    new-instance v8, Landroidx/media3/exoplayer/s1$b;

    const/4 v4, -0x1

    const-wide v5, -0x7fffffffffffffffL    # -4.9E-324

    const/4 v7, 0x1

    const/4 v7, 0x0

    move-object v1, v8

    move-object v2, p2

    move-object v3, p3

    invoke-direct/range {v1 .. v7}, Landroidx/media3/exoplayer/s1$b;-><init>(Ljava/util/List;Lu2/f0;IJLandroidx/media3/exoplayer/s1$a;)V

    const/16 p2, 0x12

    const/4 p3, 0x1

    const/4 p3, 0x0

    invoke-interface {v0, p2, p1, p3, v8}, Le2/j;->obtainMessage(IIILjava/lang/Object;)Le2/j$a;

    move-result-object p1

    invoke-interface {p1}, Le2/j$a;->a()V

    return-void
.end method

.method public m0()V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/4 v1, 0x1

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Le2/j;->obtainMessage(I)Le2/j$a;

    move-result-object v0

    invoke-interface {v0}, Le2/j$a;->a()V

    return-void
.end method

.method public final m1()V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->o()Lx2/f0;

    move-result-object v0

    const/4 v1, 0x1

    const/4 v1, 0x0

    :goto_0
    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v2, v2

    if-ge v1, v2, :cond_2

    invoke-virtual {v0, v1}, Lx2/f0;->c(I)Z

    move-result v2

    if-eqz v2, :cond_1

    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    aget-object v2, v2, v1

    invoke-interface {v2}, Landroidx/media3/exoplayer/w2;->getState()I

    move-result v2

    const/4 v3, 0x1

    if-ne v2, v3, :cond_1

    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    aget-object v2, v2, v1

    invoke-interface {v2}, Landroidx/media3/exoplayer/w2;->start()V

    :cond_1
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_2
    return-void
.end method

.method public final n()V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->o()Lx2/f0;

    move-result-object v0

    const/4 v1, 0x1

    const/4 v1, 0x0

    :goto_0
    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v2, v2

    if-ge v1, v2, :cond_1

    invoke-virtual {v0, v1}, Lx2/f0;->c(I)Z

    move-result v2

    if-eqz v2, :cond_0

    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    aget-object v2, v2, v1

    invoke-interface {v2}, Landroidx/media3/exoplayer/w2;->f()V

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public final n0()V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/s1$e;->b(I)V

    const/4 v0, 0x1

    const/4 v0, 0x0

    invoke-virtual {p0, v0, v0, v0, v1}, Landroidx/media3/exoplayer/s1;->w0(ZZZZ)V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->g:Landroidx/media3/exoplayer/v1;

    invoke-interface {v0}, Landroidx/media3/exoplayer/v1;->onPrepared()V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    const/4 v1, 0x2

    if-eqz v0, :cond_0

    const/4 v0, 0x4

    goto :goto_0

    :cond_0
    const/4 v0, 0x2

    :goto_0
    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->g1(I)V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->u:Landroidx/media3/exoplayer/r2;

    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->h:Landroidx/media3/exoplayer/upstream/e;

    invoke-interface {v2}, Landroidx/media3/exoplayer/upstream/e;->b()Lh2/o;

    move-result-object v2

    invoke-virtual {v0, v2}, Landroidx/media3/exoplayer/r2;->x(Lh2/o;)V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    invoke-interface {v0, v1}, Le2/j;->sendEmptyMessage(I)Z

    return-void
.end method

.method public n1()V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/4 v1, 0x6

    invoke-interface {v0, v1}, Le2/j;->obtainMessage(I)Le2/j$a;

    move-result-object v0

    invoke-interface {v0}, Le2/j$a;->a()V

    return-void
.end method

.method public final o()V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->v0()V

    return-void
.end method

.method public declared-synchronized o0()Z
    .locals 3

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Landroidx/media3/exoplayer/s1;->A:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->k:Landroid/os/Looper;

    invoke-virtual {v0}, Landroid/os/Looper;->getThread()Ljava/lang/Thread;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Thread;->isAlive()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/4 v1, 0x7

    invoke-interface {v0, v1}, Le2/j;->sendEmptyMessage(I)Z

    new-instance v0, Landroidx/media3/exoplayer/q1;

    invoke-direct {v0, p0}, Landroidx/media3/exoplayer/q1;-><init>(Landroidx/media3/exoplayer/s1;)V

    iget-wide v1, p0, Landroidx/media3/exoplayer/s1;->w:J

    invoke-virtual {p0, v0, v1, v2}, Landroidx/media3/exoplayer/s1;->z1(Lcom/google/common/base/q;J)V

    iget-boolean v0, p0, Landroidx/media3/exoplayer/s1;->A:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return v0

    :catchall_0
    move-exception v0

    goto :goto_1

    :cond_1
    :goto_0
    monitor-exit p0

    const/4 v0, 0x1

    return v0

    :goto_1
    monitor-exit p0

    throw v0
.end method

.method public final o1(ZZ)V
    .locals 2

    const/4 v0, 0x1

    const/4 v1, 0x1

    const/4 v1, 0x0

    if-nez p1, :cond_1

    iget-boolean p1, p0, Landroidx/media3/exoplayer/s1;->I:Z

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x1

    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    invoke-virtual {p0, p1, v1, v0, v1}, Landroidx/media3/exoplayer/s1;->w0(ZZZZ)V

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    invoke-virtual {p1, p2}, Landroidx/media3/exoplayer/s1$e;->b(I)V

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->g:Landroidx/media3/exoplayer/v1;

    invoke-interface {p1}, Landroidx/media3/exoplayer/v1;->onStopped()V

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->g1(I)V

    return-void
.end method

.method public onPlaybackParametersChanged(Landroidx/media3/common/g0;)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/16 v1, 0x10

    invoke-interface {v0, v1, p1}, Le2/j;->obtainMessage(ILjava/lang/Object;)Le2/j$a;

    move-result-object p1

    invoke-interface {p1}, Le2/j$a;->a()V

    return-void
.end method

.method public onTrackSelectionsInvalidated()V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/16 v1, 0xa

    invoke-interface {v0, v1}, Le2/j;->sendEmptyMessage(I)Z

    return-void
.end method

.method public final p(Landroidx/media3/exoplayer/a2;J)Landroidx/media3/exoplayer/z1;
    .locals 10

    new-instance v9, Landroidx/media3/exoplayer/z1;

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->c:[Landroidx/media3/exoplayer/y2;

    iget-object v4, p0, Landroidx/media3/exoplayer/s1;->d:Lx2/e0;

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->g:Landroidx/media3/exoplayer/v1;

    invoke-interface {v0}, Landroidx/media3/exoplayer/v1;->getAllocator()Landroidx/media3/exoplayer/upstream/b;

    move-result-object v5

    iget-object v6, p0, Landroidx/media3/exoplayer/s1;->u:Landroidx/media3/exoplayer/r2;

    iget-object v8, p0, Landroidx/media3/exoplayer/s1;->f:Lx2/f0;

    move-object v0, v9

    move-wide v2, p2

    move-object v7, p1

    invoke-direct/range {v0 .. v8}, Landroidx/media3/exoplayer/z1;-><init>([Landroidx/media3/exoplayer/y2;JLx2/e0;Landroidx/media3/exoplayer/upstream/b;Landroidx/media3/exoplayer/r2;Landroidx/media3/exoplayer/a2;Lx2/f0;)V

    return-object v9
.end method

.method public final p0()V
    .locals 3

    const/4 v0, 0x1

    const/4 v0, 0x0

    const/4 v1, 0x1

    :try_start_0
    invoke-virtual {p0, v1, v0, v1, v0}, Landroidx/media3/exoplayer/s1;->w0(ZZZZ)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->q0()V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->g:Landroidx/media3/exoplayer/v1;

    invoke-interface {v0}, Landroidx/media3/exoplayer/v1;->onReleased()V

    invoke-virtual {p0, v1}, Landroidx/media3/exoplayer/s1;->g1(I)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->j:Landroid/os/HandlerThread;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Landroid/os/HandlerThread;->quit()Z

    :cond_0
    monitor-enter p0

    :try_start_1
    iput-boolean v1, p0, Landroidx/media3/exoplayer/s1;->A:Z

    invoke-virtual {p0}, Ljava/lang/Object;->notifyAll()V

    monitor-exit p0

    return-void

    :catchall_0
    move-exception v0

    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw v0

    :catchall_1
    move-exception v0

    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->j:Landroid/os/HandlerThread;

    if-eqz v2, :cond_1

    invoke-virtual {v2}, Landroid/os/HandlerThread;->quit()Z

    :cond_1
    monitor-enter p0

    :try_start_2
    iput-boolean v1, p0, Landroidx/media3/exoplayer/s1;->A:Z

    invoke-virtual {p0}, Ljava/lang/Object;->notifyAll()V

    monitor-exit p0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_2

    throw v0

    :catchall_2
    move-exception v0

    :try_start_3
    monitor-exit p0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_2

    throw v0
.end method

.method public final p1()V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/r;->h()V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v1, v0

    const/4 v2, 0x1

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v1, :cond_1

    aget-object v3, v0, v2

    invoke-static {v3}, Landroidx/media3/exoplayer/s1;->T(Landroidx/media3/exoplayer/w2;)Z

    move-result v4

    if-eqz v4, :cond_0

    invoke-virtual {p0, v3}, Landroidx/media3/exoplayer/s1;->w(Landroidx/media3/exoplayer/w2;)V

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    return-void
.end method

.method public final q(Landroidx/media3/exoplayer/t2;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    invoke-virtual {p1}, Landroidx/media3/exoplayer/t2;->j()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x1

    :try_start_0
    invoke-virtual {p1}, Landroidx/media3/exoplayer/t2;->g()Landroidx/media3/exoplayer/t2$b;

    move-result-object v1

    invoke-virtual {p1}, Landroidx/media3/exoplayer/t2;->i()I

    move-result v2

    invoke-virtual {p1}, Landroidx/media3/exoplayer/t2;->e()Ljava/lang/Object;

    move-result-object v3

    invoke-interface {v1, v2, v3}, Landroidx/media3/exoplayer/t2$b;->handleMessage(ILjava/lang/Object;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    invoke-virtual {p1, v0}, Landroidx/media3/exoplayer/t2;->k(Z)V

    return-void

    :catchall_0
    move-exception v1

    invoke-virtual {p1, v0}, Landroidx/media3/exoplayer/t2;->k(Z)V

    throw v1
.end method

.method public final q0()V
    .locals 2

    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v1, v1

    if-ge v0, v1, :cond_0

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->c:[Landroidx/media3/exoplayer/y2;

    aget-object v1, v1, v0

    invoke-interface {v1}, Landroidx/media3/exoplayer/y2;->e()V

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    aget-object v1, v1, v0

    invoke-interface {v1}, Landroidx/media3/exoplayer/w2;->release()V

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-void
.end method

.method public final q1()V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->l()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    iget-boolean v1, p0, Landroidx/media3/exoplayer/s1;->F:Z

    if-nez v1, :cond_1

    if-eqz v0, :cond_0

    iget-object v0, v0, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    invoke-interface {v0}, Landroidx/media3/exoplayer/source/k;->isLoading()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-boolean v2, v1, Landroidx/media3/exoplayer/s2;->g:Z

    if-eq v0, v2, :cond_2

    invoke-virtual {v1, v0}, Landroidx/media3/exoplayer/s2;->b(Z)Landroidx/media3/exoplayer/s2;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    :cond_2
    return-void
.end method

.method public final r(Landroidx/media3/exoplayer/w2;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    invoke-static {p1}, Landroidx/media3/exoplayer/s1;->T(Landroidx/media3/exoplayer/w2;)Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/r;->a(Landroidx/media3/exoplayer/w2;)V

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/s1;->w(Landroidx/media3/exoplayer/w2;)V

    invoke-interface {p1}, Landroidx/media3/exoplayer/w2;->disable()V

    iget p1, p0, Landroidx/media3/exoplayer/s1;->L:I

    add-int/lit8 p1, p1, -0x1

    iput p1, p0, Landroidx/media3/exoplayer/s1;->L:I

    return-void
.end method

.method public final r0(IILu2/f0;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/s1$e;->b(I)V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->u:Landroidx/media3/exoplayer/r2;

    invoke-virtual {v0, p1, p2, p3}, Landroidx/media3/exoplayer/r2;->B(IILu2/f0;)Landroidx/media3/common/m0;

    move-result-object p1

    const/4 p2, 0x1

    const/4 p2, 0x0

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/s1;->K(Landroidx/media3/common/m0;Z)V

    return-void
.end method

.method public final r1(Landroidx/media3/exoplayer/source/l$b;Lu2/k0;Lx2/f0;)V
    .locals 6

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->g:Landroidx/media3/exoplayer/v1;

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v1, v1, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    iget-object v5, p3, Lx2/f0;->c:[Lx2/z;

    move-object v2, p1

    move-object v4, p2

    invoke-interface/range {v0 .. v5}, Landroidx/media3/exoplayer/v1;->b(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;[Landroidx/media3/exoplayer/w2;Lu2/k0;[Lx2/z;)V

    return-void
.end method

.method public final s()V
    .locals 15
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;,
            Ljava/io/IOException;
        }
    .end annotation

    move-object v0, p0

    iget-object v1, v0, Landroidx/media3/exoplayer/s1;->r:Le2/d;

    invoke-interface {v1}, Le2/d;->uptimeMillis()J

    move-result-wide v1

    iget-object v3, v0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/4 v4, 0x2

    invoke-interface {v3, v4}, Le2/j;->removeMessages(I)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->u1()V

    iget-object v3, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget v3, v3, Landroidx/media3/exoplayer/s2;->e:I

    const/4 v5, 0x1

    if-eq v3, v5, :cond_21

    const/4 v6, 0x4

    if-ne v3, v6, :cond_0

    goto/16 :goto_10

    :cond_0
    iget-object v3, v0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v3}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v3

    const-wide/16 v7, 0xa

    if-nez v3, :cond_1

    invoke-virtual {p0, v1, v2, v7, v8}, Landroidx/media3/exoplayer/s1;->F0(JJ)V

    return-void

    :cond_1
    const-string v9, "doSomeWork"

    invoke-static {v9}, Le2/j0;->a(Ljava/lang/String;)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->v1()V

    iget-boolean v9, v3, Landroidx/media3/exoplayer/z1;->d:Z

    const/4 v10, 0x1

    const/4 v10, 0x0

    if-eqz v9, :cond_a

    iget-object v9, v0, Landroidx/media3/exoplayer/s1;->r:Le2/d;

    invoke-interface {v9}, Le2/d;->elapsedRealtime()J

    move-result-wide v11

    invoke-static {v11, v12}, Le2/u0;->S0(J)J

    move-result-wide v11

    iget-object v9, v3, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    iget-object v13, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v13, v13, Landroidx/media3/exoplayer/s2;->r:J

    iget-wide v7, v0, Landroidx/media3/exoplayer/s1;->n:J

    sub-long/2addr v13, v7

    iget-boolean v7, v0, Landroidx/media3/exoplayer/s1;->o:Z

    invoke-interface {v9, v13, v14, v7}, Landroidx/media3/exoplayer/source/k;->discardBuffer(JZ)V

    const/4 v7, 0x1

    const/4 v7, 0x0

    const/4 v8, 0x1

    const/4 v9, 0x1

    :goto_0
    iget-object v13, v0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v14, v13

    if-ge v7, v14, :cond_b

    aget-object v13, v13, v7

    invoke-static {v13}, Landroidx/media3/exoplayer/s1;->T(Landroidx/media3/exoplayer/w2;)Z

    move-result v14

    if-nez v14, :cond_2

    goto :goto_7

    :cond_2
    iget-wide v4, v0, Landroidx/media3/exoplayer/s1;->N:J

    invoke-interface {v13, v4, v5, v11, v12}, Landroidx/media3/exoplayer/w2;->render(JJ)V

    if-eqz v8, :cond_3

    invoke-interface {v13}, Landroidx/media3/exoplayer/w2;->isEnded()Z

    move-result v4

    if-eqz v4, :cond_3

    const/4 v8, 0x1

    goto :goto_1

    :cond_3
    const/4 v8, 0x1

    const/4 v8, 0x0

    :goto_1
    iget-object v4, v3, Landroidx/media3/exoplayer/z1;->c:[Lu2/e0;

    aget-object v4, v4, v7

    invoke-interface {v13}, Landroidx/media3/exoplayer/w2;->getStream()Lu2/e0;

    move-result-object v5

    if-eq v4, v5, :cond_4

    const/4 v4, 0x1

    goto :goto_2

    :cond_4
    const/4 v4, 0x1

    const/4 v4, 0x0

    :goto_2
    if-nez v4, :cond_5

    invoke-interface {v13}, Landroidx/media3/exoplayer/w2;->hasReadStreamToEnd()Z

    move-result v5

    if-eqz v5, :cond_5

    const/4 v5, 0x1

    goto :goto_3

    :cond_5
    const/4 v5, 0x1

    const/4 v5, 0x0

    :goto_3
    if-nez v4, :cond_7

    if-nez v5, :cond_7

    invoke-interface {v13}, Landroidx/media3/exoplayer/w2;->isReady()Z

    move-result v4

    if-nez v4, :cond_7

    invoke-interface {v13}, Landroidx/media3/exoplayer/w2;->isEnded()Z

    move-result v4

    if-eqz v4, :cond_6

    goto :goto_4

    :cond_6
    const/4 v4, 0x1

    const/4 v4, 0x0

    goto :goto_5

    :cond_7
    :goto_4
    const/4 v4, 0x1

    :goto_5
    if-eqz v9, :cond_8

    if-eqz v4, :cond_8

    const/4 v9, 0x1

    goto :goto_6

    :cond_8
    const/4 v9, 0x1

    const/4 v9, 0x0

    :goto_6
    if-nez v4, :cond_9

    invoke-interface {v13}, Landroidx/media3/exoplayer/w2;->maybeThrowStreamError()V

    :cond_9
    :goto_7
    add-int/lit8 v7, v7, 0x1

    const/4 v4, 0x2

    const/4 v5, 0x1

    goto :goto_0

    :cond_a
    iget-object v4, v3, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    invoke-interface {v4}, Landroidx/media3/exoplayer/source/k;->maybeThrowPrepareError()V

    const/4 v8, 0x1

    const/4 v9, 0x1

    :cond_b
    iget-object v4, v3, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-wide v4, v4, Landroidx/media3/exoplayer/a2;->e:J

    const-wide v11, -0x7fffffffffffffffL    # -4.9E-324

    if-eqz v8, :cond_d

    iget-boolean v7, v3, Landroidx/media3/exoplayer/z1;->d:Z

    if-eqz v7, :cond_d

    cmp-long v7, v4, v11

    if-eqz v7, :cond_c

    iget-object v7, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v7, v7, Landroidx/media3/exoplayer/s2;->r:J

    cmp-long v13, v4, v7

    if-gtz v13, :cond_d

    :cond_c
    const/4 v4, 0x1

    goto :goto_8

    :cond_d
    const/4 v4, 0x1

    const/4 v4, 0x0

    :goto_8
    if-eqz v4, :cond_e

    iget-boolean v5, v0, Landroidx/media3/exoplayer/s1;->C:Z

    if-eqz v5, :cond_e

    iput-boolean v10, v0, Landroidx/media3/exoplayer/s1;->C:Z

    iget-object v5, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget v5, v5, Landroidx/media3/exoplayer/s2;->m:I

    const/4 v7, 0x5

    invoke-virtual {p0, v10, v5, v10, v7}, Landroidx/media3/exoplayer/s1;->X0(ZIZI)V

    :cond_e
    const/4 v5, 0x3

    if-eqz v4, :cond_f

    iget-object v4, v3, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-boolean v4, v4, Landroidx/media3/exoplayer/a2;->i:Z

    if-eqz v4, :cond_f

    invoke-virtual {p0, v6}, Landroidx/media3/exoplayer/s1;->g1(I)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->p1()V

    goto :goto_9

    :cond_f
    iget-object v4, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget v4, v4, Landroidx/media3/exoplayer/s2;->e:I

    const/4 v7, 0x2

    if-ne v4, v7, :cond_10

    invoke-virtual {p0, v9}, Landroidx/media3/exoplayer/s1;->k1(Z)Z

    move-result v4

    if-eqz v4, :cond_10

    invoke-virtual {p0, v5}, Landroidx/media3/exoplayer/s1;->g1(I)V

    const/4 v4, 0x1

    const/4 v4, 0x0

    iput-object v4, v0, Landroidx/media3/exoplayer/s1;->Q:Landroidx/media3/exoplayer/ExoPlaybackException;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->j1()Z

    move-result v4

    if-eqz v4, :cond_14

    invoke-virtual {p0, v10, v10}, Landroidx/media3/exoplayer/s1;->x1(ZZ)V

    iget-object v4, v0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {v4}, Landroidx/media3/exoplayer/r;->f()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->m1()V

    goto :goto_9

    :cond_10
    iget-object v4, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget v4, v4, Landroidx/media3/exoplayer/s2;->e:I

    if-ne v4, v5, :cond_14

    iget v4, v0, Landroidx/media3/exoplayer/s1;->L:I

    if-nez v4, :cond_11

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->U()Z

    move-result v4

    if-eqz v4, :cond_12

    goto :goto_9

    :cond_11
    if-nez v9, :cond_14

    :cond_12
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->j1()Z

    move-result v4

    invoke-virtual {p0, v4, v10}, Landroidx/media3/exoplayer/s1;->x1(ZZ)V

    const/4 v4, 0x2

    invoke-virtual {p0, v4}, Landroidx/media3/exoplayer/s1;->g1(I)V

    iget-boolean v4, v0, Landroidx/media3/exoplayer/s1;->D:Z

    if-eqz v4, :cond_13

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->k0()V

    iget-object v4, v0, Landroidx/media3/exoplayer/s1;->v:Landroidx/media3/exoplayer/u1;

    invoke-interface {v4}, Landroidx/media3/exoplayer/u1;->c()V

    :cond_13
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->p1()V

    :cond_14
    :goto_9
    iget-object v4, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget v4, v4, Landroidx/media3/exoplayer/s2;->e:I

    const/4 v7, 0x2

    if-ne v4, v7, :cond_19

    const/4 v4, 0x1

    const/4 v4, 0x0

    :goto_a
    iget-object v7, v0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v8, v7

    if-ge v4, v8, :cond_16

    aget-object v7, v7, v4

    invoke-static {v7}, Landroidx/media3/exoplayer/s1;->T(Landroidx/media3/exoplayer/w2;)Z

    move-result v7

    if-eqz v7, :cond_15

    iget-object v7, v0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    aget-object v7, v7, v4

    invoke-interface {v7}, Landroidx/media3/exoplayer/w2;->getStream()Lu2/e0;

    move-result-object v7

    iget-object v8, v3, Landroidx/media3/exoplayer/z1;->c:[Lu2/e0;

    aget-object v8, v8, v4

    if-ne v7, v8, :cond_15

    iget-object v7, v0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    aget-object v7, v7, v4

    invoke-interface {v7}, Landroidx/media3/exoplayer/w2;->maybeThrowStreamError()V

    :cond_15
    add-int/lit8 v4, v4, 0x1

    goto :goto_a

    :cond_16
    iget-object v3, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-boolean v4, v3, Landroidx/media3/exoplayer/s2;->g:Z

    if-nez v4, :cond_19

    iget-wide v3, v3, Landroidx/media3/exoplayer/s2;->q:J

    const-wide/32 v7, 0x7a120

    cmp-long v9, v3, v7

    if-gez v9, :cond_19

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->S()Z

    move-result v3

    if-eqz v3, :cond_19

    iget-wide v3, v0, Landroidx/media3/exoplayer/s1;->S:J

    cmp-long v7, v3, v11

    if-nez v7, :cond_17

    iget-object v3, v0, Landroidx/media3/exoplayer/s1;->r:Le2/d;

    invoke-interface {v3}, Le2/d;->elapsedRealtime()J

    move-result-wide v3

    iput-wide v3, v0, Landroidx/media3/exoplayer/s1;->S:J

    goto :goto_b

    :cond_17
    iget-object v3, v0, Landroidx/media3/exoplayer/s1;->r:Le2/d;

    invoke-interface {v3}, Le2/d;->elapsedRealtime()J

    move-result-wide v3

    iget-wide v7, v0, Landroidx/media3/exoplayer/s1;->S:J

    sub-long/2addr v3, v7

    const-wide/16 v7, 0xfa0

    cmp-long v9, v3, v7

    if-gez v9, :cond_18

    goto :goto_b

    :cond_18
    new-instance v1, Ljava/lang/IllegalStateException;

    const-string v2, "Playback stuck buffering and not loading"

    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v1

    :cond_19
    iput-wide v11, v0, Landroidx/media3/exoplayer/s1;->S:J

    :goto_b
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->j1()Z

    move-result v3

    if-eqz v3, :cond_1a

    iget-object v3, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget v3, v3, Landroidx/media3/exoplayer/s2;->e:I

    if-ne v3, v5, :cond_1a

    const/4 v3, 0x1

    goto :goto_c

    :cond_1a
    const/4 v3, 0x1

    const/4 v3, 0x0

    :goto_c
    iget-boolean v4, v0, Landroidx/media3/exoplayer/s1;->K:Z

    if-eqz v4, :cond_1b

    iget-boolean v4, v0, Landroidx/media3/exoplayer/s1;->J:Z

    if-eqz v4, :cond_1b

    if-eqz v3, :cond_1b

    const/4 v4, 0x1

    goto :goto_d

    :cond_1b
    const/4 v4, 0x1

    const/4 v4, 0x0

    :goto_d
    iget-object v7, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-boolean v8, v7, Landroidx/media3/exoplayer/s2;->o:Z

    if-eq v8, v4, :cond_1c

    invoke-virtual {v7, v4}, Landroidx/media3/exoplayer/s2;->i(Z)Landroidx/media3/exoplayer/s2;

    move-result-object v7

    iput-object v7, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    :cond_1c
    iput-boolean v10, v0, Landroidx/media3/exoplayer/s1;->J:Z

    if-nez v4, :cond_20

    iget-object v4, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget v4, v4, Landroidx/media3/exoplayer/s2;->e:I

    if-ne v4, v6, :cond_1d

    goto :goto_f

    :cond_1d
    if-nez v3, :cond_1e

    const/4 v3, 0x2

    if-ne v4, v3, :cond_1f

    :cond_1e
    const-wide/16 v3, 0xa

    goto :goto_e

    :cond_1f
    if-ne v4, v5, :cond_20

    iget v3, v0, Landroidx/media3/exoplayer/s1;->L:I

    if-eqz v3, :cond_20

    const-wide/16 v3, 0x3e8

    invoke-virtual {p0, v1, v2, v3, v4}, Landroidx/media3/exoplayer/s1;->F0(JJ)V

    goto :goto_f

    :goto_e
    invoke-virtual {p0, v1, v2, v3, v4}, Landroidx/media3/exoplayer/s1;->F0(JJ)V

    :cond_20
    :goto_f
    invoke-static {}, Le2/j0;->c()V

    :cond_21
    :goto_10
    return-void
.end method

.method public s0(IILu2/f0;)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/16 v1, 0x14

    invoke-interface {v0, v1, p1, p2, p3}, Le2/j;->obtainMessage(IIILjava/lang/Object;)Le2/j$a;

    move-result-object p1

    invoke-interface {p1}, Le2/j$a;->a()V

    return-void
.end method

.method public s1(IILjava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/List<",
            "Landroidx/media3/common/b0;",
            ">;)V"
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/16 v1, 0x1b

    invoke-interface {v0, v1, p1, p2, p3}, Le2/j;->obtainMessage(IIILjava/lang/Object;)Le2/j$a;

    move-result-object p1

    invoke-interface {p1}, Le2/j$a;->a()V

    return-void
.end method

.method public final t(IZJ)V
    .locals 17
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    move-object/from16 v0, p0

    iget-object v1, v0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    aget-object v1, v1, p1

    invoke-static {v1}, Landroidx/media3/exoplayer/s1;->T(Landroidx/media3/exoplayer/w2;)Z

    move-result v2

    if-eqz v2, :cond_0

    return-void

    :cond_0
    iget-object v2, v0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v2}, Landroidx/media3/exoplayer/c2;->s()Landroidx/media3/exoplayer/z1;

    move-result-object v2

    iget-object v3, v0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v3}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v3

    const/4 v4, 0x1

    const/4 v4, 0x0

    const/4 v5, 0x1

    if-ne v2, v3, :cond_1

    const/4 v15, 0x1

    goto :goto_0

    :cond_1
    const/4 v15, 0x1

    const/4 v15, 0x0

    :goto_0
    invoke-virtual {v2}, Landroidx/media3/exoplayer/z1;->o()Lx2/f0;

    move-result-object v3

    iget-object v6, v3, Lx2/f0;->b:[Landroidx/media3/exoplayer/z2;

    aget-object v6, v6, p1

    iget-object v3, v3, Lx2/f0;->c:[Lx2/z;

    aget-object v3, v3, p1

    invoke-static {v3}, Landroidx/media3/exoplayer/s1;->A(Lx2/z;)[Landroidx/media3/common/y;

    move-result-object v7

    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/s1;->j1()Z

    move-result v3

    if-eqz v3, :cond_2

    iget-object v3, v0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget v3, v3, Landroidx/media3/exoplayer/s2;->e:I

    const/4 v8, 0x3

    if-ne v3, v8, :cond_2

    const/16 v16, 0x1

    goto :goto_1

    :cond_2
    const/16 v16, 0x0

    :goto_1
    if-nez p2, :cond_3

    if-eqz v16, :cond_3

    const/4 v8, 0x1

    goto :goto_2

    :cond_3
    const/4 v8, 0x1

    const/4 v8, 0x0

    :goto_2
    iget v3, v0, Landroidx/media3/exoplayer/s1;->L:I

    add-int/2addr v3, v5

    iput v3, v0, Landroidx/media3/exoplayer/s1;->L:I

    iget-object v3, v0, Landroidx/media3/exoplayer/s1;->b:Ljava/util/Set;

    invoke-interface {v3, v1}, Ljava/util/Set;->add(Ljava/lang/Object;)Z

    iget-object v3, v2, Landroidx/media3/exoplayer/z1;->c:[Lu2/e0;

    aget-object v5, v3, p1

    iget-wide v9, v0, Landroidx/media3/exoplayer/s1;->N:J

    invoke-virtual {v2}, Landroidx/media3/exoplayer/z1;->l()J

    move-result-wide v12

    iget-object v2, v2, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v14, v2, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    move-object v2, v1

    move-object v3, v6

    move-object v4, v7

    move-wide v6, v9

    move v9, v15

    move-wide/from16 v10, p3

    invoke-interface/range {v2 .. v14}, Landroidx/media3/exoplayer/w2;->j(Landroidx/media3/exoplayer/z2;[Landroidx/media3/common/y;Lu2/e0;JZZJJLandroidx/media3/exoplayer/source/l$b;)V

    new-instance v2, Landroidx/media3/exoplayer/s1$a;

    invoke-direct {v2, v0}, Landroidx/media3/exoplayer/s1$a;-><init>(Landroidx/media3/exoplayer/s1;)V

    const/16 v3, 0xb

    invoke-interface {v1, v3, v2}, Landroidx/media3/exoplayer/t2$b;->handleMessage(ILjava/lang/Object;)V

    iget-object v2, v0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {v2, v1}, Landroidx/media3/exoplayer/r;->c(Landroidx/media3/exoplayer/w2;)V

    if-eqz v16, :cond_4

    if-eqz v15, :cond_4

    invoke-interface {v1}, Landroidx/media3/exoplayer/w2;->start()V

    :cond_4
    return-void
.end method

.method public final t0()Z
    .locals 17
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    move-object/from16 v0, p0

    iget-object v1, v0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/c2;->s()Landroidx/media3/exoplayer/z1;

    move-result-object v1

    invoke-virtual {v1}, Landroidx/media3/exoplayer/z1;->o()Lx2/f0;

    move-result-object v2

    const/4 v3, 0x1

    const/4 v3, 0x0

    const/4 v4, 0x1

    const/4 v4, 0x0

    const/4 v5, 0x1

    const/4 v5, 0x0

    :goto_0
    iget-object v6, v0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v7, v6

    const/4 v8, 0x1

    if-ge v4, v7, :cond_6

    aget-object v9, v6, v4

    invoke-static {v9}, Landroidx/media3/exoplayer/s1;->T(Landroidx/media3/exoplayer/w2;)Z

    move-result v6

    if-nez v6, :cond_0

    goto :goto_2

    :cond_0
    invoke-interface {v9}, Landroidx/media3/exoplayer/w2;->getStream()Lu2/e0;

    move-result-object v6

    iget-object v7, v1, Landroidx/media3/exoplayer/z1;->c:[Lu2/e0;

    aget-object v7, v7, v4

    if-eq v6, v7, :cond_1

    const/4 v6, 0x1

    goto :goto_1

    :cond_1
    const/4 v6, 0x1

    const/4 v6, 0x0

    :goto_1
    invoke-virtual {v2, v4}, Lx2/f0;->c(I)Z

    move-result v7

    if-eqz v7, :cond_2

    if-nez v6, :cond_2

    goto :goto_2

    :cond_2
    invoke-interface {v9}, Landroidx/media3/exoplayer/w2;->isCurrentStreamFinal()Z

    move-result v6

    if-nez v6, :cond_3

    iget-object v6, v2, Lx2/f0;->c:[Lx2/z;

    aget-object v6, v6, v4

    invoke-static {v6}, Landroidx/media3/exoplayer/s1;->A(Lx2/z;)[Landroidx/media3/common/y;

    move-result-object v10

    iget-object v6, v1, Landroidx/media3/exoplayer/z1;->c:[Lu2/e0;

    aget-object v11, v6, v4

    invoke-virtual {v1}, Landroidx/media3/exoplayer/z1;->m()J

    move-result-wide v12

    invoke-virtual {v1}, Landroidx/media3/exoplayer/z1;->l()J

    move-result-wide v14

    iget-object v6, v1, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-object v6, v6, Landroidx/media3/exoplayer/a2;->a:Landroidx/media3/exoplayer/source/l$b;

    move-object/from16 v16, v6

    invoke-interface/range {v9 .. v16}, Landroidx/media3/exoplayer/w2;->l([Landroidx/media3/common/y;Lu2/e0;JJLandroidx/media3/exoplayer/source/l$b;)V

    iget-boolean v6, v0, Landroidx/media3/exoplayer/s1;->K:Z

    if-eqz v6, :cond_5

    invoke-virtual {v0, v3}, Landroidx/media3/exoplayer/s1;->U0(Z)V

    goto :goto_2

    :cond_3
    invoke-interface {v9}, Landroidx/media3/exoplayer/w2;->isEnded()Z

    move-result v6

    if-eqz v6, :cond_4

    invoke-virtual {v0, v9}, Landroidx/media3/exoplayer/s1;->r(Landroidx/media3/exoplayer/w2;)V

    goto :goto_2

    :cond_4
    const/4 v5, 0x1

    :cond_5
    :goto_2
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    :cond_6
    xor-int/lit8 v1, v5, 0x1

    return v1
.end method

.method public final t1(IILjava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/List<",
            "Landroidx/media3/common/b0;",
            ">;)V"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->z:Landroidx/media3/exoplayer/s1$e;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/s1$e;->b(I)V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->u:Landroidx/media3/exoplayer/r2;

    invoke-virtual {v0, p1, p2, p3}, Landroidx/media3/exoplayer/r2;->F(IILjava/util/List;)Landroidx/media3/common/m0;

    move-result-object p1

    const/4 p2, 0x1

    const/4 p2, 0x0

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/s1;->K(Landroidx/media3/common/m0;Z)V

    return-void
.end method

.method public final u()V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v0, v0

    new-array v0, v0, [Z

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/c2;->s()Landroidx/media3/exoplayer/z1;

    move-result-object v1

    invoke-virtual {v1}, Landroidx/media3/exoplayer/z1;->m()J

    move-result-wide v1

    invoke-virtual {p0, v0, v1, v2}, Landroidx/media3/exoplayer/s1;->v([ZJ)V

    return-void
.end method

.method public final u0()V
    .locals 18
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    move-object/from16 v10, p0

    iget-object v0, v10, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/r;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object v0

    iget v0, v0, Landroidx/media3/common/g0;->a:F

    iget-object v1, v10, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v1

    iget-object v2, v10, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v2}, Landroidx/media3/exoplayer/c2;->s()Landroidx/media3/exoplayer/z1;

    move-result-object v2

    const/4 v3, 0x1

    const/4 v3, 0x0

    const/4 v4, 0x1

    :goto_0
    if-eqz v1, :cond_c

    iget-boolean v5, v1, Landroidx/media3/exoplayer/z1;->d:Z

    if-nez v5, :cond_0

    goto/16 :goto_6

    :cond_0
    iget-object v5, v10, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v5, v5, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v1, v0, v5}, Landroidx/media3/exoplayer/z1;->v(FLandroidx/media3/common/m0;)Lx2/f0;

    move-result-object v5

    iget-object v6, v10, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v6}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v6

    if-ne v1, v6, :cond_1

    move-object v3, v5

    :cond_1
    invoke-virtual {v1}, Landroidx/media3/exoplayer/z1;->o()Lx2/f0;

    move-result-object v6

    invoke-virtual {v5, v6}, Lx2/f0;->a(Lx2/f0;)Z

    move-result v6

    if-nez v6, :cond_a

    const/4 v13, 0x4

    if-eqz v4, :cond_8

    iget-object v0, v10, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v14

    iget-object v0, v10, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0, v14}, Landroidx/media3/exoplayer/c2;->D(Landroidx/media3/exoplayer/z1;)Z

    move-result v8

    iget-object v0, v10, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v0, v0

    new-array v15, v0, [Z

    invoke-static {v3}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    move-object v5, v0

    check-cast v5, Lx2/f0;

    iget-object v0, v10, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v6, v0, Landroidx/media3/exoplayer/s2;->r:J

    move-object v4, v14

    move-object v9, v15

    invoke-virtual/range {v4 .. v9}, Landroidx/media3/exoplayer/z1;->b(Lx2/f0;JZ[Z)J

    move-result-wide v8

    iget-object v0, v10, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget v1, v0, Landroidx/media3/exoplayer/s2;->e:I

    if-eq v1, v13, :cond_2

    iget-wide v0, v0, Landroidx/media3/exoplayer/s2;->r:J

    cmp-long v2, v8, v0

    if-eqz v2, :cond_2

    const/16 v16, 0x1

    goto :goto_1

    :cond_2
    const/16 v16, 0x0

    :goto_1
    iget-object v0, v10, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v4, v0, Landroidx/media3/exoplayer/s2;->c:J

    iget-wide v6, v0, Landroidx/media3/exoplayer/s2;->d:J

    const/16 v17, 0x5

    move-object/from16 v0, p0

    move-wide v2, v8

    move-wide v11, v8

    move/from16 v8, v16

    move/from16 v9, v17

    invoke-virtual/range {v0 .. v9}, Landroidx/media3/exoplayer/s1;->O(Landroidx/media3/exoplayer/source/l$b;JJJZI)Landroidx/media3/exoplayer/s2;

    move-result-object v0

    iput-object v0, v10, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    if-eqz v16, :cond_3

    invoke-virtual {v10, v11, v12}, Landroidx/media3/exoplayer/s1;->y0(J)V

    :cond_3
    iget-object v0, v10, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v0, v0

    new-array v0, v0, [Z

    const/4 v12, 0x1

    const/4 v12, 0x0

    :goto_2
    iget-object v1, v10, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v2, v1

    if-ge v12, v2, :cond_6

    aget-object v1, v1, v12

    invoke-static {v1}, Landroidx/media3/exoplayer/s1;->T(Landroidx/media3/exoplayer/w2;)Z

    move-result v2

    aput-boolean v2, v0, v12

    iget-object v3, v14, Landroidx/media3/exoplayer/z1;->c:[Lu2/e0;

    aget-object v3, v3, v12

    if-eqz v2, :cond_5

    invoke-interface {v1}, Landroidx/media3/exoplayer/w2;->getStream()Lu2/e0;

    move-result-object v2

    if-eq v3, v2, :cond_4

    invoke-virtual {v10, v1}, Landroidx/media3/exoplayer/s1;->r(Landroidx/media3/exoplayer/w2;)V

    goto :goto_3

    :cond_4
    aget-boolean v2, v15, v12

    if-eqz v2, :cond_5

    iget-wide v2, v10, Landroidx/media3/exoplayer/s1;->N:J

    invoke-interface {v1, v2, v3}, Landroidx/media3/exoplayer/w2;->resetPosition(J)V

    :cond_5
    :goto_3
    add-int/lit8 v12, v12, 0x1

    goto :goto_2

    :cond_6
    iget-wide v1, v10, Landroidx/media3/exoplayer/s1;->N:J

    invoke-virtual {v10, v0, v1, v2}, Landroidx/media3/exoplayer/s1;->v([ZJ)V

    :cond_7
    :goto_4
    const/4 v5, 0x1

    goto :goto_5

    :cond_8
    iget-object v0, v10, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/c2;->D(Landroidx/media3/exoplayer/z1;)Z

    iget-boolean v0, v1, Landroidx/media3/exoplayer/z1;->d:Z

    if-eqz v0, :cond_7

    iget-object v0, v1, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-wide v2, v0, Landroidx/media3/exoplayer/a2;->b:J

    iget-wide v6, v10, Landroidx/media3/exoplayer/s1;->N:J

    invoke-virtual {v1, v6, v7}, Landroidx/media3/exoplayer/z1;->y(J)J

    move-result-wide v6

    invoke-static {v2, v3, v6, v7}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v2

    const/4 v6, 0x1

    const/4 v6, 0x0

    invoke-virtual {v1, v5, v2, v3, v6}, Landroidx/media3/exoplayer/z1;->a(Lx2/f0;JZ)J

    goto :goto_4

    :goto_5
    invoke-virtual {v10, v5}, Landroidx/media3/exoplayer/s1;->J(Z)V

    iget-object v0, v10, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget v0, v0, Landroidx/media3/exoplayer/s2;->e:I

    if-eq v0, v13, :cond_9

    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/s1;->Y()V

    invoke-virtual/range {p0 .. p0}, Landroidx/media3/exoplayer/s1;->v1()V

    iget-object v0, v10, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/4 v1, 0x2

    invoke-interface {v0, v1}, Le2/j;->sendEmptyMessage(I)Z

    :cond_9
    return-void

    :cond_a
    const/4 v5, 0x1

    const/4 v6, 0x1

    const/4 v6, 0x0

    if-ne v1, v2, :cond_b

    const/4 v4, 0x1

    const/4 v4, 0x0

    :cond_b
    invoke-virtual {v1}, Landroidx/media3/exoplayer/z1;->j()Landroidx/media3/exoplayer/z1;

    move-result-object v1

    goto/16 :goto_0

    :cond_c
    :goto_6
    return-void
.end method

.method public final u1()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v0}, Landroidx/media3/common/m0;->q()Z

    move-result v0

    if-nez v0, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->u:Landroidx/media3/exoplayer/r2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/r2;->t()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->b0()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->e0()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->f0()V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->d0()V

    :cond_1
    :goto_0
    return-void
.end method

.method public final v([ZJ)V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->s()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->o()Lx2/f0;

    move-result-object v1

    const/4 v2, 0x1

    const/4 v2, 0x0

    const/4 v3, 0x1

    const/4 v3, 0x0

    :goto_0
    iget-object v4, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v4, v4

    if-ge v3, v4, :cond_1

    invoke-virtual {v1, v3}, Lx2/f0;->c(I)Z

    move-result v4

    if-nez v4, :cond_0

    iget-object v4, p0, Landroidx/media3/exoplayer/s1;->b:Ljava/util/Set;

    iget-object v5, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    aget-object v5, v5, v3

    invoke-interface {v4, v5}, Ljava/util/Set;->remove(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_0

    iget-object v4, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    aget-object v4, v4, v3

    invoke-interface {v4}, Landroidx/media3/exoplayer/w2;->reset()V

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_1
    :goto_1
    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v3, v3

    if-ge v2, v3, :cond_3

    invoke-virtual {v1, v2}, Lx2/f0;->c(I)Z

    move-result v3

    if-eqz v3, :cond_2

    aget-boolean v3, p1, v2

    invoke-virtual {p0, v2, v3, p2, p3}, Landroidx/media3/exoplayer/s1;->t(IZJ)V

    :cond_2
    add-int/lit8 v2, v2, 0x1

    goto :goto_1

    :cond_3
    const/4 p1, 0x1

    iput-boolean p1, v0, Landroidx/media3/exoplayer/z1;->g:Z

    return-void
.end method

.method public final v0()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->u0()V

    const/4 v0, 0x1

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->H0(Z)V

    return-void
.end method

.method public final v1()V
    .locals 11
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    iget-boolean v1, v0, Landroidx/media3/exoplayer/z1;->d:Z

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    if-eqz v1, :cond_1

    iget-object v1, v0, Landroidx/media3/exoplayer/z1;->a:Landroidx/media3/exoplayer/source/k;

    invoke-interface {v1}, Landroidx/media3/exoplayer/source/k;->readDiscontinuity()J

    move-result-wide v4

    move-wide v6, v4

    goto :goto_0

    :cond_1
    move-wide v6, v2

    :goto_0
    const/4 v10, 0x1

    const/4 v10, 0x0

    cmp-long v1, v6, v2

    if-eqz v1, :cond_3

    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->q()Z

    move-result v1

    if-nez v1, :cond_2

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v1, v0}, Landroidx/media3/exoplayer/c2;->D(Landroidx/media3/exoplayer/z1;)Z

    invoke-virtual {p0, v10}, Landroidx/media3/exoplayer/s1;->J(Z)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->Y()V

    :cond_2
    invoke-virtual {p0, v6, v7}, Landroidx/media3/exoplayer/s1;->y0(J)V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v0, v0, Landroidx/media3/exoplayer/s2;->r:J

    cmp-long v2, v6, v0

    if-eqz v2, :cond_6

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v4, v0, Landroidx/media3/exoplayer/s2;->c:J

    const/4 v8, 0x1

    const/4 v9, 0x5

    move-object v0, p0

    move-wide v2, v6

    invoke-virtual/range {v0 .. v9}, Landroidx/media3/exoplayer/s1;->O(Landroidx/media3/exoplayer/source/l$b;JJJZI)Landroidx/media3/exoplayer/s2;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    goto :goto_2

    :cond_3
    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    iget-object v2, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v2}, Landroidx/media3/exoplayer/c2;->s()Landroidx/media3/exoplayer/z1;

    move-result-object v2

    if-eq v0, v2, :cond_4

    const/4 v2, 0x1

    goto :goto_1

    :cond_4
    const/4 v2, 0x1

    const/4 v2, 0x0

    :goto_1
    invoke-virtual {v1, v2}, Landroidx/media3/exoplayer/r;->i(Z)J

    move-result-wide v1

    iput-wide v1, p0, Landroidx/media3/exoplayer/s1;->N:J

    invoke-virtual {v0, v1, v2}, Landroidx/media3/exoplayer/z1;->y(J)J

    move-result-wide v6

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v0, v0, Landroidx/media3/exoplayer/s2;->r:J

    invoke-virtual {p0, v0, v1, v6, v7}, Landroidx/media3/exoplayer/s1;->a0(JJ)V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/r;->g()Z

    move-result v0

    if-eqz v0, :cond_5

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v4, v0, Landroidx/media3/exoplayer/s2;->c:J

    const/4 v8, 0x1

    const/4 v9, 0x6

    move-object v0, p0

    move-wide v2, v6

    invoke-virtual/range {v0 .. v9}, Landroidx/media3/exoplayer/s1;->O(Landroidx/media3/exoplayer/source/l$b;JJJZI)Landroidx/media3/exoplayer/s2;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    goto :goto_2

    :cond_5
    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual {v0, v6, v7}, Landroidx/media3/exoplayer/s2;->o(J)V

    :cond_6
    :goto_2
    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->l()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->i()J

    move-result-wide v2

    iput-wide v2, v1, Landroidx/media3/exoplayer/s2;->p:J

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->F()J

    move-result-wide v1

    iput-wide v1, v0, Landroidx/media3/exoplayer/s2;->q:J

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-boolean v1, v0, Landroidx/media3/exoplayer/s2;->l:Z

    if-eqz v1, :cond_7

    iget v1, v0, Landroidx/media3/exoplayer/s2;->e:I

    const/4 v2, 0x3

    if-ne v1, v2, :cond_7

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {p0, v1, v0}, Landroidx/media3/exoplayer/s1;->l1(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;)Z

    move-result v0

    if-eqz v0, :cond_7

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    iget v0, v0, Landroidx/media3/common/g0;->a:F

    const/high16 v1, 0x3f800000    # 1.0f

    cmpl-float v0, v0, v1

    if-nez v0, :cond_7

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->v:Landroidx/media3/exoplayer/u1;

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->z()J

    move-result-wide v1

    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->F()J

    move-result-wide v3

    invoke-interface {v0, v1, v2, v3, v4}, Landroidx/media3/exoplayer/u1;->a(JJ)F

    move-result v0

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/r;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object v1

    iget v1, v1, Landroidx/media3/common/g0;->a:F

    cmpl-float v1, v1, v0

    if-eqz v1, :cond_7

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v1, v1, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    invoke-virtual {v1, v0}, Landroidx/media3/common/g0;->b(F)Landroidx/media3/common/g0;

    move-result-object v0

    invoke-virtual {p0, v0}, Landroidx/media3/exoplayer/s1;->R0(Landroidx/media3/common/g0;)V

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/r;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object v1

    iget v1, v1, Landroidx/media3/common/g0;->a:F

    invoke-virtual {p0, v0, v1, v10, v10}, Landroidx/media3/exoplayer/s1;->M(Landroidx/media3/common/g0;FZZ)V

    :cond_7
    return-void
.end method

.method public final w(Landroidx/media3/exoplayer/w2;)V
    .locals 2

    invoke-interface {p1}, Landroidx/media3/exoplayer/w2;->getState()I

    move-result v0

    const/4 v1, 0x2

    if-ne v0, v1, :cond_0

    invoke-interface {p1}, Landroidx/media3/exoplayer/w2;->stop()V

    :cond_0
    return-void
.end method

.method public final w0(ZZZZ)V
    .locals 32

    move-object/from16 v1, p0

    iget-object v0, v1, Landroidx/media3/exoplayer/s1;->i:Le2/j;

    const/4 v2, 0x2

    invoke-interface {v0, v2}, Le2/j;->removeMessages(I)V

    const/4 v2, 0x1

    const/4 v2, 0x0

    iput-object v2, v1, Landroidx/media3/exoplayer/s1;->Q:Landroidx/media3/exoplayer/ExoPlaybackException;

    const/4 v3, 0x1

    const/4 v3, 0x0

    const/4 v4, 0x1

    invoke-virtual {v1, v3, v4}, Landroidx/media3/exoplayer/s1;->x1(ZZ)V

    iget-object v0, v1, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/r;->h()V

    const-wide v5, 0xe8d4a51000L

    iput-wide v5, v1, Landroidx/media3/exoplayer/s1;->N:J

    iget-object v5, v1, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v6, v5

    const/4 v7, 0x1

    const/4 v7, 0x0

    :goto_0
    const-string v8, "ExoPlayerImplInternal"

    if-ge v7, v6, :cond_0

    aget-object v0, v5, v7

    :try_start_0
    invoke-virtual {v1, v0}, Landroidx/media3/exoplayer/s1;->r(Landroidx/media3/exoplayer/w2;)V
    :try_end_0
    .catch Landroidx/media3/exoplayer/ExoPlaybackException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/RuntimeException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_2

    :catch_0
    move-exception v0

    goto :goto_1

    :catch_1
    move-exception v0

    :goto_1
    const-string v9, "Disable failed."

    invoke-static {v8, v9, v0}, Le2/o;->d(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    :goto_2
    add-int/lit8 v7, v7, 0x1

    goto :goto_0

    :cond_0
    if-eqz p1, :cond_2

    iget-object v5, v1, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length v6, v5

    const/4 v7, 0x1

    const/4 v7, 0x0

    :goto_3
    if-ge v7, v6, :cond_2

    aget-object v0, v5, v7

    iget-object v9, v1, Landroidx/media3/exoplayer/s1;->b:Ljava/util/Set;

    invoke-interface {v9, v0}, Ljava/util/Set;->remove(Ljava/lang/Object;)Z

    move-result v9

    if-eqz v9, :cond_1

    :try_start_1
    invoke-interface {v0}, Landroidx/media3/exoplayer/w2;->reset()V
    :try_end_1
    .catch Ljava/lang/RuntimeException; {:try_start_1 .. :try_end_1} :catch_2

    goto :goto_4

    :catch_2
    move-exception v0

    move-object v9, v0

    const-string v0, "Reset failed."

    invoke-static {v8, v0, v9}, Le2/o;->d(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    :cond_1
    :goto_4
    add-int/lit8 v7, v7, 0x1

    goto :goto_3

    :cond_2
    iput v3, v1, Landroidx/media3/exoplayer/s1;->L:I

    iget-object v0, v1, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v5, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-wide v6, v0, Landroidx/media3/exoplayer/s2;->r:J

    iget-object v0, v1, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result v0

    if-nez v0, :cond_4

    iget-object v0, v1, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v8, v1, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    invoke-static {v0, v8}, Landroidx/media3/exoplayer/s1;->V(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/m0$b;)Z

    move-result v0

    if-eqz v0, :cond_3

    goto :goto_5

    :cond_3
    iget-object v0, v1, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v8, v0, Landroidx/media3/exoplayer/s2;->r:J

    goto :goto_6

    :cond_4
    :goto_5
    iget-object v0, v1, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-wide v8, v0, Landroidx/media3/exoplayer/s2;->c:J

    :goto_6
    if-eqz p2, :cond_5

    iput-object v2, v1, Landroidx/media3/exoplayer/s1;->M:Landroidx/media3/exoplayer/s1$h;

    iget-object v0, v1, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    invoke-virtual {v1, v0}, Landroidx/media3/exoplayer/s1;->D(Landroidx/media3/common/m0;)Landroid/util/Pair;

    move-result-object v0

    iget-object v5, v0, Landroid/util/Pair;->first:Ljava/lang/Object;

    check-cast v5, Landroidx/media3/exoplayer/source/l$b;

    iget-object v0, v0, Landroid/util/Pair;->second:Ljava/lang/Object;

    check-cast v0, Ljava/lang/Long;

    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    move-result-wide v6

    iget-object v0, v1, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    invoke-virtual {v5, v0}, Landroidx/media3/exoplayer/source/l$b;->equals(Ljava/lang/Object;)Z

    move-result v0

    const-wide v8, -0x7fffffffffffffffL    # -4.9E-324

    if-nez v0, :cond_5

    move-wide/from16 v27, v6

    move-wide v9, v8

    goto :goto_7

    :cond_5
    move-wide/from16 v27, v6

    move-wide v9, v8

    const/4 v4, 0x1

    const/4 v4, 0x0

    :goto_7
    iget-object v0, v1, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->f()V

    iput-boolean v3, v1, Landroidx/media3/exoplayer/s1;->F:Z

    iget-object v0, v1, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v0, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    if-eqz p3, :cond_6

    instance-of v3, v0, Landroidx/media3/exoplayer/u2;

    if-eqz v3, :cond_6

    check-cast v0, Landroidx/media3/exoplayer/u2;

    iget-object v3, v1, Landroidx/media3/exoplayer/s1;->u:Landroidx/media3/exoplayer/r2;

    invoke-virtual {v3}, Landroidx/media3/exoplayer/r2;->q()Lu2/f0;

    move-result-object v3

    invoke-virtual {v0, v3}, Landroidx/media3/exoplayer/u2;->E(Lu2/f0;)Landroidx/media3/exoplayer/u2;

    move-result-object v0

    iget v3, v5, Landroidx/media3/exoplayer/source/l$b;->b:I

    const/4 v6, -0x1

    if-eq v3, v6, :cond_6

    iget-object v3, v5, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v6, v1, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    invoke-virtual {v0, v3, v6}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    iget-object v3, v1, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    iget v3, v3, Landroidx/media3/common/m0$b;->c:I

    iget-object v6, v1, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    invoke-virtual {v0, v3, v6}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object v3

    invoke-virtual {v3}, Landroidx/media3/common/m0$c;->f()Z

    move-result v3

    if-eqz v3, :cond_6

    new-instance v3, Landroidx/media3/exoplayer/source/l$b;

    iget-object v6, v5, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-wide v7, v5, Landroidx/media3/exoplayer/source/l$b;->d:J

    invoke-direct {v3, v6, v7, v8}, Landroidx/media3/exoplayer/source/l$b;-><init>(Ljava/lang/Object;J)V

    move-object v7, v0

    move-object/from16 v19, v3

    goto :goto_8

    :cond_6
    move-object v7, v0

    move-object/from16 v19, v5

    :goto_8
    new-instance v0, Landroidx/media3/exoplayer/s2;

    iget-object v3, v1, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget v13, v3, Landroidx/media3/exoplayer/s2;->e:I

    if-eqz p4, :cond_7

    :goto_9
    move-object v14, v2

    goto :goto_a

    :cond_7
    iget-object v2, v3, Landroidx/media3/exoplayer/s2;->f:Landroidx/media3/exoplayer/ExoPlaybackException;

    goto :goto_9

    :goto_a
    const/4 v15, 0x1

    const/4 v15, 0x0

    if-eqz v4, :cond_8

    sget-object v2, Lu2/k0;->d:Lu2/k0;

    :goto_b
    move-object/from16 v16, v2

    goto :goto_c

    :cond_8
    iget-object v2, v3, Landroidx/media3/exoplayer/s2;->h:Lu2/k0;

    goto :goto_b

    :goto_c
    if-eqz v4, :cond_9

    iget-object v2, v1, Landroidx/media3/exoplayer/s1;->f:Lx2/f0;

    :goto_d
    move-object/from16 v17, v2

    goto :goto_e

    :cond_9
    iget-object v2, v3, Landroidx/media3/exoplayer/s2;->i:Lx2/f0;

    goto :goto_d

    :goto_e
    if-eqz v4, :cond_a

    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object v2

    :goto_f
    move-object/from16 v18, v2

    goto :goto_10

    :cond_a
    iget-object v2, v3, Landroidx/media3/exoplayer/s2;->j:Ljava/util/List;

    goto :goto_f

    :goto_10
    iget-object v2, v1, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-boolean v3, v2, Landroidx/media3/exoplayer/s2;->l:Z

    move/from16 v20, v3

    iget v3, v2, Landroidx/media3/exoplayer/s2;->m:I

    move/from16 v21, v3

    iget-object v2, v2, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    move-object/from16 v22, v2

    const-wide/16 v25, 0x0

    const-wide/16 v29, 0x0

    const/16 v31, 0x0

    move-object v6, v0

    move-object/from16 v8, v19

    move-wide/from16 v11, v27

    move-wide/from16 v23, v27

    invoke-direct/range {v6 .. v31}, Landroidx/media3/exoplayer/s2;-><init>(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JJILandroidx/media3/exoplayer/ExoPlaybackException;ZLu2/k0;Lx2/f0;Ljava/util/List;Landroidx/media3/exoplayer/source/l$b;ZILandroidx/media3/common/g0;JJJJZ)V

    iput-object v0, v1, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    if-eqz p3, :cond_b

    iget-object v0, v1, Landroidx/media3/exoplayer/s1;->u:Landroidx/media3/exoplayer/r2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/r2;->z()V

    :cond_b
    return-void
.end method

.method public final w1(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;JZ)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/s1;->l1(Landroidx/media3/common/m0;Landroidx/media3/exoplayer/source/l$b;)Z

    move-result v0

    if-nez v0, :cond_2

    invoke-virtual {p2}, Landroidx/media3/exoplayer/source/l$b;->b()Z

    move-result p1

    if-eqz p1, :cond_0

    sget-object p1, Landroidx/media3/common/g0;->d:Landroidx/media3/common/g0;

    goto :goto_0

    :cond_0
    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object p1, p1, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    :goto_0
    iget-object p2, p0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {p2}, Landroidx/media3/exoplayer/r;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object p2

    invoke-virtual {p2, p1}, Landroidx/media3/common/g0;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-nez p2, :cond_1

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/s1;->R0(Landroidx/media3/common/g0;)V

    iget-object p2, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object p2, p2, Landroidx/media3/exoplayer/s2;->n:Landroidx/media3/common/g0;

    iget p1, p1, Landroidx/media3/common/g0;->a:F

    const/4 p3, 0x1

    const/4 p3, 0x0

    invoke-virtual {p0, p2, p1, p3, p3}, Landroidx/media3/exoplayer/s1;->M(Landroidx/media3/common/g0;FZZ)V

    :cond_1
    return-void

    :cond_2
    iget-object v0, p2, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    invoke-virtual {p1, v0, v1}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object v0

    iget v0, v0, Landroidx/media3/common/m0$b;->c:I

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    invoke-virtual {p1, v0, v1}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->v:Landroidx/media3/exoplayer/u1;

    iget-object v1, p0, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    iget-object v1, v1, Landroidx/media3/common/m0$c;->k:Landroidx/media3/common/b0$g;

    invoke-static {v1}, Le2/u0;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/common/b0$g;

    invoke-interface {v0, v1}, Landroidx/media3/exoplayer/u1;->e(Landroidx/media3/common/b0$g;)V

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v2, p5, v0

    if-eqz v2, :cond_3

    iget-object p3, p0, Landroidx/media3/exoplayer/s1;->v:Landroidx/media3/exoplayer/u1;

    iget-object p2, p2, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    invoke-virtual {p0, p1, p2, p5, p6}, Landroidx/media3/exoplayer/s1;->B(Landroidx/media3/common/m0;Ljava/lang/Object;J)J

    move-result-wide p1

    invoke-interface {p3, p1, p2}, Landroidx/media3/exoplayer/u1;->d(J)V

    goto :goto_2

    :cond_3
    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    iget-object p1, p1, Landroidx/media3/common/m0$c;->a:Ljava/lang/Object;

    invoke-virtual {p3}, Landroidx/media3/common/m0;->q()Z

    move-result p2

    if-nez p2, :cond_4

    iget-object p2, p4, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-object p4, p0, Landroidx/media3/exoplayer/s1;->m:Landroidx/media3/common/m0$b;

    invoke-virtual {p3, p2, p4}, Landroidx/media3/common/m0;->h(Ljava/lang/Object;Landroidx/media3/common/m0$b;)Landroidx/media3/common/m0$b;

    move-result-object p2

    iget p2, p2, Landroidx/media3/common/m0$b;->c:I

    iget-object p4, p0, Landroidx/media3/exoplayer/s1;->l:Landroidx/media3/common/m0$c;

    invoke-virtual {p3, p2, p4}, Landroidx/media3/common/m0;->n(ILandroidx/media3/common/m0$c;)Landroidx/media3/common/m0$c;

    move-result-object p2

    iget-object p2, p2, Landroidx/media3/common/m0$c;->a:Ljava/lang/Object;

    goto :goto_1

    :cond_4
    const/4 p2, 0x1

    const/4 p2, 0x0

    :goto_1
    invoke-static {p2, p1}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_5

    if-eqz p7, :cond_6

    :cond_5
    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->v:Landroidx/media3/exoplayer/u1;

    invoke-interface {p1, v0, v1}, Landroidx/media3/exoplayer/u1;->d(J)V

    :cond_6
    :goto_2
    return-void
.end method

.method public x(J)V
    .locals 0

    iput-wide p1, p0, Landroidx/media3/exoplayer/s1;->R:J

    return-void
.end method

.method public final x0()V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, v0, Landroidx/media3/exoplayer/z1;->f:Landroidx/media3/exoplayer/a2;

    iget-boolean v0, v0, Landroidx/media3/exoplayer/a2;->h:Z

    if-eqz v0, :cond_0

    iget-boolean v0, p0, Landroidx/media3/exoplayer/s1;->B:Z

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    iput-boolean v0, p0, Landroidx/media3/exoplayer/s1;->C:Z

    return-void
.end method

.method public final x1(ZZ)V
    .locals 0

    iput-boolean p1, p0, Landroidx/media3/exoplayer/s1;->D:Z

    if-eqz p2, :cond_0

    const-wide p1, -0x7fffffffffffffffL    # -4.9E-324

    goto :goto_0

    :cond_0
    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->r:Le2/d;

    invoke-interface {p1}, Le2/d;->elapsedRealtime()J

    move-result-wide p1

    :goto_0
    iput-wide p1, p0, Landroidx/media3/exoplayer/s1;->E:J

    return-void
.end method

.method public final y([Lx2/z;)Lcom/google/common/collect/ImmutableList;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "([",
            "Lx2/z;",
            ")",
            "Lcom/google/common/collect/ImmutableList<",
            "Landroidx/media3/common/Metadata;",
            ">;"
        }
    .end annotation

    new-instance v0, Lcom/google/common/collect/ImmutableList$a;

    invoke-direct {v0}, Lcom/google/common/collect/ImmutableList$a;-><init>()V

    array-length v1, p1

    const/4 v2, 0x1

    const/4 v2, 0x0

    const/4 v3, 0x1

    const/4 v3, 0x0

    const/4 v4, 0x1

    const/4 v4, 0x0

    :goto_0
    if-ge v3, v1, :cond_2

    aget-object v5, p1, v3

    if-eqz v5, :cond_1

    invoke-interface {v5, v2}, Lx2/c0;->getFormat(I)Landroidx/media3/common/y;

    move-result-object v5

    iget-object v5, v5, Landroidx/media3/common/y;->k:Landroidx/media3/common/Metadata;

    if-nez v5, :cond_0

    new-instance v5, Landroidx/media3/common/Metadata;

    new-array v6, v2, [Landroidx/media3/common/Metadata$Entry;

    invoke-direct {v5, v6}, Landroidx/media3/common/Metadata;-><init>([Landroidx/media3/common/Metadata$Entry;)V

    invoke-virtual {v0, v5}, Lcom/google/common/collect/ImmutableList$a;->i(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList$a;

    goto :goto_1

    :cond_0
    invoke-virtual {v0, v5}, Lcom/google/common/collect/ImmutableList$a;->i(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList$a;

    const/4 v4, 0x1

    :cond_1
    :goto_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_2
    if-eqz v4, :cond_3

    invoke-virtual {v0}, Lcom/google/common/collect/ImmutableList$a;->m()Lcom/google/common/collect/ImmutableList;

    move-result-object p1

    goto :goto_2

    :cond_3
    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object p1

    :goto_2
    return-object p1
.end method

.method public final y0(J)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    if-nez v0, :cond_0

    const-wide v0, 0xe8d4a51000L

    add-long/2addr p1, v0

    goto :goto_0

    :cond_0
    invoke-virtual {v0, p1, p2}, Landroidx/media3/exoplayer/z1;->z(J)J

    move-result-wide p1

    :goto_0
    iput-wide p1, p0, Landroidx/media3/exoplayer/s1;->N:J

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->p:Landroidx/media3/exoplayer/r;

    invoke-virtual {v0, p1, p2}, Landroidx/media3/exoplayer/r;->d(J)V

    iget-object p1, p0, Landroidx/media3/exoplayer/s1;->a:[Landroidx/media3/exoplayer/w2;

    array-length p2, p1

    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_1
    if-ge v0, p2, :cond_2

    aget-object v1, p1, v0

    invoke-static {v1}, Landroidx/media3/exoplayer/s1;->T(Landroidx/media3/exoplayer/w2;)Z

    move-result v2

    if-eqz v2, :cond_1

    iget-wide v2, p0, Landroidx/media3/exoplayer/s1;->N:J

    invoke-interface {v1, v2, v3}, Landroidx/media3/exoplayer/w2;->resetPosition(J)V

    :cond_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    :cond_2
    invoke-virtual {p0}, Landroidx/media3/exoplayer/s1;->i0()V

    return-void
.end method

.method public final y1(F)V
    .locals 5

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->t:Landroidx/media3/exoplayer/c2;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c2;->r()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    :goto_0
    if-eqz v0, :cond_2

    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->o()Lx2/f0;

    move-result-object v1

    iget-object v1, v1, Lx2/f0;->c:[Lx2/z;

    array-length v2, v1

    const/4 v3, 0x1

    const/4 v3, 0x0

    :goto_1
    if-ge v3, v2, :cond_1

    aget-object v4, v1, v3

    if-eqz v4, :cond_0

    invoke-interface {v4, p1}, Lx2/z;->onPlaybackSpeed(F)V

    :cond_0
    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    :cond_1
    invoke-virtual {v0}, Landroidx/media3/exoplayer/z1;->j()Landroidx/media3/exoplayer/z1;

    move-result-object v0

    goto :goto_0

    :cond_2
    return-void
.end method

.method public final z()J
    .locals 5

    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->y:Landroidx/media3/exoplayer/s2;

    iget-object v1, v0, Landroidx/media3/exoplayer/s2;->a:Landroidx/media3/common/m0;

    iget-object v2, v0, Landroidx/media3/exoplayer/s2;->b:Landroidx/media3/exoplayer/source/l$b;

    iget-object v2, v2, Landroidx/media3/exoplayer/source/l$b;->a:Ljava/lang/Object;

    iget-wide v3, v0, Landroidx/media3/exoplayer/s2;->r:J

    invoke-virtual {p0, v1, v2, v3, v4}, Landroidx/media3/exoplayer/s1;->B(Landroidx/media3/common/m0;Ljava/lang/Object;J)J

    move-result-wide v0

    return-wide v0
.end method

.method public final declared-synchronized z1(Lcom/google/common/base/q;J)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/google/common/base/q<",
            "Ljava/lang/Boolean;",
            ">;J)V"
        }
    .end annotation

    monitor-enter p0

    :try_start_0
    iget-object v0, p0, Landroidx/media3/exoplayer/s1;->r:Le2/d;

    invoke-interface {v0}, Le2/d;->elapsedRealtime()J

    move-result-wide v0

    add-long/2addr v0, p2

    const/4 v2, 0x1

    const/4 v2, 0x0

    :goto_0
    invoke-interface {p1}, Lcom/google/common/base/q;->get()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Boolean;

    invoke-virtual {v3}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v3
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-nez v3, :cond_0

    const-wide/16 v3, 0x0

    cmp-long v5, p2, v3

    if-lez v5, :cond_0

    :try_start_1
    iget-object v3, p0, Landroidx/media3/exoplayer/s1;->r:Le2/d;

    invoke-interface {v3}, Le2/d;->a()V

    invoke-virtual {p0, p2, p3}, Ljava/lang/Object;->wait(J)V
    :try_end_1
    .catch Ljava/lang/InterruptedException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_1

    :catchall_0
    move-exception p1

    goto :goto_2

    :catch_0
    const/4 p2, 0x1

    const/4 v2, 0x1

    :goto_1
    :try_start_2
    iget-object p2, p0, Landroidx/media3/exoplayer/s1;->r:Le2/d;

    invoke-interface {p2}, Le2/d;->elapsedRealtime()J

    move-result-wide p2

    sub-long p2, v0, p2

    goto :goto_0

    :cond_0
    if-eqz v2, :cond_1

    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Thread;->interrupt()V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    :cond_1
    monitor-exit p0

    return-void

    :goto_2
    monitor-exit p0

    throw p1
.end method
