.class public interface abstract Lcom/google/android/gms/internal/ads/zzdad;
.super Ljava/lang/Object;


# virtual methods
.method public abstract zza(Lcom/google/android/gms/internal/ads/zzfdk;)Lcom/google/android/gms/internal/ads/zzdad;
    .param p1    # Lcom/google/android/gms/internal/ads/zzfdk;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract zzb(Lcom/google/android/gms/internal/ads/zzfeh;)Lcom/google/android/gms/internal/ads/zzdad;
    .param p1    # Lcom/google/android/gms/internal/ads/zzfeh;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract zzh()Ljava/lang/Object;
.end method
