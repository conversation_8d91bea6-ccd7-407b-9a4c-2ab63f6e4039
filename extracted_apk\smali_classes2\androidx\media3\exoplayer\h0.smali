.class public final synthetic Landroidx/media3/exoplayer/h0;
.super Ljava/lang/Object;

# interfaces
.implements Le2/n$a;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/s2;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/s2;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/h0;->a:Landroidx/media3/exoplayer/s2;

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/h0;->a:Landroidx/media3/exoplayer/s2;

    check-cast p1, Landroidx/media3/common/h0$d;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/c1;->h0(Landroidx/media3/exoplayer/s2;Landroidx/media3/common/h0$d;)V

    return-void
.end method
