.class final Landroidx/compose/ui/text/style/TextForegroundStyle$merge$2;
.super Lkotlin/jvm/internal/Lambda;

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Landroidx/compose/ui/text/style/TextForegroundStyle$-CC;->a(Landroidx/compose/ui/text/style/l;Landroidx/compose/ui/text/style/l;)Landroidx/compose/ui/text/style/l;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/Lambda;",
        "Lkotlin/jvm/functions/Function0<",
        "Landroidx/compose/ui/text/style/l;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field final synthetic this$0:Landroidx/compose/ui/text/style/l;


# direct methods
.method public constructor <init>(Landroidx/compose/ui/text/style/l;)V
    .locals 0

    iput-object p1, p0, Landroidx/compose/ui/text/style/TextForegroundStyle$merge$2;->this$0:Landroidx/compose/ui/text/style/l;

    const/4 p1, 0x1

    const/4 p1, 0x0

    invoke-direct {p0, p1}, Lkotlin/jvm/internal/Lambda;-><init>(I)V

    return-void
.end method


# virtual methods
.method public final invoke()Landroidx/compose/ui/text/style/l;
    .locals 1

    iget-object v0, p0, Landroidx/compose/ui/text/style/TextForegroundStyle$merge$2;->this$0:Landroidx/compose/ui/text/style/l;

    return-object v0
.end method

.method public bridge synthetic invoke()Ljava/lang/Object;
    .locals 1

    invoke-virtual {p0}, Landroidx/compose/ui/text/style/TextForegroundStyle$merge$2;->invoke()Landroidx/compose/ui/text/style/l;

    move-result-object v0

    return-object v0
.end method
