.class public interface abstract Landroidx/media3/exoplayer/hls/q$b;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/source/t$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/hls/q;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroidx/media3/exoplayer/source/t$a<",
        "Landroidx/media3/exoplayer/hls/q;",
        ">;"
    }
.end annotation


# virtual methods
.method public abstract d(Landroid/net/Uri;)V
.end method

.method public abstract onPrepared()V
.end method
