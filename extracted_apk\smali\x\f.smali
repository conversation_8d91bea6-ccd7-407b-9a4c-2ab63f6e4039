.class public interface abstract Lx/f;
.super Ljava/lang/Object;

# interfaces
.implements Lx/d;
.implements Lx/b;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lx/f$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<E:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lx/d<",
        "TE;>;",
        "Lx/b;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# virtual methods
.method public abstract add(ILjava/lang/Object;)Lx/f;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ITE;)",
            "Lx/f<",
            "TE;>;"
        }
    .end annotation
.end method

.method public abstract add(Ljava/lang/Object;)Lx/f;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TE;)",
            "Lx/f<",
            "TE;>;"
        }
    .end annotation
.end method

.method public abstract addAll(Ljava/util/Collection;)Lx/f;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "+TE;>;)",
            "Lx/f<",
            "TE;>;"
        }
    .end annotation
.end method

.method public abstract builder()Lx/f$a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lx/f$a<",
            "TE;>;"
        }
    .end annotation
.end method

.method public abstract i0(Lkotlin/jvm/functions/Function1;)Lx/f;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/jvm/functions/Function1<",
            "-TE;",
            "Ljava/lang/Boolean;",
            ">;)",
            "Lx/f<",
            "TE;>;"
        }
    .end annotation
.end method

.method public abstract n(I)Lx/f;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Lx/f<",
            "TE;>;"
        }
    .end annotation
.end method

.method public abstract remove(Ljava/lang/Object;)Lx/f;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TE;)",
            "Lx/f<",
            "TE;>;"
        }
    .end annotation
.end method

.method public abstract removeAll(Ljava/util/Collection;)Lx/f;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Collection<",
            "+TE;>;)",
            "Lx/f<",
            "TE;>;"
        }
    .end annotation
.end method

.method public abstract set(ILjava/lang/Object;)Lx/f;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ITE;)",
            "Lx/f<",
            "TE;>;"
        }
    .end annotation
.end method
