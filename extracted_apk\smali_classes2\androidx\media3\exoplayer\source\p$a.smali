.class public Landroidx/media3/exoplayer/source/p$a;
.super Lz2/e0;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Landroidx/media3/exoplayer/source/p;->W(Lz2/m0;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic b:Landroidx/media3/exoplayer/source/p;


# direct methods
.method public constructor <init>(Landroidx/media3/exoplayer/source/p;Lz2/m0;)V
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/source/p$a;->b:Landroidx/media3/exoplayer/source/p;

    invoke-direct {p0, p2}, Lz2/e0;-><init>(Lz2/m0;)V

    return-void
.end method


# virtual methods
.method public getDurationUs()J
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p$a;->b:Landroidx/media3/exoplayer/source/p;

    invoke-static {v0}, Landroidx/media3/exoplayer/source/p;->t(Landroidx/media3/exoplayer/source/p;)J

    move-result-wide v0

    return-wide v0
.end method
