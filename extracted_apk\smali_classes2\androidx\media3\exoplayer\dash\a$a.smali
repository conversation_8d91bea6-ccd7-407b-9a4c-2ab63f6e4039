.class public interface abstract Landroidx/media3/exoplayer/dash/a$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/dash/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract a(Lt3/s$a;)Landroidx/media3/exoplayer/dash/a$a;
.end method

.method public abstract b(Z)Landroidx/media3/exoplayer/dash/a$a;
.end method

.method public abstract c(Landroidx/media3/common/y;)Landroidx/media3/common/y;
.end method

.method public abstract d(Landroidx/media3/exoplayer/upstream/n;Lm2/c;Ll2/b;I[ILx2/z;IJZLjava/util/List;Landroidx/media3/exoplayer/dash/d$c;Lh2/o;Lj2/x3;Landroidx/media3/exoplayer/upstream/f;)Landroidx/media3/exoplayer/dash/a;
    .param p12    # Landroidx/media3/exoplayer/dash/d$c;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p13    # Lh2/o;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p15    # Landroidx/media3/exoplayer/upstream/f;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/media3/exoplayer/upstream/n;",
            "Lm2/c;",
            "Ll2/b;",
            "I[I",
            "Lx2/z;",
            "IJZ",
            "Ljava/util/List<",
            "Landroidx/media3/common/y;",
            ">;",
            "Landroidx/media3/exoplayer/dash/d$c;",
            "Lh2/o;",
            "Lj2/x3;",
            "Landroidx/media3/exoplayer/upstream/f;",
            ")",
            "Landroidx/media3/exoplayer/dash/a;"
        }
    .end annotation
.end method
