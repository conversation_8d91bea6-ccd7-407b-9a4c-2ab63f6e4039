.class public final Lcom/google/android/gms/measurement/internal/zzdu;
.super Ljava/lang/Object;


# annotations
.annotation build Lcom/google/android/gms/common/util/VisibleForTesting;
.end annotation


# static fields
.field public static final zzA:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzB:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzC:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzD:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzE:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzF:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzG:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzH:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzI:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzJ:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzK:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzL:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzM:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzN:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzO:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzP:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzQ:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzR:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzS:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzT:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzU:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzV:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzW:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzX:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzY:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzZ:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zza:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzaa:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzab:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzac:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzad:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzae:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzaf:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzag:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzah:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzai:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzaj:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzak:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzal:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzam:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzan:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzao:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzap:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzaq:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzar:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzas:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzat:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzau:Lcom/google/android/gms/measurement/internal/zzdt;

.field private static final zzav:Ljava/util/List;

.field private static final zzaw:Ljava/util/Set;

.field public static final zzb:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzc:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzd:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zze:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzf:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzg:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzh:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzi:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzj:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzk:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzl:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzm:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzn:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzo:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzp:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzq:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzr:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzs:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzt:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzu:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzv:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzw:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzx:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzy:Lcom/google/android/gms/measurement/internal/zzdt;

.field public static final zzz:Lcom/google/android/gms/measurement/internal/zzdt;


# direct methods
.method static constructor <clinit>()V
    .locals 8

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    invoke-static {v0}, Ljava/util/Collections;->synchronizedList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    sput-object v0, Lcom/google/android/gms/measurement/internal/zzdu;->zzav:Ljava/util/List;

    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    invoke-static {v0}, Ljava/util/Collections;->synchronizedSet(Ljava/util/Set;)Ljava/util/Set;

    move-result-object v0

    sput-object v0, Lcom/google/android/gms/measurement/internal/zzdu;->zzaw:Ljava/util/Set;

    const-wide/16 v0, 0x2710

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    const-string v1, "measurement.ad_id_cache_time"

    sget-object v2, Lcom/google/android/gms/measurement/internal/zzbj;->zza:Lcom/google/android/gms/measurement/internal/zzbj;

    invoke-static {v1, v0, v0, v2}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v1

    sput-object v1, Lcom/google/android/gms/measurement/internal/zzdu;->zza:Lcom/google/android/gms/measurement/internal/zzdt;

    const-wide/32 v1, 0x5265c00

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    const-string v2, "measurement.monitoring.sample_period_millis"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzbb;->zza:Lcom/google/android/gms/measurement/internal/zzbb;

    invoke-static {v2, v1, v1, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzb:Lcom/google/android/gms/measurement/internal/zzdt;

    const-wide/32 v2, 0x36ee80

    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v2

    const-string v3, "measurement.config.cache_time"

    sget-object v4, Lcom/google/android/gms/measurement/internal/zzbn;->zza:Lcom/google/android/gms/measurement/internal/zzbn;

    invoke-static {v3, v1, v2, v4}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v3

    sput-object v3, Lcom/google/android/gms/measurement/internal/zzdu;->zzc:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v3, "measurement.config.url_scheme"

    sget-object v4, Lcom/google/android/gms/measurement/internal/zzbz;->zza:Lcom/google/android/gms/measurement/internal/zzbz;

    const-string v5, "https"

    invoke-static {v3, v5, v5, v4}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v3

    sput-object v3, Lcom/google/android/gms/measurement/internal/zzdu;->zzd:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v3, "measurement.config.url_authority"

    sget-object v4, Lcom/google/android/gms/measurement/internal/zzcl;->zza:Lcom/google/android/gms/measurement/internal/zzcl;

    const-string v5, "@Topappx"

    invoke-static {v3, v5, v5, v4}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v3

    sput-object v3, Lcom/google/android/gms/measurement/internal/zzdu;->zze:Lcom/google/android/gms/measurement/internal/zzdt;

    const/16 v3, 0x64

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    const-string v4, "measurement.upload.max_bundles"

    sget-object v5, Lcom/google/android/gms/measurement/internal/zzcx;->zza:Lcom/google/android/gms/measurement/internal/zzcx;

    invoke-static {v4, v3, v3, v5}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v4

    sput-object v4, Lcom/google/android/gms/measurement/internal/zzdu;->zzf:Lcom/google/android/gms/measurement/internal/zzdt;

    const/high16 v4, 0x10000

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    const-string v5, "measurement.upload.max_batch_size"

    sget-object v6, Lcom/google/android/gms/measurement/internal/zzdi;->zza:Lcom/google/android/gms/measurement/internal/zzdi;

    invoke-static {v5, v4, v4, v6}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v5

    sput-object v5, Lcom/google/android/gms/measurement/internal/zzdu;->zzg:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v5, "measurement.upload.max_bundle_size"

    sget-object v6, Lcom/google/android/gms/measurement/internal/zzdj;->zza:Lcom/google/android/gms/measurement/internal/zzdj;

    invoke-static {v5, v4, v4, v6}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v4

    sput-object v4, Lcom/google/android/gms/measurement/internal/zzdu;->zzh:Lcom/google/android/gms/measurement/internal/zzdt;

    const/16 v4, 0x3e8

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    const-string v5, "measurement.upload.max_events_per_bundle"

    sget-object v6, Lcom/google/android/gms/measurement/internal/zzdk;->zza:Lcom/google/android/gms/measurement/internal/zzdk;

    invoke-static {v5, v4, v4, v6}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v5

    sput-object v5, Lcom/google/android/gms/measurement/internal/zzdu;->zzi:Lcom/google/android/gms/measurement/internal/zzdt;

    const v5, 0x186a0

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v5

    const-string v6, "measurement.upload.max_events_per_day"

    sget-object v7, Lcom/google/android/gms/measurement/internal/zzdl;->zza:Lcom/google/android/gms/measurement/internal/zzdl;

    invoke-static {v6, v5, v5, v7}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v6

    sput-object v6, Lcom/google/android/gms/measurement/internal/zzdu;->zzj:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v6, "measurement.upload.max_error_events_per_day"

    sget-object v7, Lcom/google/android/gms/measurement/internal/zzbu;->zza:Lcom/google/android/gms/measurement/internal/zzbu;

    invoke-static {v6, v4, v4, v7}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v4

    sput-object v4, Lcom/google/android/gms/measurement/internal/zzdu;->zzk:Lcom/google/android/gms/measurement/internal/zzdt;

    const v4, 0xc350

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    const-string v6, "measurement.upload.max_public_events_per_day"

    sget-object v7, Lcom/google/android/gms/measurement/internal/zzcf;->zza:Lcom/google/android/gms/measurement/internal/zzcf;

    invoke-static {v6, v4, v4, v7}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v4

    sput-object v4, Lcom/google/android/gms/measurement/internal/zzdu;->zzl:Lcom/google/android/gms/measurement/internal/zzdt;

    const/16 v4, 0x2710

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    const-string v6, "measurement.upload.max_conversions_per_day"

    sget-object v7, Lcom/google/android/gms/measurement/internal/zzcq;->zza:Lcom/google/android/gms/measurement/internal/zzcq;

    invoke-static {v6, v4, v4, v7}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v4

    sput-object v4, Lcom/google/android/gms/measurement/internal/zzdu;->zzm:Lcom/google/android/gms/measurement/internal/zzdt;

    const/16 v4, 0xa

    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    const-string v6, "measurement.upload.max_realtime_events_per_day"

    sget-object v7, Lcom/google/android/gms/measurement/internal/zzdb;->zza:Lcom/google/android/gms/measurement/internal/zzdb;

    invoke-static {v6, v4, v4, v7}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v4

    sput-object v4, Lcom/google/android/gms/measurement/internal/zzdu;->zzn:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v4, "measurement.store.max_stored_events_per_app"

    sget-object v6, Lcom/google/android/gms/measurement/internal/zzdm;->zza:Lcom/google/android/gms/measurement/internal/zzdm;

    invoke-static {v4, v5, v5, v6}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v4

    sput-object v4, Lcom/google/android/gms/measurement/internal/zzdu;->zzo:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v4, "measurement.upload.url"

    sget-object v5, Lcom/google/android/gms/measurement/internal/zzdn;->zza:Lcom/google/android/gms/measurement/internal/zzdn;

    const-string v6, "@Topappx"

    invoke-static {v4, v6, v6, v5}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v4

    sput-object v4, Lcom/google/android/gms/measurement/internal/zzdu;->zzp:Lcom/google/android/gms/measurement/internal/zzdt;

    const-wide/32 v4, 0x2932e00

    invoke-static {v4, v5}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v4

    const-string v5, "measurement.upload.backoff_period"

    sget-object v6, Lcom/google/android/gms/measurement/internal/zzdo;->zza:Lcom/google/android/gms/measurement/internal/zzdo;

    invoke-static {v5, v4, v4, v6}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v4

    sput-object v4, Lcom/google/android/gms/measurement/internal/zzdu;->zzq:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v4, "measurement.upload.window_interval"

    sget-object v5, Lcom/google/android/gms/measurement/internal/zzdp;->zza:Lcom/google/android/gms/measurement/internal/zzdp;

    invoke-static {v4, v2, v2, v5}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v4

    sput-object v4, Lcom/google/android/gms/measurement/internal/zzdu;->zzr:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v4, "measurement.upload.interval"

    sget-object v5, Lcom/google/android/gms/measurement/internal/zzaz;->zza:Lcom/google/android/gms/measurement/internal/zzaz;

    invoke-static {v4, v2, v2, v5}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzs:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.upload.realtime_upload_interval"

    sget-object v4, Lcom/google/android/gms/measurement/internal/zzba;->zza:Lcom/google/android/gms/measurement/internal/zzba;

    invoke-static {v2, v0, v0, v4}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v0

    sput-object v0, Lcom/google/android/gms/measurement/internal/zzdu;->zzt:Lcom/google/android/gms/measurement/internal/zzdt;

    const-wide/16 v4, 0x3e8

    invoke-static {v4, v5}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    const-string v2, "measurement.upload.debug_upload_interval"

    sget-object v4, Lcom/google/android/gms/measurement/internal/zzbc;->zza:Lcom/google/android/gms/measurement/internal/zzbc;

    invoke-static {v2, v0, v0, v4}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v0

    sput-object v0, Lcom/google/android/gms/measurement/internal/zzdu;->zzu:Lcom/google/android/gms/measurement/internal/zzdt;

    const-wide/16 v4, 0x1f4

    invoke-static {v4, v5}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    const-string v2, "measurement.upload.minimum_delay"

    sget-object v4, Lcom/google/android/gms/measurement/internal/zzbd;->zza:Lcom/google/android/gms/measurement/internal/zzbd;

    invoke-static {v2, v0, v0, v4}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v0

    sput-object v0, Lcom/google/android/gms/measurement/internal/zzdu;->zzv:Lcom/google/android/gms/measurement/internal/zzdt;

    const-wide/32 v4, 0xea60

    invoke-static {v4, v5}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    const-string v2, "measurement.alarm_manager.minimum_interval"

    sget-object v4, Lcom/google/android/gms/measurement/internal/zzbe;->zza:Lcom/google/android/gms/measurement/internal/zzbe;

    invoke-static {v2, v0, v0, v4}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v0

    sput-object v0, Lcom/google/android/gms/measurement/internal/zzdu;->zzw:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v0, "measurement.upload.stale_data_deletion_interval"

    sget-object v2, Lcom/google/android/gms/measurement/internal/zzbf;->zza:Lcom/google/android/gms/measurement/internal/zzbf;

    invoke-static {v0, v1, v1, v2}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v0

    sput-object v0, Lcom/google/android/gms/measurement/internal/zzdu;->zzx:Lcom/google/android/gms/measurement/internal/zzdt;

    const-wide/32 v0, 0x240c8400

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    const-string v1, "measurement.upload.refresh_blacklisted_config_interval"

    sget-object v2, Lcom/google/android/gms/measurement/internal/zzbg;->zza:Lcom/google/android/gms/measurement/internal/zzbg;

    invoke-static {v1, v0, v0, v2}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v1

    sput-object v1, Lcom/google/android/gms/measurement/internal/zzdu;->zzy:Lcom/google/android/gms/measurement/internal/zzdt;

    const-wide/16 v1, 0x3a98

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    const-string v2, "measurement.upload.initial_upload_delay_time"

    sget-object v4, Lcom/google/android/gms/measurement/internal/zzbh;->zza:Lcom/google/android/gms/measurement/internal/zzbh;

    invoke-static {v2, v1, v1, v4}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v1

    sput-object v1, Lcom/google/android/gms/measurement/internal/zzdu;->zzz:Lcom/google/android/gms/measurement/internal/zzdt;

    const-wide/32 v1, 0x1b7740

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    const-string v2, "measurement.upload.retry_time"

    sget-object v4, Lcom/google/android/gms/measurement/internal/zzbi;->zza:Lcom/google/android/gms/measurement/internal/zzbi;

    invoke-static {v2, v1, v1, v4}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v1

    sput-object v1, Lcom/google/android/gms/measurement/internal/zzdu;->zzA:Lcom/google/android/gms/measurement/internal/zzdt;

    const/4 v1, 0x6

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "measurement.upload.retry_count"

    sget-object v4, Lcom/google/android/gms/measurement/internal/zzbk;->zza:Lcom/google/android/gms/measurement/internal/zzbk;

    invoke-static {v2, v1, v1, v4}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v1

    sput-object v1, Lcom/google/android/gms/measurement/internal/zzdu;->zzB:Lcom/google/android/gms/measurement/internal/zzdt;

    const-wide v1, 0x90321000L

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    const-string v2, "measurement.upload.max_queue_time"

    sget-object v4, Lcom/google/android/gms/measurement/internal/zzbl;->zza:Lcom/google/android/gms/measurement/internal/zzbl;

    invoke-static {v2, v1, v1, v4}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v1

    sput-object v1, Lcom/google/android/gms/measurement/internal/zzdu;->zzC:Lcom/google/android/gms/measurement/internal/zzdt;

    const/4 v1, 0x4

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "measurement.lifetimevalue.max_currency_tracked"

    sget-object v4, Lcom/google/android/gms/measurement/internal/zzbm;->zza:Lcom/google/android/gms/measurement/internal/zzbm;

    invoke-static {v2, v1, v1, v4}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v1

    sput-object v1, Lcom/google/android/gms/measurement/internal/zzdu;->zzD:Lcom/google/android/gms/measurement/internal/zzdt;

    const/16 v1, 0xc8

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "measurement.audience.filter_result_max_count"

    sget-object v4, Lcom/google/android/gms/measurement/internal/zzbo;->zza:Lcom/google/android/gms/measurement/internal/zzbo;

    invoke-static {v2, v1, v1, v4}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v1

    sput-object v1, Lcom/google/android/gms/measurement/internal/zzdu;->zzE:Lcom/google/android/gms/measurement/internal/zzdt;

    const/16 v1, 0x19

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "measurement.upload.max_public_user_properties"

    const/4 v4, 0x1

    const/4 v4, 0x0

    invoke-static {v2, v1, v1, v4}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzF:Lcom/google/android/gms/measurement/internal/zzdt;

    const/16 v2, 0x1f4

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    const-string v5, "measurement.upload.max_event_name_cardinality"

    invoke-static {v5, v2, v2, v4}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzG:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.upload.max_public_event_params"

    invoke-static {v2, v1, v1, v4}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v1

    sput-object v1, Lcom/google/android/gms/measurement/internal/zzdu;->zzH:Lcom/google/android/gms/measurement/internal/zzdt;

    const-wide/16 v1, 0x1388

    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    const-string v2, "measurement.service_client.idle_disconnect_millis"

    sget-object v5, Lcom/google/android/gms/measurement/internal/zzbp;->zza:Lcom/google/android/gms/measurement/internal/zzbp;

    invoke-static {v2, v1, v1, v5}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v1

    sput-object v1, Lcom/google/android/gms/measurement/internal/zzdu;->zzI:Lcom/google/android/gms/measurement/internal/zzdt;

    sget-object v1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    const-string v2, "measurement.test.boolean_flag"

    sget-object v5, Lcom/google/android/gms/measurement/internal/zzbq;->zza:Lcom/google/android/gms/measurement/internal/zzbq;

    invoke-static {v2, v1, v1, v5}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzJ:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.test.string_flag"

    sget-object v5, Lcom/google/android/gms/measurement/internal/zzbr;->zza:Lcom/google/android/gms/measurement/internal/zzbr;

    const-string v6, "---"

    invoke-static {v2, v6, v6, v5}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzK:Lcom/google/android/gms/measurement/internal/zzdt;

    const-wide/16 v5, -0x1

    invoke-static {v5, v6}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v2

    const-string v5, "measurement.test.long_flag"

    sget-object v6, Lcom/google/android/gms/measurement/internal/zzbs;->zza:Lcom/google/android/gms/measurement/internal/zzbs;

    invoke-static {v5, v2, v2, v6}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzL:Lcom/google/android/gms/measurement/internal/zzdt;

    const/4 v2, -0x2

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    const-string v5, "measurement.test.int_flag"

    sget-object v6, Lcom/google/android/gms/measurement/internal/zzbt;->zza:Lcom/google/android/gms/measurement/internal/zzbt;

    invoke-static {v5, v2, v2, v6}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzM:Lcom/google/android/gms/measurement/internal/zzdt;

    const-wide/high16 v5, -0x3ff8000000000000L    # -3.0

    invoke-static {v5, v6}, Ljava/lang/Double;->valueOf(D)Ljava/lang/Double;

    move-result-object v2

    const-string v5, "measurement.test.double_flag"

    sget-object v6, Lcom/google/android/gms/measurement/internal/zzbv;->zza:Lcom/google/android/gms/measurement/internal/zzbv;

    invoke-static {v5, v2, v2, v6}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzN:Lcom/google/android/gms/measurement/internal/zzdt;

    const/16 v2, 0x32

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    const-string v5, "measurement.experiment.max_ids"

    sget-object v6, Lcom/google/android/gms/measurement/internal/zzbw;->zza:Lcom/google/android/gms/measurement/internal/zzbw;

    invoke-static {v5, v2, v2, v6}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzO:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.max_bundles_per_iteration"

    sget-object v5, Lcom/google/android/gms/measurement/internal/zzbx;->zza:Lcom/google/android/gms/measurement/internal/zzbx;

    invoke-static {v2, v3, v3, v5}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzP:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.sdk.attribution.cache.ttl"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzby;->zza:Lcom/google/android/gms/measurement/internal/zzby;

    invoke-static {v2, v0, v0, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v0

    sput-object v0, Lcom/google/android/gms/measurement/internal/zzdu;->zzQ:Lcom/google/android/gms/measurement/internal/zzdt;

    const-wide/32 v2, 0x6ddd00

    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    const-string v2, "measurement.redaction.app_instance_id.ttl"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzca;->zza:Lcom/google/android/gms/measurement/internal/zzca;

    invoke-static {v2, v0, v0, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v0

    sput-object v0, Lcom/google/android/gms/measurement/internal/zzdu;->zzR:Lcom/google/android/gms/measurement/internal/zzdt;

    sget-object v0, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    const-string v2, "measurement.collection.log_event_and_bundle_v2"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzcb;->zza:Lcom/google/android/gms/measurement/internal/zzcb;

    invoke-static {v2, v0, v0, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzS:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.quality.checksum"

    invoke-static {v2, v1, v1, v4}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzT:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.audience.use_bundle_end_timestamp_for_non_sequence_property_filters"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzcc;->zza:Lcom/google/android/gms/measurement/internal/zzcc;

    invoke-static {v2, v1, v1, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzU:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.audience.refresh_event_count_filters_timestamp"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzcd;->zza:Lcom/google/android/gms/measurement/internal/zzcd;

    invoke-static {v2, v1, v1, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzV:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.audience.use_bundle_timestamp_for_event_count_filters"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzce;->zza:Lcom/google/android/gms/measurement/internal/zzce;

    invoke-static {v2, v1, v1, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzW:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.sdk.collection.retrieve_deeplink_from_bow_2"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzcg;->zza:Lcom/google/android/gms/measurement/internal/zzcg;

    invoke-static {v2, v0, v0, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzX:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.sdk.collection.last_deep_link_referrer_campaign2"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzch;->zza:Lcom/google/android/gms/measurement/internal/zzch;

    invoke-static {v2, v1, v1, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzY:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.lifecycle.app_in_background_parameter"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzci;->zza:Lcom/google/android/gms/measurement/internal/zzci;

    invoke-static {v2, v1, v1, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzZ:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.integration.disable_firebase_instance_id"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzcj;->zza:Lcom/google/android/gms/measurement/internal/zzcj;

    invoke-static {v2, v1, v1, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzaa:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.collection.service.update_with_analytics_fix"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzck;->zza:Lcom/google/android/gms/measurement/internal/zzck;

    invoke-static {v2, v1, v1, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzab:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.client.firebase_feature_rollout.v1.enable"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzcm;->zza:Lcom/google/android/gms/measurement/internal/zzcm;

    invoke-static {v2, v0, v0, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzac:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.client.sessions.check_on_reset_and_enable2"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzcn;->zza:Lcom/google/android/gms/measurement/internal/zzcn;

    invoke-static {v2, v0, v0, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzad:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.collection.synthetic_data_mitigation"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzco;->zza:Lcom/google/android/gms/measurement/internal/zzco;

    invoke-static {v2, v1, v1, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzae:Lcom/google/android/gms/measurement/internal/zzdt;

    const v2, 0x31b50

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzcp;->zza:Lcom/google/android/gms/measurement/internal/zzcp;

    const-string v4, "measurement.service.storage_consent_support_version"

    invoke-static {v4, v2, v2, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzaf:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.client.click_identifier_control.dev"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzcr;->zza:Lcom/google/android/gms/measurement/internal/zzcr;

    invoke-static {v2, v1, v1, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzag:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.service.click_identifier_control"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzcs;->zza:Lcom/google/android/gms/measurement/internal/zzcs;

    invoke-static {v2, v1, v1, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzah:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.service.store_null_safelist"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzct;->zza:Lcom/google/android/gms/measurement/internal/zzct;

    invoke-static {v2, v0, v0, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzai:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.service.store_safelist"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzcu;->zza:Lcom/google/android/gms/measurement/internal/zzcu;

    invoke-static {v2, v0, v0, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzaj:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.collection.enable_session_stitching_token.service"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzcv;->zza:Lcom/google/android/gms/measurement/internal/zzcv;

    invoke-static {v2, v1, v1, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzak:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.collection.enable_session_stitching_token.service_new"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzcw;->zza:Lcom/google/android/gms/measurement/internal/zzcw;

    invoke-static {v2, v0, v0, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzal:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.collection.enable_session_stitching_token.client.dev"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzcy;->zza:Lcom/google/android/gms/measurement/internal/zzcy;

    invoke-static {v2, v0, v0, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v2

    sput-object v2, Lcom/google/android/gms/measurement/internal/zzdu;->zzam:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v2, "measurement.session_stitching_token_enabled"

    sget-object v3, Lcom/google/android/gms/measurement/internal/zzcz;->zza:Lcom/google/android/gms/measurement/internal/zzcz;

    invoke-static {v2, v1, v1, v3}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v1

    sput-object v1, Lcom/google/android/gms/measurement/internal/zzdu;->zzan:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v1, "measurement.redaction.e_tag"

    sget-object v2, Lcom/google/android/gms/measurement/internal/zzda;->zza:Lcom/google/android/gms/measurement/internal/zzda;

    invoke-static {v1, v0, v0, v2}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v1

    sput-object v1, Lcom/google/android/gms/measurement/internal/zzdu;->zzao:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v1, "measurement.redaction.client_ephemeral_aiid_generation"

    sget-object v2, Lcom/google/android/gms/measurement/internal/zzdc;->zza:Lcom/google/android/gms/measurement/internal/zzdc;

    invoke-static {v1, v0, v0, v2}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v1

    sput-object v1, Lcom/google/android/gms/measurement/internal/zzdu;->zzap:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v1, "measurement.redaction.retain_major_os_version"

    sget-object v2, Lcom/google/android/gms/measurement/internal/zzdd;->zza:Lcom/google/android/gms/measurement/internal/zzdd;

    invoke-static {v1, v0, v0, v2}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v1

    sput-object v1, Lcom/google/android/gms/measurement/internal/zzdu;->zzaq:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v1, "measurement.redaction.scion_payload_generator"

    sget-object v2, Lcom/google/android/gms/measurement/internal/zzde;->zza:Lcom/google/android/gms/measurement/internal/zzde;

    invoke-static {v1, v0, v0, v2}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v1

    sput-object v1, Lcom/google/android/gms/measurement/internal/zzdu;->zzar:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v1, "measurement.audience.dynamic_filters.oob_fix"

    sget-object v2, Lcom/google/android/gms/measurement/internal/zzdf;->zza:Lcom/google/android/gms/measurement/internal/zzdf;

    invoke-static {v1, v0, v0, v2}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v1

    sput-object v1, Lcom/google/android/gms/measurement/internal/zzdu;->zzas:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v1, "measurement.service.clear_global_params_on_uninstall"

    sget-object v2, Lcom/google/android/gms/measurement/internal/zzdg;->zza:Lcom/google/android/gms/measurement/internal/zzdg;

    invoke-static {v1, v0, v0, v2}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v1

    sput-object v1, Lcom/google/android/gms/measurement/internal/zzdu;->zzat:Lcom/google/android/gms/measurement/internal/zzdt;

    const-string v1, "measurement.sessionid.enable_client_session_id"

    sget-object v2, Lcom/google/android/gms/measurement/internal/zzdh;->zza:Lcom/google/android/gms/measurement/internal/zzdh;

    invoke-static {v1, v0, v0, v2}, Lcom/google/android/gms/measurement/internal/zzdu;->zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;

    move-result-object v0

    sput-object v0, Lcom/google/android/gms/measurement/internal/zzdu;->zzau:Lcom/google/android/gms/measurement/internal/zzdt;

    return-void
.end method

.method public static zza(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;)Lcom/google/android/gms/measurement/internal/zzdt;
    .locals 7
    .annotation build Lcom/google/android/gms/common/util/VisibleForTesting;
    .end annotation

    new-instance v6, Lcom/google/android/gms/measurement/internal/zzdt;

    const/4 v5, 0x1

    const/4 v5, 0x0

    move-object v0, v6

    move-object v1, p0

    move-object v2, p1

    move-object v3, p2

    move-object v4, p3

    invoke-direct/range {v0 .. v5}, Lcom/google/android/gms/measurement/internal/zzdt;-><init>(Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Lcom/google/android/gms/measurement/internal/zzdq;Lcom/google/android/gms/measurement/internal/zzds;)V

    sget-object p0, Lcom/google/android/gms/measurement/internal/zzdu;->zzav:Ljava/util/List;

    invoke-interface {p0, v6}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object v6
.end method

.method public static bridge synthetic zzb()Ljava/util/List;
    .locals 1

    sget-object v0, Lcom/google/android/gms/measurement/internal/zzdu;->zzav:Ljava/util/List;

    return-object v0
.end method

.method public static zzc(Landroid/content/Context;)Ljava/util/Map;
    .locals 2

    invoke-virtual {p0}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object p0

    const-string v0, "com.google.android.gms.measurement"

    invoke-static {v0}, Lcom/google/android/gms/internal/measurement/zzhq;->zza(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v0

    sget-object v1, Lcom/google/android/gms/measurement/internal/zzay;->zza:Lcom/google/android/gms/measurement/internal/zzay;

    invoke-static {p0, v0, v1}, Lcom/google/android/gms/internal/measurement/zzhf;->zza(Landroid/content/ContentResolver;Landroid/net/Uri;Ljava/lang/Runnable;)Lcom/google/android/gms/internal/measurement/zzhf;

    move-result-object p0

    if-nez p0, :cond_0

    invoke-static {}, Ljava/util/Collections;->emptyMap()Ljava/util/Map;

    move-result-object p0

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Lcom/google/android/gms/internal/measurement/zzhf;->zzc()Ljava/util/Map;

    move-result-object p0

    :goto_0
    return-object p0
.end method
