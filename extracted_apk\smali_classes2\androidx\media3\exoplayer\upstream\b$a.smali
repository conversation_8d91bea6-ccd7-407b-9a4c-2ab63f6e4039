.class public interface abstract Landroidx/media3/exoplayer/upstream/b$a;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/upstream/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract a()Landroidx/media3/exoplayer/upstream/a;
.end method

.method public abstract next()Landroidx/media3/exoplayer/upstream/b$a;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end method
