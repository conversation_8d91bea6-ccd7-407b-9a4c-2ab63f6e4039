.class public interface abstract Lz2/s;
.super Ljava/lang/Object;


# virtual methods
.method public abstract b()Lz2/s;
.end method

.method public abstract c(Lz2/u;)V
.end method

.method public abstract d(Lz2/t;Lz2/l0;)I
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract e(Lz2/t;)Z
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract release()V
.end method

.method public abstract seek(JJ)V
.end method
