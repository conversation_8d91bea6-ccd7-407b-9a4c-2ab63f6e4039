.class public final Landroidx/media3/exoplayer/source/u$b;
.super Ljava/lang/Object;

# interfaces
.implements Lu2/e0;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/source/u;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "b"
.end annotation


# instance fields
.field public a:I

.field public b:Z

.field public final synthetic c:Landroidx/media3/exoplayer/source/u;


# direct methods
.method public constructor <init>(Landroidx/media3/exoplayer/source/u;)V
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/source/u$b;->c:Landroidx/media3/exoplayer/source/u;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Landroidx/media3/exoplayer/source/u;Landroidx/media3/exoplayer/source/u$a;)V
    .locals 0

    invoke-direct {p0, p1}, Landroidx/media3/exoplayer/source/u$b;-><init>(Landroidx/media3/exoplayer/source/u;)V

    return-void
.end method


# virtual methods
.method public final a()V
    .locals 8

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/u$b;->b:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/source/u$b;->c:Landroidx/media3/exoplayer/source/u;

    invoke-static {v0}, Landroidx/media3/exoplayer/source/u;->c(Landroidx/media3/exoplayer/source/u;)Landroidx/media3/exoplayer/source/m$a;

    move-result-object v1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/u$b;->c:Landroidx/media3/exoplayer/source/u;

    iget-object v0, v0, Landroidx/media3/exoplayer/source/u;->k:Landroidx/media3/common/y;

    iget-object v0, v0, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-static {v0}, Landroidx/media3/common/f0;->k(Ljava/lang/String;)I

    move-result v2

    iget-object v0, p0, Landroidx/media3/exoplayer/source/u$b;->c:Landroidx/media3/exoplayer/source/u;

    iget-object v3, v0, Landroidx/media3/exoplayer/source/u;->k:Landroidx/media3/common/y;

    const/4 v4, 0x1

    const/4 v4, 0x0

    const/4 v5, 0x1

    const/4 v5, 0x0

    const-wide/16 v6, 0x0

    invoke-virtual/range {v1 .. v7}, Landroidx/media3/exoplayer/source/m$a;->h(ILandroidx/media3/common/y;ILjava/lang/Object;J)V

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/source/u$b;->b:Z

    :cond_0
    return-void
.end method

.method public b()V
    .locals 2

    iget v0, p0, Landroidx/media3/exoplayer/source/u$b;->a:I

    const/4 v1, 0x2

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    iput v0, p0, Landroidx/media3/exoplayer/source/u$b;->a:I

    :cond_0
    return-void
.end method

.method public c(Landroidx/media3/exoplayer/t1;Landroidx/media3/decoder/DecoderInputBuffer;I)I
    .locals 7

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/u$b;->a()V

    iget-object v0, p0, Landroidx/media3/exoplayer/source/u$b;->c:Landroidx/media3/exoplayer/source/u;

    iget-boolean v1, v0, Landroidx/media3/exoplayer/source/u;->m:Z

    const/4 v2, 0x2

    if-eqz v1, :cond_0

    iget-object v3, v0, Landroidx/media3/exoplayer/source/u;->n:[B

    if-nez v3, :cond_0

    iput v2, p0, Landroidx/media3/exoplayer/source/u$b;->a:I

    :cond_0
    iget v3, p0, Landroidx/media3/exoplayer/source/u$b;->a:I

    const/4 v4, -0x4

    if-ne v3, v2, :cond_1

    const/4 p1, 0x4

    invoke-virtual {p2, p1}, Landroidx/media3/decoder/a;->addFlag(I)V

    return v4

    :cond_1
    and-int/lit8 v5, p3, 0x2

    const/4 v6, 0x1

    if-nez v5, :cond_6

    if-nez v3, :cond_2

    goto :goto_0

    :cond_2
    if-nez v1, :cond_3

    const/4 p1, -0x3

    return p1

    :cond_3
    iget-object p1, v0, Landroidx/media3/exoplayer/source/u;->n:[B

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p2, v6}, Landroidx/media3/decoder/a;->addFlag(I)V

    const-wide/16 v0, 0x0

    iput-wide v0, p2, Landroidx/media3/decoder/DecoderInputBuffer;->f:J

    and-int/lit8 p1, p3, 0x4

    if-nez p1, :cond_4

    iget-object p1, p0, Landroidx/media3/exoplayer/source/u$b;->c:Landroidx/media3/exoplayer/source/u;

    iget p1, p1, Landroidx/media3/exoplayer/source/u;->o:I

    invoke-virtual {p2, p1}, Landroidx/media3/decoder/DecoderInputBuffer;->b(I)V

    iget-object p1, p2, Landroidx/media3/decoder/DecoderInputBuffer;->c:Ljava/nio/ByteBuffer;

    iget-object p2, p0, Landroidx/media3/exoplayer/source/u$b;->c:Landroidx/media3/exoplayer/source/u;

    iget-object v0, p2, Landroidx/media3/exoplayer/source/u;->n:[B

    const/4 v1, 0x1

    const/4 v1, 0x0

    iget p2, p2, Landroidx/media3/exoplayer/source/u;->o:I

    invoke-virtual {p1, v0, v1, p2}, Ljava/nio/ByteBuffer;->put([BII)Ljava/nio/ByteBuffer;

    :cond_4
    and-int/lit8 p1, p3, 0x1

    if-nez p1, :cond_5

    iput v2, p0, Landroidx/media3/exoplayer/source/u$b;->a:I

    :cond_5
    return v4

    :cond_6
    :goto_0
    iget-object p2, v0, Landroidx/media3/exoplayer/source/u;->k:Landroidx/media3/common/y;

    iput-object p2, p1, Landroidx/media3/exoplayer/t1;->b:Landroidx/media3/common/y;

    iput v6, p0, Landroidx/media3/exoplayer/source/u$b;->a:I

    const/4 p1, -0x5

    return p1
.end method

.method public isReady()Z
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/u$b;->c:Landroidx/media3/exoplayer/source/u;

    iget-boolean v0, v0, Landroidx/media3/exoplayer/source/u;->m:Z

    return v0
.end method

.method public maybeThrowError()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/source/u$b;->c:Landroidx/media3/exoplayer/source/u;

    iget-boolean v1, v0, Landroidx/media3/exoplayer/source/u;->l:Z

    if-nez v1, :cond_0

    iget-object v0, v0, Landroidx/media3/exoplayer/source/u;->j:Landroidx/media3/exoplayer/upstream/Loader;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/upstream/Loader;->maybeThrowError()V

    :cond_0
    return-void
.end method

.method public skipData(J)I
    .locals 3

    invoke-virtual {p0}, Landroidx/media3/exoplayer/source/u$b;->a()V

    const-wide/16 v0, 0x0

    cmp-long v2, p1, v0

    if-lez v2, :cond_0

    iget p1, p0, Landroidx/media3/exoplayer/source/u$b;->a:I

    const/4 p2, 0x2

    if-eq p1, p2, :cond_0

    iput p2, p0, Landroidx/media3/exoplayer/source/u$b;->a:I

    const/4 p1, 0x1

    return p1

    :cond_0
    const/4 p1, 0x1

    const/4 p1, 0x0

    return p1
.end method
