.class public final Landroidx/media3/common/b0$j;
.super Landroidx/media3/common/b0$k;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/common/b0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "j"
.end annotation

.annotation runtime Ljava/lang/Deprecated;
.end annotation


# direct methods
.method public constructor <init>(Landroidx/media3/common/b0$k$a;)V
    .locals 1

    const/4 v0, 0x1

    const/4 v0, 0x0

    invoke-direct {p0, p1, v0}, Landroidx/media3/common/b0$k;-><init>(Landroidx/media3/common/b0$k$a;Landroidx/media3/common/b0$a;)V

    return-void
.end method

.method public synthetic constructor <init>(Landroidx/media3/common/b0$k$a;Landroidx/media3/common/b0$a;)V
    .locals 0

    invoke-direct {p0, p1}, Landroidx/media3/common/b0$j;-><init>(Landroidx/media3/common/b0$k$a;)V

    return-void
.end method
