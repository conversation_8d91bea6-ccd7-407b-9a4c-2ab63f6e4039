.class public final synthetic Landroidx/media3/common/w;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/common/base/f;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, <PERSON>java/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final apply(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Landroid/os/Bundle;

    invoke-static {p1}, Landroidx/media3/common/a0;->a(Landroid/os/Bundle;)Landroidx/media3/common/a0;

    move-result-object p1

    return-object p1
.end method
