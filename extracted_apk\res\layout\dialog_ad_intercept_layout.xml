<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:id="@id/llContent" android:background="@drawable/ps_link_16_bg" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="40.0dip" android:layout_marginRight="40.0dip">
        <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_horizontal" android:id="@id/ivIcon" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginTop="16.0dip" android:src="@mipmap/co_download" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <TextView android:textSize="12.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tvTips" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="16.0dip" android:text="@string/co_ad_dialog_title" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ivIcon" style="@style/style_regular_text" />
        <androidx.cardview.widget.CardView android:id="@id/cardView" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="16.0dip" app:cardCornerRadius="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvTips">
            <FrameLayout android:id="@id/flAdContent" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvTips" />
        </androidx.cardview.widget.CardView>
        <View android:id="@id/viewLineL" android:background="@color/white_10" android:layout_width="0.0dip" android:layout_height="1.0dip" android:layout_marginStart="16.0dip" app:layout_constraintBottom_toBottomOf="@id/tvOr" app:layout_constraintEnd_toStartOf="@id/tvOr" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/tvOr" />
        <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white_40" android:id="@id/tvOr" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="8.0dip" android:layout_marginTop="12.0dip" android:layout_marginRight="8.0dip" android:layout_marginBottom="12.0dip" android:text="@string/co_ad_dialog_or" app:layout_constraintBottom_toTopOf="@id/cl2MemberInfo" app:layout_constraintEnd_toStartOf="@id/viewLineR" app:layout_constraintStart_toEndOf="@id/viewLineL" app:layout_constraintTop_toBottomOf="@id/cardView" style="@style/style_medium_text" />
        <View android:id="@id/viewLineR" android:background="@color/white_10" android:layout_width="0.0dip" android:layout_height="1.0dip" android:layout_marginEnd="16.0dip" app:layout_constraintBottom_toBottomOf="@id/tvOr" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvOr" app:layout_constraintTop_toTopOf="@id/tvOr" />
        <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/cl2MemberInfo" android:background="@drawable/ps_bg_app_8_bg" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="36.0dip" android:layout_marginLeft="16.0dip" android:layout_marginRight="16.0dip" android:layout_marginBottom="16.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvOr">
            <TextView android:textSize="14.0sp" android:textColor="@color/text_01" android:id="@id/tvGetAd" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/member_guide_dialog_get_ad" android:layout_marginStart="4.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/ivPremium" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/style_import_text" />
            <androidx.appcompat.widget.AppCompatTextView android:textSize="10.0sp" android:textStyle="bold" android:textColor="#ff130f26" android:layout_gravity="end" android:id="@id/ivPremium" android:background="@drawable/bg_days_left" android:paddingLeft="4.0dip" android:paddingTop="2.0dip" android:paddingRight="4.0dip" android:paddingBottom="2.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/member_download_premium" android:drawablePadding="2.0dip" android:drawableStart="@mipmap/ic_premium" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvGetAd" app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <TextView android:textSize="14.0sp" android:textColor="@color/text_01" android:gravity="center" android:id="@id/tvGo2Integral" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:layout_marginTop="16.0dip" android:layout_marginRight="16.0dip" android:layout_marginBottom="20.0dip" android:text="@string/co_other_ways" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintTop_toBottomOf="@id/cl2MemberInfo" style="@style/style_medium_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.appcompat.widget.AppCompatImageView android:layout_gravity="center_horizontal" android:id="@id/ivClose" android:layout_width="48.0dip" android:layout_height="48.0dip" android:layout_marginTop="24.0dip" android:src="@mipmap/co_close" />
</LinearLayout>
