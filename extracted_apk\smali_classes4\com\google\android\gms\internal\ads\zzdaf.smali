.class public final Lcom/google/android/gms/internal/ads/zzdaf;
.super Ljava/lang/Object;


# instance fields
.field private zza:Landroid/content/Context;

.field private zzb:Lcom/google/android/gms/internal/ads/zzfhh;

.field private zzc:Landroid/os/Bundle;

.field private zzd:Lcom/google/android/gms/internal/ads/zzfgz;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private zze:Lcom/google/android/gms/internal/ads/zzczz;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private zzf:Lcom/google/android/gms/internal/ads/zzeis;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static bridge synthetic zza(Lcom/google/android/gms/internal/ads/zzdaf;)Landroid/content/Context;
    .locals 0

    iget-object p0, p0, Lcom/google/android/gms/internal/ads/zzdaf;->zza:Landroid/content/Context;

    return-object p0
.end method

.method public static bridge synthetic zzb(Lcom/google/android/gms/internal/ads/zzdaf;)Landroid/os/Bundle;
    .locals 0

    iget-object p0, p0, Lcom/google/android/gms/internal/ads/zzdaf;->zzc:Landroid/os/Bundle;

    return-object p0
.end method

.method public static bridge synthetic zzc(Lcom/google/android/gms/internal/ads/zzdaf;)Lcom/google/android/gms/internal/ads/zzczz;
    .locals 0

    iget-object p0, p0, Lcom/google/android/gms/internal/ads/zzdaf;->zze:Lcom/google/android/gms/internal/ads/zzczz;

    return-object p0
.end method

.method public static bridge synthetic zzk(Lcom/google/android/gms/internal/ads/zzdaf;)Lcom/google/android/gms/internal/ads/zzeis;
    .locals 0

    iget-object p0, p0, Lcom/google/android/gms/internal/ads/zzdaf;->zzf:Lcom/google/android/gms/internal/ads/zzeis;

    return-object p0
.end method

.method public static bridge synthetic zzl(Lcom/google/android/gms/internal/ads/zzdaf;)Lcom/google/android/gms/internal/ads/zzfgz;
    .locals 0

    iget-object p0, p0, Lcom/google/android/gms/internal/ads/zzdaf;->zzd:Lcom/google/android/gms/internal/ads/zzfgz;

    return-object p0
.end method

.method public static bridge synthetic zzm(Lcom/google/android/gms/internal/ads/zzdaf;)Lcom/google/android/gms/internal/ads/zzfhh;
    .locals 0

    iget-object p0, p0, Lcom/google/android/gms/internal/ads/zzdaf;->zzb:Lcom/google/android/gms/internal/ads/zzfhh;

    return-object p0
.end method


# virtual methods
.method public final zzd(Lcom/google/android/gms/internal/ads/zzeis;)Lcom/google/android/gms/internal/ads/zzdaf;
    .locals 0
    .param p1    # Lcom/google/android/gms/internal/ads/zzeis;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/google/android/gms/internal/ads/zzdaf;->zzf:Lcom/google/android/gms/internal/ads/zzeis;

    return-object p0
.end method

.method public final zze(Landroid/content/Context;)Lcom/google/android/gms/internal/ads/zzdaf;
    .locals 0

    iput-object p1, p0, Lcom/google/android/gms/internal/ads/zzdaf;->zza:Landroid/content/Context;

    return-object p0
.end method

.method public final zzf(Landroid/os/Bundle;)Lcom/google/android/gms/internal/ads/zzdaf;
    .locals 0

    iput-object p1, p0, Lcom/google/android/gms/internal/ads/zzdaf;->zzc:Landroid/os/Bundle;

    return-object p0
.end method

.method public final zzg(Lcom/google/android/gms/internal/ads/zzczz;)Lcom/google/android/gms/internal/ads/zzdaf;
    .locals 0
    .param p1    # Lcom/google/android/gms/internal/ads/zzczz;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iput-object p1, p0, Lcom/google/android/gms/internal/ads/zzdaf;->zze:Lcom/google/android/gms/internal/ads/zzczz;

    return-object p0
.end method

.method public final zzh(Lcom/google/android/gms/internal/ads/zzfgz;)Lcom/google/android/gms/internal/ads/zzdaf;
    .locals 0

    iput-object p1, p0, Lcom/google/android/gms/internal/ads/zzdaf;->zzd:Lcom/google/android/gms/internal/ads/zzfgz;

    return-object p0
.end method

.method public final zzi(Lcom/google/android/gms/internal/ads/zzfhh;)Lcom/google/android/gms/internal/ads/zzdaf;
    .locals 0

    iput-object p1, p0, Lcom/google/android/gms/internal/ads/zzdaf;->zzb:Lcom/google/android/gms/internal/ads/zzfhh;

    return-object p0
.end method

.method public final zzj()Lcom/google/android/gms/internal/ads/zzdah;
    .locals 2

    new-instance v0, Lcom/google/android/gms/internal/ads/zzdah;

    const/4 v1, 0x1

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Lcom/google/android/gms/internal/ads/zzdah;-><init>(Lcom/google/android/gms/internal/ads/zzdaf;Lcom/google/android/gms/internal/ads/zzdag;)V

    return-object v0
.end method
