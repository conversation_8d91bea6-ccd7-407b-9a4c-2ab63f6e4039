.class abstract Lcom/bumptech/glide/repackaged/com/google/common/collect/ImmutableSortedSetFauxverideShim;
.super Lcom/bumptech/glide/repackaged/com/google/common/collect/ImmutableSet;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<E:",
        "Ljava/lang/Object;",
        ">",
        "Lcom/bumptech/glide/repackaged/com/google/common/collect/ImmutableSet<",
        "TE;>;"
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/bumptech/glide/repackaged/com/google/common/collect/ImmutableSet;-><init>()V

    return-void
.end method
