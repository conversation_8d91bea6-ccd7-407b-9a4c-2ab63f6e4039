.class public final Landroidx/media3/datasource/cronet/R$color;
.super Ljava/lang/Object;


# static fields
.field public static common_google_signin_btn_text_dark:I = 0x7f06017c

.field public static common_google_signin_btn_text_dark_default:I = 0x7f06017d

.field public static common_google_signin_btn_text_dark_disabled:I = 0x7f06017e

.field public static common_google_signin_btn_text_dark_focused:I = 0x7f06017f

.field public static common_google_signin_btn_text_dark_pressed:I = 0x7f060180

.field public static common_google_signin_btn_text_light:I = 0x7f060181

.field public static common_google_signin_btn_text_light_default:I = 0x7f060182

.field public static common_google_signin_btn_text_light_disabled:I = 0x7f060183

.field public static common_google_signin_btn_text_light_focused:I = 0x7f060184

.field public static common_google_signin_btn_text_light_pressed:I = 0x7f060185

.field public static common_google_signin_btn_tint:I = 0x7f060186

.field public static notification_action_color_filter:I = 0x7f060459

.field public static notification_icon_bg_color:I = 0x7f06045b

.field public static ripple_material_light:I = 0x7f0605ef

.field public static secondary_text_default_material_light:I = 0x7f0605f2


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
