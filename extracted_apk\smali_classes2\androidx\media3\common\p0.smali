.class public Landroidx/media3/common/p0;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/common/p0$c;,
        Landroidx/media3/common/p0$b;
    }
.end annotation


# static fields
.field public static final C:Landroidx/media3/common/p0;

.field public static final D:Landroidx/media3/common/p0;
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final E:Ljava/lang/String;

.field public static final F:Ljava/lang/String;

.field public static final G:Ljava/lang/String;

.field public static final H:Ljava/lang/String;

.field public static final I:Ljava/lang/String;

.field public static final J:Ljava/lang/String;

.field public static final K:Ljava/lang/String;

.field public static final L:Ljava/lang/String;

.field public static final M:Ljava/lang/String;

.field public static final N:Ljava/lang/String;

.field public static final O:Ljava/lang/String;

.field public static final P:Ljava/lang/String;

.field public static final Q:Ljava/lang/String;

.field public static final R:Ljava/lang/String;

.field public static final S:Ljava/lang/String;

.field public static final T:Ljava/lang/String;

.field public static final U:Ljava/lang/String;

.field public static final V:Ljava/lang/String;

.field public static final W:Ljava/lang/String;

.field public static final X:Ljava/lang/String;

.field public static final Y:Ljava/lang/String;

.field public static final Z:Ljava/lang/String;

.field public static final a0:Ljava/lang/String;

.field public static final b0:Ljava/lang/String;

.field public static final c0:Ljava/lang/String;

.field public static final d0:Ljava/lang/String;

.field public static final e0:Ljava/lang/String;

.field public static final f0:Ljava/lang/String;

.field public static final g0:Ljava/lang/String;

.field public static final h0:Ljava/lang/String;

.field public static final i0:Ljava/lang/String;

.field public static final j0:Landroidx/media3/common/i;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/media3/common/i<",
            "Landroidx/media3/common/p0;",
            ">;"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field


# instance fields
.field public final A:Lcom/google/common/collect/ImmutableMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/common/collect/ImmutableMap<",
            "Landroidx/media3/common/n0;",
            "Landroidx/media3/common/o0;",
            ">;"
        }
    .end annotation
.end field

.field public final B:Lcom/google/common/collect/ImmutableSet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/common/collect/ImmutableSet<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public final a:I

.field public final b:I

.field public final c:I

.field public final d:I

.field public final e:I

.field public final f:I

.field public final g:I

.field public final h:I

.field public final i:I

.field public final j:I

.field public final k:Z

.field public final l:Lcom/google/common/collect/ImmutableList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/common/collect/ImmutableList<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public final m:I

.field public final n:Lcom/google/common/collect/ImmutableList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/common/collect/ImmutableList<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public final o:I

.field public final p:I

.field public final q:I

.field public final r:Lcom/google/common/collect/ImmutableList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/common/collect/ImmutableList<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public final s:Landroidx/media3/common/p0$b;

.field public final t:Lcom/google/common/collect/ImmutableList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/common/collect/ImmutableList<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public final u:I

.field public final v:I

.field public final w:Z

.field public final x:Z

.field public final y:Z

.field public final z:Z


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/media3/common/p0$c;

    invoke-direct {v0}, Landroidx/media3/common/p0$c;-><init>()V

    invoke-virtual {v0}, Landroidx/media3/common/p0$c;->C()Landroidx/media3/common/p0;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->C:Landroidx/media3/common/p0;

    sput-object v0, Landroidx/media3/common/p0;->D:Landroidx/media3/common/p0;

    const/4 v0, 0x1

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->E:Ljava/lang/String;

    const/4 v0, 0x2

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->F:Ljava/lang/String;

    const/4 v0, 0x3

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->G:Ljava/lang/String;

    const/4 v0, 0x4

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->H:Ljava/lang/String;

    const/4 v0, 0x5

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->I:Ljava/lang/String;

    const/4 v0, 0x6

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->J:Ljava/lang/String;

    const/4 v0, 0x7

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->K:Ljava/lang/String;

    const/16 v0, 0x8

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->L:Ljava/lang/String;

    const/16 v0, 0x9

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->M:Ljava/lang/String;

    const/16 v0, 0xa

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->N:Ljava/lang/String;

    const/16 v0, 0xb

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->O:Ljava/lang/String;

    const/16 v0, 0xc

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->P:Ljava/lang/String;

    const/16 v0, 0xd

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->Q:Ljava/lang/String;

    const/16 v0, 0xe

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->R:Ljava/lang/String;

    const/16 v0, 0xf

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->S:Ljava/lang/String;

    const/16 v0, 0x10

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->T:Ljava/lang/String;

    const/16 v0, 0x11

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->U:Ljava/lang/String;

    const/16 v0, 0x12

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->V:Ljava/lang/String;

    const/16 v0, 0x13

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->W:Ljava/lang/String;

    const/16 v0, 0x14

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->X:Ljava/lang/String;

    const/16 v0, 0x15

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->Y:Ljava/lang/String;

    const/16 v0, 0x16

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->Z:Ljava/lang/String;

    const/16 v0, 0x17

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->a0:Ljava/lang/String;

    const/16 v0, 0x18

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->b0:Ljava/lang/String;

    const/16 v0, 0x19

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->c0:Ljava/lang/String;

    const/16 v0, 0x1a

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->d0:Ljava/lang/String;

    const/16 v0, 0x1b

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->e0:Ljava/lang/String;

    const/16 v0, 0x1c

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->f0:Ljava/lang/String;

    const/16 v0, 0x1d

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->g0:Ljava/lang/String;

    const/16 v0, 0x1e

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->h0:Ljava/lang/String;

    const/16 v0, 0x1f

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/p0;->i0:Ljava/lang/String;

    new-instance v0, Landroidx/media3/common/b;

    invoke-direct {v0}, Landroidx/media3/common/b;-><init>()V

    sput-object v0, Landroidx/media3/common/p0;->j0:Landroidx/media3/common/i;

    return-void
.end method

.method public constructor <init>(Landroidx/media3/common/p0$c;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p1}, Landroidx/media3/common/p0$c;->a(Landroidx/media3/common/p0$c;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/p0;->a:I

    invoke-static {p1}, Landroidx/media3/common/p0$c;->b(Landroidx/media3/common/p0$c;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/p0;->b:I

    invoke-static {p1}, Landroidx/media3/common/p0$c;->c(Landroidx/media3/common/p0$c;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/p0;->c:I

    invoke-static {p1}, Landroidx/media3/common/p0$c;->d(Landroidx/media3/common/p0$c;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/p0;->d:I

    invoke-static {p1}, Landroidx/media3/common/p0$c;->e(Landroidx/media3/common/p0$c;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/p0;->e:I

    invoke-static {p1}, Landroidx/media3/common/p0$c;->f(Landroidx/media3/common/p0$c;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/p0;->f:I

    invoke-static {p1}, Landroidx/media3/common/p0$c;->g(Landroidx/media3/common/p0$c;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/p0;->g:I

    invoke-static {p1}, Landroidx/media3/common/p0$c;->h(Landroidx/media3/common/p0$c;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/p0;->h:I

    invoke-static {p1}, Landroidx/media3/common/p0$c;->i(Landroidx/media3/common/p0$c;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/p0;->i:I

    invoke-static {p1}, Landroidx/media3/common/p0$c;->j(Landroidx/media3/common/p0$c;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/p0;->j:I

    invoke-static {p1}, Landroidx/media3/common/p0$c;->k(Landroidx/media3/common/p0$c;)Z

    move-result v0

    iput-boolean v0, p0, Landroidx/media3/common/p0;->k:Z

    invoke-static {p1}, Landroidx/media3/common/p0$c;->l(Landroidx/media3/common/p0$c;)Lcom/google/common/collect/ImmutableList;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/p0;->l:Lcom/google/common/collect/ImmutableList;

    invoke-static {p1}, Landroidx/media3/common/p0$c;->m(Landroidx/media3/common/p0$c;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/p0;->m:I

    invoke-static {p1}, Landroidx/media3/common/p0$c;->n(Landroidx/media3/common/p0$c;)Lcom/google/common/collect/ImmutableList;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/p0;->n:Lcom/google/common/collect/ImmutableList;

    invoke-static {p1}, Landroidx/media3/common/p0$c;->o(Landroidx/media3/common/p0$c;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/p0;->o:I

    invoke-static {p1}, Landroidx/media3/common/p0$c;->p(Landroidx/media3/common/p0$c;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/p0;->p:I

    invoke-static {p1}, Landroidx/media3/common/p0$c;->q(Landroidx/media3/common/p0$c;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/p0;->q:I

    invoke-static {p1}, Landroidx/media3/common/p0$c;->r(Landroidx/media3/common/p0$c;)Lcom/google/common/collect/ImmutableList;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/p0;->r:Lcom/google/common/collect/ImmutableList;

    invoke-static {p1}, Landroidx/media3/common/p0$c;->s(Landroidx/media3/common/p0$c;)Landroidx/media3/common/p0$b;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/p0;->s:Landroidx/media3/common/p0$b;

    invoke-static {p1}, Landroidx/media3/common/p0$c;->t(Landroidx/media3/common/p0$c;)Lcom/google/common/collect/ImmutableList;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/p0;->t:Lcom/google/common/collect/ImmutableList;

    invoke-static {p1}, Landroidx/media3/common/p0$c;->u(Landroidx/media3/common/p0$c;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/p0;->u:I

    invoke-static {p1}, Landroidx/media3/common/p0$c;->v(Landroidx/media3/common/p0$c;)I

    move-result v0

    iput v0, p0, Landroidx/media3/common/p0;->v:I

    invoke-static {p1}, Landroidx/media3/common/p0$c;->w(Landroidx/media3/common/p0$c;)Z

    move-result v0

    iput-boolean v0, p0, Landroidx/media3/common/p0;->w:Z

    invoke-static {p1}, Landroidx/media3/common/p0$c;->x(Landroidx/media3/common/p0$c;)Z

    move-result v0

    iput-boolean v0, p0, Landroidx/media3/common/p0;->x:Z

    invoke-static {p1}, Landroidx/media3/common/p0$c;->y(Landroidx/media3/common/p0$c;)Z

    move-result v0

    iput-boolean v0, p0, Landroidx/media3/common/p0;->y:Z

    invoke-static {p1}, Landroidx/media3/common/p0$c;->z(Landroidx/media3/common/p0$c;)Z

    move-result v0

    iput-boolean v0, p0, Landroidx/media3/common/p0;->z:Z

    invoke-static {p1}, Landroidx/media3/common/p0$c;->A(Landroidx/media3/common/p0$c;)Ljava/util/HashMap;

    move-result-object v0

    invoke-static {v0}, Lcom/google/common/collect/ImmutableMap;->copyOf(Ljava/util/Map;)Lcom/google/common/collect/ImmutableMap;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/p0;->A:Lcom/google/common/collect/ImmutableMap;

    invoke-static {p1}, Landroidx/media3/common/p0$c;->B(Landroidx/media3/common/p0$c;)Ljava/util/HashSet;

    move-result-object p1

    invoke-static {p1}, Lcom/google/common/collect/ImmutableSet;->copyOf(Ljava/util/Collection;)Lcom/google/common/collect/ImmutableSet;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/common/p0;->B:Lcom/google/common/collect/ImmutableSet;

    return-void
.end method


# virtual methods
.method public a()Landroidx/media3/common/p0$c;
    .locals 1

    new-instance v0, Landroidx/media3/common/p0$c;

    invoke-direct {v0, p0}, Landroidx/media3/common/p0$c;-><init>(Landroidx/media3/common/p0;)V

    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    const/4 v1, 0x1

    const/4 v1, 0x0

    if-eqz p1, :cond_3

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v2

    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v3

    if-eq v2, v3, :cond_1

    goto/16 :goto_1

    :cond_1
    check-cast p1, Landroidx/media3/common/p0;

    iget v2, p0, Landroidx/media3/common/p0;->a:I

    iget v3, p1, Landroidx/media3/common/p0;->a:I

    if-ne v2, v3, :cond_2

    iget v2, p0, Landroidx/media3/common/p0;->b:I

    iget v3, p1, Landroidx/media3/common/p0;->b:I

    if-ne v2, v3, :cond_2

    iget v2, p0, Landroidx/media3/common/p0;->c:I

    iget v3, p1, Landroidx/media3/common/p0;->c:I

    if-ne v2, v3, :cond_2

    iget v2, p0, Landroidx/media3/common/p0;->d:I

    iget v3, p1, Landroidx/media3/common/p0;->d:I

    if-ne v2, v3, :cond_2

    iget v2, p0, Landroidx/media3/common/p0;->e:I

    iget v3, p1, Landroidx/media3/common/p0;->e:I

    if-ne v2, v3, :cond_2

    iget v2, p0, Landroidx/media3/common/p0;->f:I

    iget v3, p1, Landroidx/media3/common/p0;->f:I

    if-ne v2, v3, :cond_2

    iget v2, p0, Landroidx/media3/common/p0;->g:I

    iget v3, p1, Landroidx/media3/common/p0;->g:I

    if-ne v2, v3, :cond_2

    iget v2, p0, Landroidx/media3/common/p0;->h:I

    iget v3, p1, Landroidx/media3/common/p0;->h:I

    if-ne v2, v3, :cond_2

    iget-boolean v2, p0, Landroidx/media3/common/p0;->k:Z

    iget-boolean v3, p1, Landroidx/media3/common/p0;->k:Z

    if-ne v2, v3, :cond_2

    iget v2, p0, Landroidx/media3/common/p0;->i:I

    iget v3, p1, Landroidx/media3/common/p0;->i:I

    if-ne v2, v3, :cond_2

    iget v2, p0, Landroidx/media3/common/p0;->j:I

    iget v3, p1, Landroidx/media3/common/p0;->j:I

    if-ne v2, v3, :cond_2

    iget-object v2, p0, Landroidx/media3/common/p0;->l:Lcom/google/common/collect/ImmutableList;

    iget-object v3, p1, Landroidx/media3/common/p0;->l:Lcom/google/common/collect/ImmutableList;

    invoke-virtual {v2, v3}, Lcom/google/common/collect/ImmutableList;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget v2, p0, Landroidx/media3/common/p0;->m:I

    iget v3, p1, Landroidx/media3/common/p0;->m:I

    if-ne v2, v3, :cond_2

    iget-object v2, p0, Landroidx/media3/common/p0;->n:Lcom/google/common/collect/ImmutableList;

    iget-object v3, p1, Landroidx/media3/common/p0;->n:Lcom/google/common/collect/ImmutableList;

    invoke-virtual {v2, v3}, Lcom/google/common/collect/ImmutableList;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget v2, p0, Landroidx/media3/common/p0;->o:I

    iget v3, p1, Landroidx/media3/common/p0;->o:I

    if-ne v2, v3, :cond_2

    iget v2, p0, Landroidx/media3/common/p0;->p:I

    iget v3, p1, Landroidx/media3/common/p0;->p:I

    if-ne v2, v3, :cond_2

    iget v2, p0, Landroidx/media3/common/p0;->q:I

    iget v3, p1, Landroidx/media3/common/p0;->q:I

    if-ne v2, v3, :cond_2

    iget-object v2, p0, Landroidx/media3/common/p0;->r:Lcom/google/common/collect/ImmutableList;

    iget-object v3, p1, Landroidx/media3/common/p0;->r:Lcom/google/common/collect/ImmutableList;

    invoke-virtual {v2, v3}, Lcom/google/common/collect/ImmutableList;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget-object v2, p0, Landroidx/media3/common/p0;->s:Landroidx/media3/common/p0$b;

    iget-object v3, p1, Landroidx/media3/common/p0;->s:Landroidx/media3/common/p0$b;

    invoke-virtual {v2, v3}, Landroidx/media3/common/p0$b;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget-object v2, p0, Landroidx/media3/common/p0;->t:Lcom/google/common/collect/ImmutableList;

    iget-object v3, p1, Landroidx/media3/common/p0;->t:Lcom/google/common/collect/ImmutableList;

    invoke-virtual {v2, v3}, Lcom/google/common/collect/ImmutableList;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget v2, p0, Landroidx/media3/common/p0;->u:I

    iget v3, p1, Landroidx/media3/common/p0;->u:I

    if-ne v2, v3, :cond_2

    iget v2, p0, Landroidx/media3/common/p0;->v:I

    iget v3, p1, Landroidx/media3/common/p0;->v:I

    if-ne v2, v3, :cond_2

    iget-boolean v2, p0, Landroidx/media3/common/p0;->w:Z

    iget-boolean v3, p1, Landroidx/media3/common/p0;->w:Z

    if-ne v2, v3, :cond_2

    iget-boolean v2, p0, Landroidx/media3/common/p0;->x:Z

    iget-boolean v3, p1, Landroidx/media3/common/p0;->x:Z

    if-ne v2, v3, :cond_2

    iget-boolean v2, p0, Landroidx/media3/common/p0;->y:Z

    iget-boolean v3, p1, Landroidx/media3/common/p0;->y:Z

    if-ne v2, v3, :cond_2

    iget-boolean v2, p0, Landroidx/media3/common/p0;->z:Z

    iget-boolean v3, p1, Landroidx/media3/common/p0;->z:Z

    if-ne v2, v3, :cond_2

    iget-object v2, p0, Landroidx/media3/common/p0;->A:Lcom/google/common/collect/ImmutableMap;

    iget-object v3, p1, Landroidx/media3/common/p0;->A:Lcom/google/common/collect/ImmutableMap;

    invoke-virtual {v2, v3}, Lcom/google/common/collect/ImmutableMap;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_2

    iget-object v2, p0, Landroidx/media3/common/p0;->B:Lcom/google/common/collect/ImmutableSet;

    iget-object p1, p1, Landroidx/media3/common/p0;->B:Lcom/google/common/collect/ImmutableSet;

    invoke-virtual {v2, p1}, Lcom/google/common/collect/ImmutableSet;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    return v0

    :cond_3
    :goto_1
    return v1
.end method

.method public hashCode()I
    .locals 3

    iget v0, p0, Landroidx/media3/common/p0;->a:I

    const/16 v1, 0x1f

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget v2, p0, Landroidx/media3/common/p0;->b:I

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget v2, p0, Landroidx/media3/common/p0;->c:I

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget v2, p0, Landroidx/media3/common/p0;->d:I

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget v2, p0, Landroidx/media3/common/p0;->e:I

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget v2, p0, Landroidx/media3/common/p0;->f:I

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget v2, p0, Landroidx/media3/common/p0;->g:I

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget v2, p0, Landroidx/media3/common/p0;->h:I

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget-boolean v2, p0, Landroidx/media3/common/p0;->k:Z

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget v2, p0, Landroidx/media3/common/p0;->i:I

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget v2, p0, Landroidx/media3/common/p0;->j:I

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget-object v2, p0, Landroidx/media3/common/p0;->l:Lcom/google/common/collect/ImmutableList;

    invoke-virtual {v2}, Lcom/google/common/collect/ImmutableList;->hashCode()I

    move-result v2

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget v2, p0, Landroidx/media3/common/p0;->m:I

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget-object v2, p0, Landroidx/media3/common/p0;->n:Lcom/google/common/collect/ImmutableList;

    invoke-virtual {v2}, Lcom/google/common/collect/ImmutableList;->hashCode()I

    move-result v2

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget v2, p0, Landroidx/media3/common/p0;->o:I

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget v2, p0, Landroidx/media3/common/p0;->p:I

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget v2, p0, Landroidx/media3/common/p0;->q:I

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget-object v2, p0, Landroidx/media3/common/p0;->r:Lcom/google/common/collect/ImmutableList;

    invoke-virtual {v2}, Lcom/google/common/collect/ImmutableList;->hashCode()I

    move-result v2

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget-object v2, p0, Landroidx/media3/common/p0;->s:Landroidx/media3/common/p0$b;

    invoke-virtual {v2}, Landroidx/media3/common/p0$b;->hashCode()I

    move-result v2

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget-object v2, p0, Landroidx/media3/common/p0;->t:Lcom/google/common/collect/ImmutableList;

    invoke-virtual {v2}, Lcom/google/common/collect/ImmutableList;->hashCode()I

    move-result v2

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget v2, p0, Landroidx/media3/common/p0;->u:I

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget v2, p0, Landroidx/media3/common/p0;->v:I

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget-boolean v2, p0, Landroidx/media3/common/p0;->w:Z

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget-boolean v2, p0, Landroidx/media3/common/p0;->x:Z

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget-boolean v2, p0, Landroidx/media3/common/p0;->y:Z

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget-boolean v2, p0, Landroidx/media3/common/p0;->z:Z

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget-object v2, p0, Landroidx/media3/common/p0;->A:Lcom/google/common/collect/ImmutableMap;

    invoke-virtual {v2}, Lcom/google/common/collect/ImmutableMap;->hashCode()I

    move-result v2

    add-int/2addr v0, v2

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Landroidx/media3/common/p0;->B:Lcom/google/common/collect/ImmutableSet;

    invoke-virtual {v1}, Lcom/google/common/collect/ImmutableSet;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method
