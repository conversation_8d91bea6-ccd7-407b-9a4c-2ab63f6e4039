.class public Lcom/google/android/gms/internal/ads/zzcwh;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/android/gms/internal/ads/zzemi;


# instance fields
.field protected final zza:Lcom/google/android/gms/internal/ads/zzfgy;

.field protected final zzb:Lcom/google/android/gms/internal/ads/zzfgm;

.field private final zzc:Lcom/google/android/gms/internal/ads/zzdbw;

.field private final zzd:Lcom/google/android/gms/internal/ads/zzdcj;

.field private final zze:Lcom/google/android/gms/internal/ads/zzfea;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field private final zzf:Lcom/google/android/gms/internal/ads/zzdaq;

.field private final zzg:Lcom/google/android/gms/internal/ads/zzdfe;

.field private final zzh:Lcom/google/android/gms/internal/ads/zzdcn;

.field private final zzi:Lcom/google/android/gms/internal/ads/zzdij;


# direct methods
.method public constructor <init>(Lcom/google/android/gms/internal/ads/zzcwg;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p1}, Lcom/google/android/gms/internal/ads/zzcwg;->zzi(Lcom/google/android/gms/internal/ads/zzcwg;)Lcom/google/android/gms/internal/ads/zzfgy;

    move-result-object v0

    iput-object v0, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zza:Lcom/google/android/gms/internal/ads/zzfgy;

    invoke-static {p1}, Lcom/google/android/gms/internal/ads/zzcwg;->zzh(Lcom/google/android/gms/internal/ads/zzcwg;)Lcom/google/android/gms/internal/ads/zzfgm;

    move-result-object v0

    iput-object v0, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zzb:Lcom/google/android/gms/internal/ads/zzfgm;

    invoke-static {p1}, Lcom/google/android/gms/internal/ads/zzcwg;->zzb(Lcom/google/android/gms/internal/ads/zzcwg;)Lcom/google/android/gms/internal/ads/zzdbw;

    move-result-object v0

    iput-object v0, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zzc:Lcom/google/android/gms/internal/ads/zzdbw;

    invoke-static {p1}, Lcom/google/android/gms/internal/ads/zzcwg;->zzc(Lcom/google/android/gms/internal/ads/zzcwg;)Lcom/google/android/gms/internal/ads/zzdcj;

    move-result-object v0

    iput-object v0, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zzd:Lcom/google/android/gms/internal/ads/zzdcj;

    invoke-static {p1}, Lcom/google/android/gms/internal/ads/zzcwg;->zzg(Lcom/google/android/gms/internal/ads/zzcwg;)Lcom/google/android/gms/internal/ads/zzfea;

    move-result-object v0

    iput-object v0, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zze:Lcom/google/android/gms/internal/ads/zzfea;

    invoke-static {p1}, Lcom/google/android/gms/internal/ads/zzcwg;->zza(Lcom/google/android/gms/internal/ads/zzcwg;)Lcom/google/android/gms/internal/ads/zzdaq;

    move-result-object v0

    iput-object v0, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zzf:Lcom/google/android/gms/internal/ads/zzdaq;

    invoke-static {p1}, Lcom/google/android/gms/internal/ads/zzcwg;->zze(Lcom/google/android/gms/internal/ads/zzcwg;)Lcom/google/android/gms/internal/ads/zzdfe;

    move-result-object v0

    iput-object v0, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zzg:Lcom/google/android/gms/internal/ads/zzdfe;

    invoke-static {p1}, Lcom/google/android/gms/internal/ads/zzcwg;->zzd(Lcom/google/android/gms/internal/ads/zzcwg;)Lcom/google/android/gms/internal/ads/zzdcn;

    move-result-object v0

    iput-object v0, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zzh:Lcom/google/android/gms/internal/ads/zzdcn;

    invoke-static {p1}, Lcom/google/android/gms/internal/ads/zzcwg;->zzf(Lcom/google/android/gms/internal/ads/zzcwg;)Lcom/google/android/gms/internal/ads/zzdij;

    move-result-object p1

    iput-object p1, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zzi:Lcom/google/android/gms/internal/ads/zzdij;

    return-void
.end method


# virtual methods
.method public zzb()V
    .locals 2

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zzc:Lcom/google/android/gms/internal/ads/zzdbw;

    const/4 v1, 0x1

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/google/android/gms/internal/ads/zzdbw;->zza(Landroid/content/Context;)V

    return-void
.end method

.method public zzj()V
    .locals 1

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zzd:Lcom/google/android/gms/internal/ads/zzdcj;

    invoke-virtual {v0}, Lcom/google/android/gms/internal/ads/zzdcj;->zzr()V

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zzh:Lcom/google/android/gms/internal/ads/zzdcn;

    invoke-virtual {v0, p0}, Lcom/google/android/gms/internal/ads/zzdcn;->zza(Lcom/google/android/gms/internal/ads/zzcwh;)V

    return-void
.end method

.method public final zzl()Lcom/google/android/gms/internal/ads/zzdaq;
    .locals 1

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zzf:Lcom/google/android/gms/internal/ads/zzdaq;

    return-object v0
.end method

.method public final zzm()Lcom/google/android/gms/internal/ads/zzdbw;
    .locals 1

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zzc:Lcom/google/android/gms/internal/ads/zzdbw;

    return-object v0
.end method

.method public final zzn()Lcom/google/android/gms/internal/ads/zzdfc;
    .locals 1

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zzg:Lcom/google/android/gms/internal/ads/zzdfe;

    invoke-virtual {v0}, Lcom/google/android/gms/internal/ads/zzdfe;->zzi()Lcom/google/android/gms/internal/ads/zzdfc;

    move-result-object v0

    return-object v0
.end method

.method public final zzo()Lcom/google/android/gms/internal/ads/zzfea;
    .locals 1
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zze:Lcom/google/android/gms/internal/ads/zzfea;

    return-object v0
.end method

.method public final zzp()Lcom/google/android/gms/internal/ads/zzfgy;
    .locals 1

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zza:Lcom/google/android/gms/internal/ads/zzfgy;

    return-object v0
.end method

.method public final zzq()V
    .locals 1

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzcwh;->zzi:Lcom/google/android/gms/internal/ads/zzdij;

    invoke-virtual {v0}, Lcom/google/android/gms/internal/ads/zzdij;->zzs()V

    return-void
.end method
