<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingStart="16.0dip" android:paddingEnd="16.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivCoverBlur" android:layout_width="72.0dip" android:layout_height="96.0dip" android:layout_marginBottom="16.0dip" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:shapeAppearance="@style/corner_style_4" />
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/ivCover" android:layout_width="0.0dip" android:layout_height="0.0dip" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="@id/ivCoverBlur" app:layout_constraintEnd_toEndOf="@id/ivCoverBlur" app:layout_constraintStart_toStartOf="@id/ivCoverBlur" app:layout_constraintTop_toTopOf="@id/ivCoverBlur" app:shapeAppearance="@style/corner_style_4" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/tvSubject" android:visibility="visible" android:layout_width="0.0dip" android:maxLines="1" android:textAlignment="viewStart" android:layout_marginStart="8.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/ivCover" app:layout_constraintTop_toTopOf="@id/ivCover" style="@style/style_medium_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_03" android:ellipsize="end" android:gravity="start|center" android:id="@id/tvSubjectYear" android:layout_width="0.0dip" android:layout_marginTop="6.0dip" android:maxLines="2" android:drawablePadding="4.0dip" android:textAlignment="viewStart" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/tvSubject" app:layout_constraintTop_toBottomOf="@id/tvSubject" style="@style/style_regula_bigger_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/yellow_dark_70" android:gravity="center" android:id="@id/tvSubjectScore" android:layout_height="0.0dip" android:drawablePadding="2.0dip" android:drawableStart="@drawable/ic_category_star" android:layout_marginStart="8.0dip" app:layout_constraintBottom_toBottomOf="@id/llDownload" app:layout_constraintStart_toEndOf="@id/ivCover" app:layout_constraintTop_toTopOf="@id/llDownload" style="@style/style_medium_text" />
    <com.transsnet.downloader.widget.DownloadView android:gravity="center_vertical" android:id="@id/llDownload" android:background="@drawable/bg_btn_01" android:layout_width="wrap_content" android:layout_height="28.0dip" android:minWidth="88.0dip" app:layout_constraintBottom_toBottomOf="@id/ivCover" app:layout_constraintEnd_toEndOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
