.class public final Landroidx/media3/exoplayer/hls/R$drawable;
.super Ljava/lang/Object;


# static fields
.field public static notification_action_background:I = 0x7f080481

.field public static notification_bg:I = 0x7f080482

.field public static notification_bg_low:I = 0x7f080483

.field public static notification_bg_low_normal:I = 0x7f080484

.field public static notification_bg_low_pressed:I = 0x7f080485

.field public static notification_bg_normal:I = 0x7f080486

.field public static notification_bg_normal_pressed:I = 0x7f080487

.field public static notification_icon_background:I = 0x7f080488

.field public static notification_template_icon_bg:I = 0x7f08048a

.field public static notification_template_icon_low_bg:I = 0x7f08048b

.field public static notification_tile_bg:I = 0x7f08048c

.field public static notify_panel_notification_icon_bg:I = 0x7f08048d


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
