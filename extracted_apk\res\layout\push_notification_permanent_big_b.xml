<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textSize="12.0sp" android:textStyle="bold" android:textColor="@color/new_text_05" android:id="@id/notification_title_tv" android:layout_width="fill_parent" android:layout_height="wrap_content" android:maxLines="2" android:paddingEnd="10.0dip" />
    <include android:layout_gravity="center_horizontal" android:id="@id/content_layout" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" app:layout_constraintTop_toBottomOf="@id/notification_title_tv" layout="@layout/layout_notification_four_item" />
</LinearLayout>
