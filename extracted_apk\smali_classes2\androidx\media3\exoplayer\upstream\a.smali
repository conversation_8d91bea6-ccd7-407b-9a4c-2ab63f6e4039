.class public final Landroidx/media3/exoplayer/upstream/a;
.super Ljava/lang/Object;


# instance fields
.field public final a:[B

.field public final b:I


# direct methods
.method public constructor <init>([BI)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/upstream/a;->a:[B

    iput p2, p0, Landroidx/media3/exoplayer/upstream/a;->b:I

    return-void
.end method
