<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <View android:id="@id/space" android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_weight="1.0" />
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="horizontal" android:background="@drawable/land_play_8_bg" android:layout_width="wrap_content" android:layout_height="fill_parent" android:layout_marginTop="12.0dip" android:layout_marginBottom="12.0dip" android:layout_marginEnd="12.0dip">
        <FrameLayout android:id="@id/fl_audio_container" android:layout_width="140.0dip" android:layout_height="fill_parent" />
        <FrameLayout android:id="@id/fl_subtitle_container" android:layout_width="210.0dip" android:layout_height="fill_parent" />
        <FrameLayout android:id="@id/flStyleContainer" android:visibility="gone" android:layout_width="272.0dip" android:layout_height="fill_parent" />
        <FrameLayout android:id="@id/flSearchContainer" android:visibility="gone" android:layout_width="360.0dip" android:layout_height="fill_parent" />
        <FrameLayout android:id="@id/flSyncAdjustContainer" android:visibility="gone" android:layout_width="200.0dip" android:layout_height="fill_parent" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</androidx.appcompat.widget.LinearLayoutCompat>
