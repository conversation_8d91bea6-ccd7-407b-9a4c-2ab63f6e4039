.class public final Landroidx/compose/ui/text/platform/o;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/compose/runtime/f3;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroidx/compose/runtime/f3<",
        "<PERSON>java/lang/<PERSON>;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
.end annotation


# instance fields
.field public final a:Z


# direct methods
.method public constructor <init>(Z)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-boolean p1, p0, Landroidx/compose/ui/text/platform/o;->a:Z

    return-void
.end method


# virtual methods
.method public b()Ljava/lang/Boolean;
    .locals 1

    iget-boolean v0, p0, Landroidx/compose/ui/text/platform/o;->a:Z

    invoke-static {v0}, <PERSON>java/lang/Bo<PERSON>an;->valueOf(Z)Ljava/lang/<PERSON>;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic getValue()Ljava/lang/Object;
    .locals 1

    invoke-virtual {p0}, Landroidx/compose/ui/text/platform/o;->b()Ljava/lang/Boolean;

    move-result-object v0

    return-object v0
.end method
