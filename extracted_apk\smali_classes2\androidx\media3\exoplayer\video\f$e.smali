.class public final Landroidx/media3/exoplayer/video/f$e;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/video/VideoSink;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/video/f;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "e"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/video/f$e$a;
    }
.end annotation


# instance fields
.field public final a:Landroid/content/Context;

.field public final b:Landroidx/media3/exoplayer/video/f;

.field public final c:I

.field public final d:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Landroidx/media3/common/p;",
            ">;"
        }
    .end annotation
.end field

.field public e:Landroidx/media3/common/p;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public f:Landroidx/media3/common/y;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public g:I

.field public h:J

.field public i:Z

.field public j:J

.field public k:J

.field public l:Z

.field public m:J


# direct methods
.method public constructor <init>(Landroid/content/Context;Landroidx/media3/exoplayer/video/f;Landroidx/media3/common/j0;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/common/VideoFrameProcessingException;
        }
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/f$e;->a:Landroid/content/Context;

    iput-object p2, p0, Landroidx/media3/exoplayer/video/f$e;->b:Landroidx/media3/exoplayer/video/f;

    invoke-static {p1}, Le2/u0;->g0(Landroid/content/Context;)I

    move-result p1

    iput p1, p0, Landroidx/media3/exoplayer/video/f$e;->c:I

    invoke-interface {p3}, Landroidx/media3/common/s0;->d()I

    move-result p1

    invoke-interface {p3, p1}, Landroidx/media3/common/s0;->a(I)Landroidx/media3/common/r0;

    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/f$e;->d:Ljava/util/ArrayList;

    const-wide p1, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide p1, p0, Landroidx/media3/exoplayer/video/f$e;->j:J

    iput-wide p1, p0, Landroidx/media3/exoplayer/video/f$e;->k:J

    return-void
.end method


# virtual methods
.method public a(JZ)J
    .locals 2

    iget p1, p0, Landroidx/media3/exoplayer/video/f$e;->c:I

    const/4 p2, -0x1

    if-eq p1, p2, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x1

    const/4 p1, 0x0

    :goto_0
    invoke-static {p1}, Le2/a;->g(Z)V

    iget-wide p1, p0, Landroidx/media3/exoplayer/video/f$e;->m:J

    const-wide v0, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long p3, p1, v0

    if-eqz p3, :cond_2

    iget-object p3, p0, Landroidx/media3/exoplayer/video/f$e;->b:Landroidx/media3/exoplayer/video/f;

    invoke-static {p3, p1, p2}, Landroidx/media3/exoplayer/video/f;->s(Landroidx/media3/exoplayer/video/f;J)Z

    move-result p1

    if-nez p1, :cond_1

    return-wide v0

    :cond_1
    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/f$e;->f()V

    iput-wide v0, p0, Landroidx/media3/exoplayer/video/f$e;->m:J

    :cond_2
    const/4 p1, 0x1

    const/4 p1, 0x0

    throw p1
.end method

.method public b(ILandroidx/media3/common/y;)V
    .locals 4

    const/4 v0, 0x1

    if-eq p1, v0, :cond_1

    const/4 v1, 0x2

    if-ne p1, v1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p2, Ljava/lang/UnsupportedOperationException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Unsupported input type "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p2, p1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p2

    :cond_1
    :goto_0
    if-ne p1, v0, :cond_3

    sget v1, Le2/u0;->a:I

    const/16 v2, 0x15

    if-ge v1, v2, :cond_3

    iget v1, p2, Landroidx/media3/common/y;->u:I

    const/4 v2, -0x1

    if-eq v1, v2, :cond_3

    if-eqz v1, :cond_3

    iget-object v2, p0, Landroidx/media3/exoplayer/video/f$e;->e:Landroidx/media3/common/p;

    if-eqz v2, :cond_2

    iget-object v2, p0, Landroidx/media3/exoplayer/video/f$e;->f:Landroidx/media3/common/y;

    if-eqz v2, :cond_2

    iget v2, v2, Landroidx/media3/common/y;->u:I

    if-eq v2, v1, :cond_4

    :cond_2
    int-to-float v1, v1

    invoke-static {v1}, Landroidx/media3/exoplayer/video/f$e$a;->a(F)Landroidx/media3/common/p;

    move-result-object v1

    iput-object v1, p0, Landroidx/media3/exoplayer/video/f$e;->e:Landroidx/media3/common/p;

    goto :goto_1

    :cond_3
    const/4 v1, 0x1

    const/4 v1, 0x0

    iput-object v1, p0, Landroidx/media3/exoplayer/video/f$e;->e:Landroidx/media3/common/p;

    :cond_4
    :goto_1
    iput p1, p0, Landroidx/media3/exoplayer/video/f$e;->g:I

    iput-object p2, p0, Landroidx/media3/exoplayer/video/f$e;->f:Landroidx/media3/common/y;

    iget-boolean p1, p0, Landroidx/media3/exoplayer/video/f$e;->l:Z

    const-wide v1, -0x7fffffffffffffffL    # -4.9E-324

    if-nez p1, :cond_5

    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/f$e;->f()V

    iput-boolean v0, p0, Landroidx/media3/exoplayer/video/f$e;->l:Z

    iput-wide v1, p0, Landroidx/media3/exoplayer/video/f$e;->m:J

    goto :goto_3

    :cond_5
    iget-wide p1, p0, Landroidx/media3/exoplayer/video/f$e;->k:J

    cmp-long v3, p1, v1

    if-eqz v3, :cond_6

    goto :goto_2

    :cond_6
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_2
    invoke-static {v0}, Le2/a;->g(Z)V

    iget-wide p1, p0, Landroidx/media3/exoplayer/video/f$e;->k:J

    iput-wide p1, p0, Landroidx/media3/exoplayer/video/f$e;->m:J

    :goto_3
    return-void
.end method

.method public c()Z
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f$e;->a:Landroid/content/Context;

    invoke-static {v0}, Le2/u0;->J0(Landroid/content/Context;)Z

    move-result v0

    return v0
.end method

.method public d()Landroid/view/Surface;
    .locals 1

    const/4 v0, 0x1

    const/4 v0, 0x0

    throw v0
.end method

.method public e(Landroidx/media3/exoplayer/video/VideoSink$a;Ljava/util/concurrent/Executor;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f$e;->b:Landroidx/media3/exoplayer/video/f;

    invoke-static {v0, p1, p2}, Landroidx/media3/exoplayer/video/f;->t(Landroidx/media3/exoplayer/video/f;Landroidx/media3/exoplayer/video/VideoSink$a;Ljava/util/concurrent/Executor;)V

    return-void
.end method

.method public final f()V
    .locals 5

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f$e;->f:Landroidx/media3/common/y;

    if-nez v0, :cond_0

    return-void

    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iget-object v1, p0, Landroidx/media3/exoplayer/video/f$e;->e:Landroidx/media3/common/p;

    if-eqz v1, :cond_1

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_1
    iget-object v1, p0, Landroidx/media3/exoplayer/video/f$e;->d:Ljava/util/ArrayList;

    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f$e;->f:Landroidx/media3/common/y;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/common/y;

    new-instance v1, Landroidx/media3/common/z$b;

    iget-object v2, v0, Landroidx/media3/common/y;->y:Landroidx/media3/common/k;

    invoke-static {v2}, Landroidx/media3/exoplayer/video/f;->q(Landroidx/media3/common/k;)Landroidx/media3/common/k;

    move-result-object v2

    iget v3, v0, Landroidx/media3/common/y;->r:I

    iget v4, v0, Landroidx/media3/common/y;->s:I

    invoke-direct {v1, v2, v3, v4}, Landroidx/media3/common/z$b;-><init>(Landroidx/media3/common/k;II)V

    iget v0, v0, Landroidx/media3/common/y;->v:F

    invoke-virtual {v1, v0}, Landroidx/media3/common/z$b;->b(F)Landroidx/media3/common/z$b;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/z$b;->a()Landroidx/media3/common/z;

    const/4 v0, 0x1

    const/4 v0, 0x0

    throw v0
.end method

.method public flush()V
    .locals 1

    const/4 v0, 0x1

    const/4 v0, 0x0

    throw v0
.end method

.method public g(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/common/p;",
            ">;)V"
        }
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f$e;->d:Ljava/util/ArrayList;

    invoke-virtual {v0}, Ljava/util/ArrayList;->clear()V

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f$e;->d:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    return-void
.end method

.method public h(J)V
    .locals 3

    iget-wide v0, p0, Landroidx/media3/exoplayer/video/f$e;->h:J

    cmp-long v2, v0, p1

    if-eqz v2, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    iput-boolean v0, p0, Landroidx/media3/exoplayer/video/f$e;->i:Z

    iput-wide p1, p0, Landroidx/media3/exoplayer/video/f$e;->h:J

    return-void
.end method

.method public i(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/common/p;",
            ">;)V"
        }
    .end annotation

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/video/f$e;->g(Ljava/util/List;)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/f$e;->f()V

    return-void
.end method

.method public isEnded()Z
    .locals 5

    iget-wide v0, p0, Landroidx/media3/exoplayer/video/f$e;->j:J

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v4, v0, v2

    if-eqz v4, :cond_0

    iget-object v2, p0, Landroidx/media3/exoplayer/video/f$e;->b:Landroidx/media3/exoplayer/video/f;

    invoke-static {v2, v0, v1}, Landroidx/media3/exoplayer/video/f;->s(Landroidx/media3/exoplayer/video/f;J)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public isReady()Z
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f$e;->b:Landroidx/media3/exoplayer/video/f;

    invoke-static {v0}, Landroidx/media3/exoplayer/video/f;->r(Landroidx/media3/exoplayer/video/f;)Z

    move-result v0

    return v0
.end method

.method public render(JJ)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/video/VideoSink$VideoSinkException;
        }
    .end annotation

    :try_start_0
    iget-object v0, p0, Landroidx/media3/exoplayer/video/f$e;->b:Landroidx/media3/exoplayer/video/f;

    invoke-virtual {v0, p1, p2, p3, p4}, Landroidx/media3/exoplayer/video/f;->D(JJ)V
    :try_end_0
    .catch Landroidx/media3/exoplayer/ExoPlaybackException; {:try_start_0 .. :try_end_0} :catch_0

    return-void

    :catch_0
    move-exception p1

    new-instance p2, Landroidx/media3/exoplayer/video/VideoSink$VideoSinkException;

    iget-object p3, p0, Landroidx/media3/exoplayer/video/f$e;->f:Landroidx/media3/common/y;

    if-eqz p3, :cond_0

    goto :goto_0

    :cond_0
    new-instance p3, Landroidx/media3/common/y$b;

    invoke-direct {p3}, Landroidx/media3/common/y$b;-><init>()V

    invoke-virtual {p3}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object p3

    :goto_0
    invoke-direct {p2, p1, p3}, Landroidx/media3/exoplayer/video/VideoSink$VideoSinkException;-><init>(Ljava/lang/Throwable;Landroidx/media3/common/y;)V

    throw p2
.end method

.method public setPlaybackSpeed(F)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/f$e;->b:Landroidx/media3/exoplayer/video/f;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/video/f;->u(Landroidx/media3/exoplayer/video/f;F)V

    return-void
.end method
