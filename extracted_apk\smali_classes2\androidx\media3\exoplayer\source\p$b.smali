.class public final Landroidx/media3/exoplayer/source/p$b;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/upstream/Loader$d;
.implements Landroidx/media3/exoplayer/source/h$a;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/source/p;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "b"
.end annotation


# instance fields
.field public final a:J

.field public final b:Landroid/net/Uri;

.field public final c:Lh2/m;

.field public final d:Landroidx/media3/exoplayer/source/o;

.field public final e:Lz2/u;

.field public final f:Le2/g;

.field public final g:Lz2/l0;

.field public volatile h:Z

.field public i:Z

.field public j:J

.field public k:Lh2/g;

.field public l:Lz2/r0;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public m:Z

.field public final synthetic n:Landroidx/media3/exoplayer/source/p;


# direct methods
.method public constructor <init>(Landroidx/media3/exoplayer/source/p;Landroid/net/Uri;Landroidx/media3/datasource/a;Landroidx/media3/exoplayer/source/o;Lz2/u;Le2/g;)V
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/source/p$b;->n:Landroidx/media3/exoplayer/source/p;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p2, p0, Landroidx/media3/exoplayer/source/p$b;->b:Landroid/net/Uri;

    new-instance p1, Lh2/m;

    invoke-direct {p1, p3}, Lh2/m;-><init>(Landroidx/media3/datasource/a;)V

    iput-object p1, p0, Landroidx/media3/exoplayer/source/p$b;->c:Lh2/m;

    iput-object p4, p0, Landroidx/media3/exoplayer/source/p$b;->d:Landroidx/media3/exoplayer/source/o;

    iput-object p5, p0, Landroidx/media3/exoplayer/source/p$b;->e:Lz2/u;

    iput-object p6, p0, Landroidx/media3/exoplayer/source/p$b;->f:Le2/g;

    new-instance p1, Lz2/l0;

    invoke-direct {p1}, Lz2/l0;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/source/p$b;->g:Lz2/l0;

    const/4 p1, 0x1

    iput-boolean p1, p0, Landroidx/media3/exoplayer/source/p$b;->i:Z

    invoke-static {}, Lu2/n;->a()J

    move-result-wide p1

    iput-wide p1, p0, Landroidx/media3/exoplayer/source/p$b;->a:J

    const-wide/16 p1, 0x0

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/source/p$b;->g(J)Lh2/g;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/source/p$b;->k:Lh2/g;

    return-void
.end method

.method public static synthetic b(Landroidx/media3/exoplayer/source/p$b;)Lh2/m;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/source/p$b;->c:Lh2/m;

    return-object p0
.end method

.method public static synthetic c(Landroidx/media3/exoplayer/source/p$b;)J
    .locals 2

    iget-wide v0, p0, Landroidx/media3/exoplayer/source/p$b;->a:J

    return-wide v0
.end method

.method public static synthetic d(Landroidx/media3/exoplayer/source/p$b;)Lh2/g;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/source/p$b;->k:Lh2/g;

    return-object p0
.end method

.method public static synthetic e(Landroidx/media3/exoplayer/source/p$b;)J
    .locals 2

    iget-wide v0, p0, Landroidx/media3/exoplayer/source/p$b;->j:J

    return-wide v0
.end method

.method public static synthetic f(Landroidx/media3/exoplayer/source/p$b;JJ)V
    .locals 0

    invoke-virtual {p0, p1, p2, p3, p4}, Landroidx/media3/exoplayer/source/p$b;->h(JJ)V

    return-void
.end method


# virtual methods
.method public a(Le2/c0;)V
    .locals 11

    iget-boolean v0, p0, Landroidx/media3/exoplayer/source/p$b;->m:Z

    const/4 v1, 0x1

    if-nez v0, :cond_0

    iget-wide v2, p0, Landroidx/media3/exoplayer/source/p$b;->j:J

    :goto_0
    move-wide v5, v2

    goto :goto_1

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/source/p$b;->n:Landroidx/media3/exoplayer/source/p;

    invoke-static {v0, v1}, Landroidx/media3/exoplayer/source/p;->q(Landroidx/media3/exoplayer/source/p;Z)J

    move-result-wide v2

    iget-wide v4, p0, Landroidx/media3/exoplayer/source/p$b;->j:J

    invoke-static {v2, v3, v4, v5}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v2

    goto :goto_0

    :goto_1
    invoke-virtual {p1}, Le2/c0;->a()I

    move-result v8

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p$b;->l:Lz2/r0;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    move-object v4, v0

    check-cast v4, Lz2/r0;

    invoke-interface {v4, p1, v8}, Lz2/r0;->f(Le2/c0;I)V

    const/4 v7, 0x1

    const/4 v9, 0x1

    const/4 v9, 0x0

    const/4 v10, 0x1

    const/4 v10, 0x0

    invoke-interface/range {v4 .. v10}, Lz2/r0;->e(JIIILz2/r0$a;)V

    iput-boolean v1, p0, Landroidx/media3/exoplayer/source/p$b;->m:Z

    return-void
.end method

.method public cancelLoad()V
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/source/p$b;->h:Z

    return-void
.end method

.method public final g(J)Lh2/g;
    .locals 2

    new-instance v0, Lh2/g$b;

    invoke-direct {v0}, Lh2/g$b;-><init>()V

    iget-object v1, p0, Landroidx/media3/exoplayer/source/p$b;->b:Landroid/net/Uri;

    invoke-virtual {v0, v1}, Lh2/g$b;->i(Landroid/net/Uri;)Lh2/g$b;

    move-result-object v0

    invoke-virtual {v0, p1, p2}, Lh2/g$b;->h(J)Lh2/g$b;

    move-result-object p1

    iget-object p2, p0, Landroidx/media3/exoplayer/source/p$b;->n:Landroidx/media3/exoplayer/source/p;

    invoke-static {p2}, Landroidx/media3/exoplayer/source/p;->s(Landroidx/media3/exoplayer/source/p;)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Lh2/g$b;->f(Ljava/lang/String;)Lh2/g$b;

    move-result-object p1

    const/4 p2, 0x6

    invoke-virtual {p1, p2}, Lh2/g$b;->b(I)Lh2/g$b;

    move-result-object p1

    invoke-static {}, Landroidx/media3/exoplayer/source/p;->r()Ljava/util/Map;

    move-result-object p2

    invoke-virtual {p1, p2}, Lh2/g$b;->e(Ljava/util/Map;)Lh2/g$b;

    move-result-object p1

    invoke-virtual {p1}, Lh2/g$b;->a()Lh2/g;

    move-result-object p1

    return-object p1
.end method

.method public final h(JJ)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/source/p$b;->g:Lz2/l0;

    iput-wide p1, v0, Lz2/l0;->a:J

    iput-wide p3, p0, Landroidx/media3/exoplayer/source/p$b;->j:J

    const/4 p1, 0x1

    iput-boolean p1, p0, Landroidx/media3/exoplayer/source/p$b;->i:Z

    const/4 p1, 0x1

    const/4 p1, 0x0

    iput-boolean p1, p0, Landroidx/media3/exoplayer/source/p$b;->m:Z

    return-void
.end method

.method public load()V
    .locals 17
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    move-object/from16 v1, p0

    const/4 v0, 0x1

    const/4 v0, 0x0

    const/4 v2, 0x1

    const/4 v2, 0x0

    :goto_0
    if-nez v2, :cond_c

    iget-boolean v3, v1, Landroidx/media3/exoplayer/source/p$b;->h:Z

    if-nez v3, :cond_c

    const/4 v3, 0x1

    const-wide/16 v4, -0x1

    :try_start_0
    iget-object v6, v1, Landroidx/media3/exoplayer/source/p$b;->g:Lz2/l0;

    iget-wide v13, v6, Lz2/l0;->a:J

    invoke-virtual {v1, v13, v14}, Landroidx/media3/exoplayer/source/p$b;->g(J)Lh2/g;

    move-result-object v6

    iput-object v6, v1, Landroidx/media3/exoplayer/source/p$b;->k:Lh2/g;

    iget-object v7, v1, Landroidx/media3/exoplayer/source/p$b;->c:Lh2/m;

    invoke-virtual {v7, v6}, Lh2/m;->a(Lh2/g;)J

    move-result-wide v6

    iget-boolean v8, v1, Landroidx/media3/exoplayer/source/p$b;->h:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v8, :cond_2

    if-ne v2, v3, :cond_0

    goto :goto_1

    :cond_0
    iget-object v0, v1, Landroidx/media3/exoplayer/source/p$b;->d:Landroidx/media3/exoplayer/source/o;

    invoke-interface {v0}, Landroidx/media3/exoplayer/source/o;->b()J

    move-result-wide v2

    cmp-long v0, v2, v4

    if-eqz v0, :cond_1

    iget-object v0, v1, Landroidx/media3/exoplayer/source/p$b;->g:Lz2/l0;

    iget-object v2, v1, Landroidx/media3/exoplayer/source/p$b;->d:Landroidx/media3/exoplayer/source/o;

    invoke-interface {v2}, Landroidx/media3/exoplayer/source/o;->b()J

    move-result-wide v2

    iput-wide v2, v0, Lz2/l0;->a:J

    :cond_1
    :goto_1
    iget-object v0, v1, Landroidx/media3/exoplayer/source/p$b;->c:Lh2/m;

    invoke-static {v0}, Lh2/f;->a(Landroidx/media3/datasource/a;)V

    goto/16 :goto_6

    :cond_2
    cmp-long v8, v6, v4

    if-eqz v8, :cond_3

    add-long/2addr v6, v13

    :try_start_1
    iget-object v8, v1, Landroidx/media3/exoplayer/source/p$b;->n:Landroidx/media3/exoplayer/source/p;

    invoke-static {v8}, Landroidx/media3/exoplayer/source/p;->u(Landroidx/media3/exoplayer/source/p;)V

    :cond_3
    move-wide v15, v6

    goto :goto_2

    :catchall_0
    move-exception v0

    goto/16 :goto_5

    :goto_2
    iget-object v6, v1, Landroidx/media3/exoplayer/source/p$b;->n:Landroidx/media3/exoplayer/source/p;

    iget-object v7, v1, Landroidx/media3/exoplayer/source/p$b;->c:Lh2/m;

    invoke-virtual {v7}, Lh2/m;->getResponseHeaders()Ljava/util/Map;

    move-result-object v7

    invoke-static {v7}, Landroidx/media3/extractor/metadata/icy/IcyHeaders;->a(Ljava/util/Map;)Landroidx/media3/extractor/metadata/icy/IcyHeaders;

    move-result-object v7

    invoke-static {v6, v7}, Landroidx/media3/exoplayer/source/p;->w(Landroidx/media3/exoplayer/source/p;Landroidx/media3/extractor/metadata/icy/IcyHeaders;)Landroidx/media3/extractor/metadata/icy/IcyHeaders;

    iget-object v6, v1, Landroidx/media3/exoplayer/source/p$b;->c:Lh2/m;

    iget-object v7, v1, Landroidx/media3/exoplayer/source/p$b;->n:Landroidx/media3/exoplayer/source/p;

    invoke-static {v7}, Landroidx/media3/exoplayer/source/p;->v(Landroidx/media3/exoplayer/source/p;)Landroidx/media3/extractor/metadata/icy/IcyHeaders;

    move-result-object v7

    if-eqz v7, :cond_4

    iget-object v7, v1, Landroidx/media3/exoplayer/source/p$b;->n:Landroidx/media3/exoplayer/source/p;

    invoke-static {v7}, Landroidx/media3/exoplayer/source/p;->v(Landroidx/media3/exoplayer/source/p;)Landroidx/media3/extractor/metadata/icy/IcyHeaders;

    move-result-object v7

    iget v7, v7, Landroidx/media3/extractor/metadata/icy/IcyHeaders;->metadataInterval:I

    const/4 v8, -0x1

    if-eq v7, v8, :cond_4

    new-instance v6, Landroidx/media3/exoplayer/source/h;

    iget-object v7, v1, Landroidx/media3/exoplayer/source/p$b;->c:Lh2/m;

    iget-object v8, v1, Landroidx/media3/exoplayer/source/p$b;->n:Landroidx/media3/exoplayer/source/p;

    invoke-static {v8}, Landroidx/media3/exoplayer/source/p;->v(Landroidx/media3/exoplayer/source/p;)Landroidx/media3/extractor/metadata/icy/IcyHeaders;

    move-result-object v8

    iget v8, v8, Landroidx/media3/extractor/metadata/icy/IcyHeaders;->metadataInterval:I

    invoke-direct {v6, v7, v8, v1}, Landroidx/media3/exoplayer/source/h;-><init>(Landroidx/media3/datasource/a;ILandroidx/media3/exoplayer/source/h$a;)V

    iget-object v7, v1, Landroidx/media3/exoplayer/source/p$b;->n:Landroidx/media3/exoplayer/source/p;

    invoke-virtual {v7}, Landroidx/media3/exoplayer/source/p;->D()Lz2/r0;

    move-result-object v7

    iput-object v7, v1, Landroidx/media3/exoplayer/source/p$b;->l:Lz2/r0;

    invoke-static {}, Landroidx/media3/exoplayer/source/p;->x()Landroidx/media3/common/y;

    move-result-object v8

    invoke-interface {v7, v8}, Lz2/r0;->a(Landroidx/media3/common/y;)V

    :cond_4
    move-object v8, v6

    iget-object v7, v1, Landroidx/media3/exoplayer/source/p$b;->d:Landroidx/media3/exoplayer/source/o;

    iget-object v9, v1, Landroidx/media3/exoplayer/source/p$b;->b:Landroid/net/Uri;

    iget-object v6, v1, Landroidx/media3/exoplayer/source/p$b;->c:Lh2/m;

    invoke-virtual {v6}, Lh2/m;->getResponseHeaders()Ljava/util/Map;

    move-result-object v10

    iget-object v6, v1, Landroidx/media3/exoplayer/source/p$b;->e:Lz2/u;

    move-wide v11, v13

    move-wide v4, v13

    move-wide v13, v15

    move-object v15, v6

    invoke-interface/range {v7 .. v15}, Landroidx/media3/exoplayer/source/o;->d(Landroidx/media3/common/l;Landroid/net/Uri;Ljava/util/Map;JJLz2/u;)V

    iget-object v6, v1, Landroidx/media3/exoplayer/source/p$b;->n:Landroidx/media3/exoplayer/source/p;

    invoke-static {v6}, Landroidx/media3/exoplayer/source/p;->v(Landroidx/media3/exoplayer/source/p;)Landroidx/media3/extractor/metadata/icy/IcyHeaders;

    move-result-object v6

    if-eqz v6, :cond_5

    iget-object v6, v1, Landroidx/media3/exoplayer/source/p$b;->d:Landroidx/media3/exoplayer/source/o;

    invoke-interface {v6}, Landroidx/media3/exoplayer/source/o;->a()V

    :cond_5
    iget-boolean v6, v1, Landroidx/media3/exoplayer/source/p$b;->i:Z

    if-eqz v6, :cond_6

    iget-object v6, v1, Landroidx/media3/exoplayer/source/p$b;->d:Landroidx/media3/exoplayer/source/o;

    iget-wide v7, v1, Landroidx/media3/exoplayer/source/p$b;->j:J

    invoke-interface {v6, v4, v5, v7, v8}, Landroidx/media3/exoplayer/source/o;->seek(JJ)V

    iput-boolean v0, v1, Landroidx/media3/exoplayer/source/p$b;->i:Z

    :cond_6
    :goto_3
    move-wide v13, v4

    :cond_7
    if-nez v2, :cond_8

    iget-boolean v4, v1, Landroidx/media3/exoplayer/source/p$b;->h:Z
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-nez v4, :cond_8

    :try_start_2
    iget-object v4, v1, Landroidx/media3/exoplayer/source/p$b;->f:Le2/g;

    invoke-virtual {v4}, Le2/g;->a()V
    :try_end_2
    .catch Ljava/lang/InterruptedException; {:try_start_2 .. :try_end_2} :catch_0
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    :try_start_3
    iget-object v4, v1, Landroidx/media3/exoplayer/source/p$b;->d:Landroidx/media3/exoplayer/source/o;

    iget-object v5, v1, Landroidx/media3/exoplayer/source/p$b;->g:Lz2/l0;

    invoke-interface {v4, v5}, Landroidx/media3/exoplayer/source/o;->c(Lz2/l0;)I

    move-result v2

    iget-object v4, v1, Landroidx/media3/exoplayer/source/p$b;->d:Landroidx/media3/exoplayer/source/o;

    invoke-interface {v4}, Landroidx/media3/exoplayer/source/o;->b()J

    move-result-wide v4

    iget-object v6, v1, Landroidx/media3/exoplayer/source/p$b;->n:Landroidx/media3/exoplayer/source/p;

    invoke-static {v6}, Landroidx/media3/exoplayer/source/p;->l(Landroidx/media3/exoplayer/source/p;)J

    move-result-wide v6

    add-long/2addr v6, v13

    cmp-long v8, v4, v6

    if-lez v8, :cond_7

    iget-object v6, v1, Landroidx/media3/exoplayer/source/p$b;->f:Le2/g;

    invoke-virtual {v6}, Le2/g;->d()Z

    iget-object v6, v1, Landroidx/media3/exoplayer/source/p$b;->n:Landroidx/media3/exoplayer/source/p;

    invoke-static {v6}, Landroidx/media3/exoplayer/source/p;->p(Landroidx/media3/exoplayer/source/p;)Landroid/os/Handler;

    move-result-object v6

    iget-object v7, v1, Landroidx/media3/exoplayer/source/p$b;->n:Landroidx/media3/exoplayer/source/p;

    invoke-static {v7}, Landroidx/media3/exoplayer/source/p;->m(Landroidx/media3/exoplayer/source/p;)Ljava/lang/Runnable;

    move-result-object v7

    invoke-virtual {v6, v7}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    goto :goto_3

    :catch_0
    new-instance v0, Ljava/io/InterruptedIOException;

    invoke-direct {v0}, Ljava/io/InterruptedIOException;-><init>()V

    throw v0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    :cond_8
    if-ne v2, v3, :cond_9

    const/4 v2, 0x1

    const/4 v2, 0x0

    goto :goto_4

    :cond_9
    iget-object v3, v1, Landroidx/media3/exoplayer/source/p$b;->d:Landroidx/media3/exoplayer/source/o;

    invoke-interface {v3}, Landroidx/media3/exoplayer/source/o;->b()J

    move-result-wide v3

    const-wide/16 v5, -0x1

    cmp-long v7, v3, v5

    if-eqz v7, :cond_a

    iget-object v3, v1, Landroidx/media3/exoplayer/source/p$b;->g:Lz2/l0;

    iget-object v4, v1, Landroidx/media3/exoplayer/source/p$b;->d:Landroidx/media3/exoplayer/source/o;

    invoke-interface {v4}, Landroidx/media3/exoplayer/source/o;->b()J

    move-result-wide v4

    iput-wide v4, v3, Lz2/l0;->a:J

    :cond_a
    :goto_4
    iget-object v3, v1, Landroidx/media3/exoplayer/source/p$b;->c:Lh2/m;

    invoke-static {v3}, Lh2/f;->a(Landroidx/media3/datasource/a;)V

    goto/16 :goto_0

    :goto_5
    if-eq v2, v3, :cond_b

    iget-object v2, v1, Landroidx/media3/exoplayer/source/p$b;->d:Landroidx/media3/exoplayer/source/o;

    invoke-interface {v2}, Landroidx/media3/exoplayer/source/o;->b()J

    move-result-wide v2

    const-wide/16 v4, -0x1

    cmp-long v6, v2, v4

    if-eqz v6, :cond_b

    iget-object v2, v1, Landroidx/media3/exoplayer/source/p$b;->g:Lz2/l0;

    iget-object v3, v1, Landroidx/media3/exoplayer/source/p$b;->d:Landroidx/media3/exoplayer/source/o;

    invoke-interface {v3}, Landroidx/media3/exoplayer/source/o;->b()J

    move-result-wide v3

    iput-wide v3, v2, Lz2/l0;->a:J

    :cond_b
    iget-object v2, v1, Landroidx/media3/exoplayer/source/p$b;->c:Lh2/m;

    invoke-static {v2}, Lh2/f;->a(Landroidx/media3/datasource/a;)V

    throw v0

    :cond_c
    :goto_6
    return-void
.end method
