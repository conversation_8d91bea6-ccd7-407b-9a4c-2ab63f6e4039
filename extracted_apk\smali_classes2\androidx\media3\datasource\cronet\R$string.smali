.class public final Landroidx/media3/datasource/cronet/R$string;
.super Ljava/lang/Object;


# static fields
.field public static CronetProviderClassName:I = 0x7f120001

.field public static common_google_play_services_enable_button:I = 0x7f12010b

.field public static common_google_play_services_enable_text:I = 0x7f12010c

.field public static common_google_play_services_enable_title:I = 0x7f12010d

.field public static common_google_play_services_install_button:I = 0x7f12010e

.field public static common_google_play_services_install_text:I = 0x7f12010f

.field public static common_google_play_services_install_title:I = 0x7f120110

.field public static common_google_play_services_notification_channel_name:I = 0x7f120111

.field public static common_google_play_services_notification_ticker:I = 0x7f120112

.field public static common_google_play_services_unknown_issue:I = 0x7f120113

.field public static common_google_play_services_unsupported_text:I = 0x7f120114

.field public static common_google_play_services_update_button:I = 0x7f120115

.field public static common_google_play_services_update_text:I = 0x7f120116

.field public static common_google_play_services_update_title:I = 0x7f120117

.field public static common_google_play_services_updating_text:I = 0x7f120118

.field public static common_google_play_services_wear_update_text:I = 0x7f120119

.field public static common_open_on_phone:I = 0x7f12011a

.field public static common_signin_button_text:I = 0x7f12011b

.field public static common_signin_button_text_long:I = 0x7f12011c

.field public static status_bar_notification_info_overflow:I = 0x7f120629


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
