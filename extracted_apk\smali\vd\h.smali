.class public final synthetic Lvd/h;
.super Ljava/lang/Object;

# interfaces
.implements Lkc/h;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lkc/e;)Ljava/lang/Object;
    .locals 0

    invoke-static {p1}, Lcom/google/firebase/installations/FirebaseInstallationsRegistrar;->a(Lkc/e;)Lvd/g;

    move-result-object p1

    return-object p1
.end method
