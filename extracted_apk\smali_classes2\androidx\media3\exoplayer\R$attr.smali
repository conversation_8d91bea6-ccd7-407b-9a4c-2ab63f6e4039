.class public final Landroidx/media3/exoplayer/R$attr;
.super Ljava/lang/Object;


# static fields
.field public static alpha:I = 0x7f040065

.field public static font:I = 0x7f04037e

.field public static fontProviderAuthority:I = 0x7f040380

.field public static fontProviderCerts:I = 0x7f040381

.field public static fontProviderFetchStrategy:I = 0x7f040382

.field public static fontProviderFetchTimeout:I = 0x7f040383

.field public static fontProviderPackage:I = 0x7f040384

.field public static fontProviderQuery:I = 0x7f040385

.field public static fontProviderSystemFontFamily:I = 0x7f040386

.field public static fontStyle:I = 0x7f040387

.field public static fontVariationSettings:I = 0x7f040388

.field public static fontWeight:I = 0x7f040389

.field public static lStar:I = 0x7f04043b

.field public static nestedScrollViewStyle:I = 0x7f040543

.field public static queryPatterns:I = 0x7f0405ad

.field public static shortcutMatchRequired:I = 0x7f040614

.field public static ttcIndex:I = 0x7f040741


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
