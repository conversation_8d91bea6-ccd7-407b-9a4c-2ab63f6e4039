.class public final Landroidx/media3/exoplayer/offline/ProgressiveDownloader;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/offline/o;


# instance fields
.field public final a:Ljava/util/concurrent/Executor;

.field public final b:Lh2/g;

.field public final c:Landroidx/media3/datasource/cache/a;

.field public final d:Landroidx/media3/datasource/cache/i;

.field public final e:Landroidx/media3/common/PriorityTaskManager;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public f:Landroidx/media3/exoplayer/offline/o$a;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public volatile g:Landroidx/media3/common/util/RunnableFutureTask;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/media3/common/util/RunnableFutureTask<",
            "Ljava/lang/Void;",
            "Ljava/io/IOException;",
            ">;"
        }
    .end annotation
.end field

.field public volatile h:Z


# direct methods
.method public constructor <init>(Landroidx/media3/common/b0;Landroidx/media3/datasource/cache/a$c;Ljava/util/concurrent/Executor;)V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p3}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Ljava/util/concurrent/Executor;

    iput-object p3, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->a:Ljava/util/concurrent/Executor;

    iget-object p3, p1, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    invoke-static {p3}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance p3, Lh2/g$b;

    invoke-direct {p3}, Lh2/g$b;-><init>()V

    iget-object v0, p1, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    iget-object v0, v0, Landroidx/media3/common/b0$h;->a:Landroid/net/Uri;

    invoke-virtual {p3, v0}, Lh2/g$b;->i(Landroid/net/Uri;)Lh2/g$b;

    move-result-object p3

    iget-object p1, p1, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    iget-object p1, p1, Landroidx/media3/common/b0$h;->e:Ljava/lang/String;

    invoke-virtual {p3, p1}, Lh2/g$b;->f(Ljava/lang/String;)Lh2/g$b;

    move-result-object p1

    const/4 p3, 0x4

    invoke-virtual {p1, p3}, Lh2/g$b;->b(I)Lh2/g$b;

    move-result-object p1

    invoke-virtual {p1}, Lh2/g$b;->a()Lh2/g;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->b:Lh2/g;

    invoke-virtual {p2}, Landroidx/media3/datasource/cache/a$c;->b()Landroidx/media3/datasource/cache/a;

    move-result-object p3

    iput-object p3, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->c:Landroidx/media3/datasource/cache/a;

    new-instance v0, Landroidx/media3/exoplayer/offline/s;

    invoke-direct {v0, p0}, Landroidx/media3/exoplayer/offline/s;-><init>(Landroidx/media3/exoplayer/offline/ProgressiveDownloader;)V

    new-instance v1, Landroidx/media3/datasource/cache/i;

    const/4 v2, 0x1

    const/4 v2, 0x0

    invoke-direct {v1, p3, p1, v2, v0}, Landroidx/media3/datasource/cache/i;-><init>(Landroidx/media3/datasource/cache/a;Lh2/g;[BLandroidx/media3/datasource/cache/i$a;)V

    iput-object v1, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->d:Landroidx/media3/datasource/cache/i;

    invoke-virtual {p2}, Landroidx/media3/datasource/cache/a$c;->g()Landroidx/media3/common/PriorityTaskManager;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->e:Landroidx/media3/common/PriorityTaskManager;

    return-void
.end method

.method public static synthetic b(Landroidx/media3/exoplayer/offline/ProgressiveDownloader;JJJ)V
    .locals 0

    invoke-virtual/range {p0 .. p6}, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->d(JJJ)V

    return-void
.end method

.method public static synthetic c(Landroidx/media3/exoplayer/offline/ProgressiveDownloader;)Landroidx/media3/datasource/cache/i;
    .locals 0

    iget-object p0, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->d:Landroidx/media3/datasource/cache/i;

    return-object p0
.end method


# virtual methods
.method public a(Landroidx/media3/exoplayer/offline/o$a;)V
    .locals 3
    .param p1    # Landroidx/media3/exoplayer/offline/o$a;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;,
            Ljava/lang/InterruptedException;
        }
    .end annotation

    iput-object p1, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->f:Landroidx/media3/exoplayer/offline/o$a;

    iget-object p1, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->e:Landroidx/media3/common/PriorityTaskManager;

    const/16 v0, -0x3e8

    if-eqz p1, :cond_0

    invoke-virtual {p1, v0}, Landroidx/media3/common/PriorityTaskManager;->a(I)V

    :cond_0
    const/4 p1, 0x1

    const/4 p1, 0x0

    :goto_0
    if-nez p1, :cond_5

    :try_start_0
    iget-boolean v1, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->h:Z

    if-nez v1, :cond_5

    new-instance v1, Landroidx/media3/exoplayer/offline/ProgressiveDownloader$1;

    invoke-direct {v1, p0}, Landroidx/media3/exoplayer/offline/ProgressiveDownloader$1;-><init>(Landroidx/media3/exoplayer/offline/ProgressiveDownloader;)V

    iput-object v1, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->g:Landroidx/media3/common/util/RunnableFutureTask;

    iget-object v1, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->e:Landroidx/media3/common/PriorityTaskManager;

    if-eqz v1, :cond_1

    invoke-virtual {v1, v0}, Landroidx/media3/common/PriorityTaskManager;->b(I)V

    goto :goto_1

    :catchall_0
    move-exception p1

    goto :goto_2

    :cond_1
    :goto_1
    iget-object v1, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->a:Ljava/util/concurrent/Executor;

    iget-object v2, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->g:Landroidx/media3/common/util/RunnableFutureTask;

    invoke-interface {v1, v2}, Ljava/util/concurrent/Executor;->execute(Ljava/lang/Runnable;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :try_start_1
    iget-object v1, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->g:Landroidx/media3/common/util/RunnableFutureTask;

    invoke-virtual {v1}, Landroidx/media3/common/util/RunnableFutureTask;->get()Ljava/lang/Object;
    :try_end_1
    .catch Ljava/util/concurrent/ExecutionException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    const/4 p1, 0x1

    goto :goto_0

    :catch_0
    move-exception v1

    :try_start_2
    invoke-virtual {v1}, Ljava/lang/Throwable;->getCause()Ljava/lang/Throwable;

    move-result-object v1

    invoke-static {v1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/Throwable;

    instance-of v2, v1, Landroidx/media3/common/PriorityTaskManager$PriorityTooLowException;

    if-eqz v2, :cond_2

    goto :goto_0

    :cond_2
    instance-of v2, v1, Ljava/io/IOException;

    if-nez v2, :cond_3

    invoke-static {v1}, Le2/u0;->m1(Ljava/lang/Throwable;)V

    goto :goto_0

    :cond_3
    check-cast v1, Ljava/io/IOException;

    throw v1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    :goto_2
    iget-object v1, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->g:Landroidx/media3/common/util/RunnableFutureTask;

    invoke-static {v1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/common/util/RunnableFutureTask;

    invoke-virtual {v1}, Landroidx/media3/common/util/RunnableFutureTask;->blockUntilFinished()V

    iget-object v1, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->e:Landroidx/media3/common/PriorityTaskManager;

    if-eqz v1, :cond_4

    invoke-virtual {v1, v0}, Landroidx/media3/common/PriorityTaskManager;->d(I)V

    :cond_4
    throw p1

    :cond_5
    iget-object p1, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->g:Landroidx/media3/common/util/RunnableFutureTask;

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/common/util/RunnableFutureTask;

    invoke-virtual {p1}, Landroidx/media3/common/util/RunnableFutureTask;->blockUntilFinished()V

    iget-object p1, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->e:Landroidx/media3/common/PriorityTaskManager;

    if-eqz p1, :cond_6

    invoke-virtual {p1, v0}, Landroidx/media3/common/PriorityTaskManager;->d(I)V

    :cond_6
    return-void
.end method

.method public cancel()V
    .locals 2

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->h:Z

    iget-object v1, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->g:Landroidx/media3/common/util/RunnableFutureTask;

    if-eqz v1, :cond_0

    invoke-virtual {v1, v0}, Landroidx/media3/common/util/RunnableFutureTask;->cancel(Z)Z

    :cond_0
    return-void
.end method

.method public final d(JJJ)V
    .locals 6

    iget-object v0, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->f:Landroidx/media3/exoplayer/offline/o$a;

    if-nez v0, :cond_0

    return-void

    :cond_0
    const-wide/16 p5, -0x1

    cmp-long v1, p1, p5

    if-eqz v1, :cond_2

    const-wide/16 p5, 0x0

    cmp-long v1, p1, p5

    if-nez v1, :cond_1

    goto :goto_0

    :cond_1
    long-to-float p5, p3

    const/high16 p6, 0x42c80000    # 100.0f

    mul-float p5, p5, p6

    long-to-float p6, p1

    div-float/2addr p5, p6

    move v5, p5

    goto :goto_1

    :cond_2
    :goto_0
    const/high16 p5, -0x40800000    # -1.0f

    const/high16 v5, -0x40800000    # -1.0f

    :goto_1
    move-wide v1, p1

    move-wide v3, p3

    invoke-interface/range {v0 .. v5}, Landroidx/media3/exoplayer/offline/o$a;->onProgress(JJF)V

    return-void
.end method

.method public remove()V
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->c:Landroidx/media3/datasource/cache/a;

    invoke-virtual {v0}, Landroidx/media3/datasource/cache/a;->e()Landroidx/media3/datasource/cache/Cache;

    move-result-object v0

    iget-object v1, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->c:Landroidx/media3/datasource/cache/a;

    invoke-virtual {v1}, Landroidx/media3/datasource/cache/a;->f()Landroidx/media3/datasource/cache/g;

    move-result-object v1

    iget-object v2, p0, Landroidx/media3/exoplayer/offline/ProgressiveDownloader;->b:Lh2/g;

    invoke-interface {v1, v2}, Landroidx/media3/datasource/cache/g;->a(Lh2/g;)Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Landroidx/media3/datasource/cache/Cache;->e(Ljava/lang/String;)V

    return-void
.end method
