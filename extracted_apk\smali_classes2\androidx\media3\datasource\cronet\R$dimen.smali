.class public final Landroidx/media3/datasource/cronet/R$dimen;
.super Ljava/lang/Object;


# static fields
.field public static compat_button_inset_horizontal_material:I = 0x7f07009a

.field public static compat_button_inset_vertical_material:I = 0x7f07009b

.field public static compat_button_padding_horizontal_material:I = 0x7f07009c

.field public static compat_button_padding_vertical_material:I = 0x7f07009d

.field public static compat_control_corner_material:I = 0x7f07009e

.field public static compat_notification_large_icon_max_height:I = 0x7f07009f

.field public static compat_notification_large_icon_max_width:I = 0x7f0700a0

.field public static notification_action_icon_size:I = 0x7f0702c7

.field public static notification_action_text_size:I = 0x7f0702c8

.field public static notification_big_circle_margin:I = 0x7f0702c9

.field public static notification_content_margin_start:I = 0x7f0702ca

.field public static notification_large_icon_height:I = 0x7f0702cb

.field public static notification_large_icon_width:I = 0x7f0702cc

.field public static notification_main_column_padding_top:I = 0x7f0702cd

.field public static notification_media_narrow_margin:I = 0x7f0702ce

.field public static notification_right_icon_size:I = 0x7f0702cf

.field public static notification_right_side_padding_top:I = 0x7f0702d0

.field public static notification_small_icon_background_padding:I = 0x7f0702d1

.field public static notification_small_icon_size_as_large:I = 0x7f0702d2

.field public static notification_subtext_size:I = 0x7f0702d3

.field public static notification_top_pad:I = 0x7f0702d4

.field public static notification_top_pad_large_text:I = 0x7f0702d5


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
