.class public abstract Lorg/chromium/net/ExperimentalBidirectionalStream;
.super Lorg/chromium/net/BidirectionalStream;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lorg/chromium/net/ExperimentalBidirectionalStream$Builder;
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lorg/chromium/net/BidirectionalStream;-><init>()V

    return-void
.end method
