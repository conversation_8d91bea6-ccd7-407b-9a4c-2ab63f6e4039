.class public Landroidx/media3/exoplayer/hls/q$c;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/r0;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/hls/q;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "c"
.end annotation


# static fields
.field public static final g:Landroidx/media3/common/y;

.field public static final h:Landroidx/media3/common/y;


# instance fields
.field public final a:Lj3/a;

.field public final b:Lz2/r0;

.field public final c:Landroidx/media3/common/y;

.field public d:Landroidx/media3/common/y;

.field public e:[B

.field public f:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Landroidx/media3/common/y$b;

    invoke-direct {v0}, Landroidx/media3/common/y$b;-><init>()V

    const-string v1, "application/id3"

    invoke-virtual {v0, v1}, Landroidx/media3/common/y$b;->k0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object v0

    sput-object v0, Landroidx/media3/exoplayer/hls/q$c;->g:Landroidx/media3/common/y;

    new-instance v0, Landroidx/media3/common/y$b;

    invoke-direct {v0}, Landroidx/media3/common/y$b;-><init>()V

    const-string v1, "application/x-emsg"

    invoke-virtual {v0, v1}, Landroidx/media3/common/y$b;->k0(Ljava/lang/String;)Landroidx/media3/common/y$b;

    move-result-object v0

    invoke-virtual {v0}, Landroidx/media3/common/y$b;->I()Landroidx/media3/common/y;

    move-result-object v0

    sput-object v0, Landroidx/media3/exoplayer/hls/q$c;->h:Landroidx/media3/common/y;

    return-void
.end method

.method public constructor <init>(Lz2/r0;I)V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lj3/a;

    invoke-direct {v0}, Lj3/a;-><init>()V

    iput-object v0, p0, Landroidx/media3/exoplayer/hls/q$c;->a:Lj3/a;

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q$c;->b:Lz2/r0;

    const/4 p1, 0x1

    if-eq p2, p1, :cond_1

    const/4 p1, 0x3

    if-ne p2, p1, :cond_0

    sget-object p1, Landroidx/media3/exoplayer/hls/q$c;->h:Landroidx/media3/common/y;

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q$c;->c:Landroidx/media3/common/y;

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Unknown metadataType: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    sget-object p1, Landroidx/media3/exoplayer/hls/q$c;->g:Landroidx/media3/common/y;

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q$c;->c:Landroidx/media3/common/y;

    :goto_0
    const/4 p1, 0x1

    const/4 p1, 0x0

    new-array p2, p1, [B

    iput-object p2, p0, Landroidx/media3/exoplayer/hls/q$c;->e:[B

    iput p1, p0, Landroidx/media3/exoplayer/hls/q$c;->f:I

    return-void
.end method


# virtual methods
.method public a(Landroidx/media3/common/y;)V
    .locals 1

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q$c;->d:Landroidx/media3/common/y;

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q$c;->b:Lz2/r0;

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q$c;->c:Landroidx/media3/common/y;

    invoke-interface {p1, v0}, Lz2/r0;->a(Landroidx/media3/common/y;)V

    return-void
.end method

.method public b(Le2/c0;II)V
    .locals 1

    iget p3, p0, Landroidx/media3/exoplayer/hls/q$c;->f:I

    add-int/2addr p3, p2

    invoke-virtual {p0, p3}, Landroidx/media3/exoplayer/hls/q$c;->h(I)V

    iget-object p3, p0, Landroidx/media3/exoplayer/hls/q$c;->e:[B

    iget v0, p0, Landroidx/media3/exoplayer/hls/q$c;->f:I

    invoke-virtual {p1, p3, v0, p2}, Le2/c0;->l([BII)V

    iget p1, p0, Landroidx/media3/exoplayer/hls/q$c;->f:I

    add-int/2addr p1, p2

    iput p1, p0, Landroidx/media3/exoplayer/hls/q$c;->f:I

    return-void
.end method

.method public synthetic c(Landroidx/media3/common/l;IZ)I
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lz2/q0;->a(Lz2/r0;Landroidx/media3/common/l;IZ)I

    move-result p1

    return p1
.end method

.method public d(Landroidx/media3/common/l;IZI)I
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget p4, p0, Landroidx/media3/exoplayer/hls/q$c;->f:I

    add-int/2addr p4, p2

    invoke-virtual {p0, p4}, Landroidx/media3/exoplayer/hls/q$c;->h(I)V

    iget-object p4, p0, Landroidx/media3/exoplayer/hls/q$c;->e:[B

    iget v0, p0, Landroidx/media3/exoplayer/hls/q$c;->f:I

    invoke-interface {p1, p4, v0, p2}, Landroidx/media3/common/l;->read([BII)I

    move-result p1

    const/4 p2, -0x1

    if-ne p1, p2, :cond_1

    if-eqz p3, :cond_0

    return p2

    :cond_0
    new-instance p1, Ljava/io/EOFException;

    invoke-direct {p1}, Ljava/io/EOFException;-><init>()V

    throw p1

    :cond_1
    iget p2, p0, Landroidx/media3/exoplayer/hls/q$c;->f:I

    add-int/2addr p2, p1

    iput p2, p0, Landroidx/media3/exoplayer/hls/q$c;->f:I

    return p1
.end method

.method public e(JIIILz2/r0$a;)V
    .locals 7
    .param p6    # Lz2/r0$a;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q$c;->d:Landroidx/media3/common/y;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p0, p4, p5}, Landroidx/media3/exoplayer/hls/q$c;->i(II)Le2/c0;

    move-result-object p4

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q$c;->d:Landroidx/media3/common/y;

    iget-object v0, v0, Landroidx/media3/common/y;->m:Ljava/lang/String;

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q$c;->c:Landroidx/media3/common/y;

    iget-object v1, v1, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-static {v0, v1}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q$c;->d:Landroidx/media3/common/y;

    iget-object v0, v0, Landroidx/media3/common/y;->m:Ljava/lang/String;

    const-string v1, "application/x-emsg"

    invoke-virtual {v1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    const-string v1, "HlsSampleStreamWrapper"

    if-eqz v0, :cond_2

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q$c;->a:Lj3/a;

    invoke-virtual {v0, p4}, Lj3/a;->c(Le2/c0;)Landroidx/media3/extractor/metadata/emsg/EventMessage;

    move-result-object p4

    invoke-virtual {p0, p4}, Landroidx/media3/exoplayer/hls/q$c;->g(Landroidx/media3/extractor/metadata/emsg/EventMessage;)Z

    move-result v0

    if-nez v0, :cond_1

    const/4 p1, 0x2

    new-array p1, p1, [Ljava/lang/Object;

    iget-object p2, p0, Landroidx/media3/exoplayer/hls/q$c;->c:Landroidx/media3/common/y;

    iget-object p2, p2, Landroidx/media3/common/y;->m:Ljava/lang/String;

    const/4 p3, 0x1

    const/4 p3, 0x0

    aput-object p2, p1, p3

    const/4 p2, 0x1

    invoke-virtual {p4}, Landroidx/media3/extractor/metadata/emsg/EventMessage;->d()Landroidx/media3/common/y;

    move-result-object p3

    aput-object p3, p1, p2

    const-string p2, "Ignoring EMSG. Expected it to contain wrapped %s but actual wrapped format: %s"

    invoke-static {p2, p1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Le2/o;->h(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    :cond_1
    new-instance v0, Le2/c0;

    invoke-virtual {p4}, Landroidx/media3/extractor/metadata/emsg/EventMessage;->g()[B

    move-result-object p4

    invoke-static {p4}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p4

    check-cast p4, [B

    invoke-direct {v0, p4}, Le2/c0;-><init>([B)V

    move-object p4, v0

    :goto_0
    invoke-virtual {p4}, Le2/c0;->a()I

    move-result v4

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q$c;->b:Lz2/r0;

    invoke-interface {v0, p4, v4}, Lz2/r0;->f(Le2/c0;I)V

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q$c;->b:Lz2/r0;

    move-wide v1, p1

    move v3, p3

    move v5, p5

    move-object v6, p6

    invoke-interface/range {v0 .. v6}, Lz2/r0;->e(JIIILz2/r0$a;)V

    return-void

    :cond_2
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "Ignoring sample for unsupported format: "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p2, p0, Landroidx/media3/exoplayer/hls/q$c;->d:Landroidx/media3/common/y;

    iget-object p2, p2, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Le2/o;->h(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic f(Le2/c0;I)V
    .locals 0

    invoke-static {p0, p1, p2}, Lz2/q0;->b(Lz2/r0;Le2/c0;I)V

    return-void
.end method

.method public final g(Landroidx/media3/extractor/metadata/emsg/EventMessage;)Z
    .locals 1

    invoke-virtual {p1}, Landroidx/media3/extractor/metadata/emsg/EventMessage;->d()Landroidx/media3/common/y;

    move-result-object p1

    if-eqz p1, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q$c;->c:Landroidx/media3/common/y;

    iget-object v0, v0, Landroidx/media3/common/y;->m:Ljava/lang/String;

    iget-object p1, p1, Landroidx/media3/common/y;->m:Ljava/lang/String;

    invoke-static {v0, p1}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x1

    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public final h(I)V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/q$c;->e:[B

    array-length v1, v0

    if-ge v1, p1, :cond_0

    div-int/lit8 v1, p1, 0x2

    add-int/2addr p1, v1

    invoke-static {v0, p1}, Ljava/util/Arrays;->copyOf([BI)[B

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/q$c;->e:[B

    :cond_0
    return-void
.end method

.method public final i(II)Le2/c0;
    .locals 3

    iget v0, p0, Landroidx/media3/exoplayer/hls/q$c;->f:I

    sub-int/2addr v0, p2

    sub-int p1, v0, p1

    iget-object v1, p0, Landroidx/media3/exoplayer/hls/q$c;->e:[B

    invoke-static {v1, p1, v0}, Ljava/util/Arrays;->copyOfRange([BII)[B

    move-result-object p1

    new-instance v1, Le2/c0;

    invoke-direct {v1, p1}, Le2/c0;-><init>([B)V

    iget-object p1, p0, Landroidx/media3/exoplayer/hls/q$c;->e:[B

    const/4 v2, 0x1

    const/4 v2, 0x0

    invoke-static {p1, v0, p1, v2, p2}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    iput p2, p0, Landroidx/media3/exoplayer/hls/q$c;->f:I

    return-object v1
.end method
