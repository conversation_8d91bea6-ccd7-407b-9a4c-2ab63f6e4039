.class public final Landroidx/media3/exoplayer/dash/d$c;
.super Ljava/lang/Object;

# interfaces
.implements Lz2/r0;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/dash/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "c"
.end annotation


# instance fields
.field public final a:Landroidx/media3/exoplayer/source/s;

.field public final b:Landroidx/media3/exoplayer/t1;

.field public final c:Lh3/b;

.field public d:J

.field public final synthetic e:Landroidx/media3/exoplayer/dash/d;


# direct methods
.method public constructor <init>(Landroidx/media3/exoplayer/dash/d;Landroidx/media3/exoplayer/upstream/b;)V
    .locals 0

    iput-object p1, p0, Landroidx/media3/exoplayer/dash/d$c;->e:Landroidx/media3/exoplayer/dash/d;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-static {p2}, Landroidx/media3/exoplayer/source/s;->l(Landroidx/media3/exoplayer/upstream/b;)Landroidx/media3/exoplayer/source/s;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/exoplayer/dash/d$c;->a:Landroidx/media3/exoplayer/source/s;

    new-instance p1, Landroidx/media3/exoplayer/t1;

    invoke-direct {p1}, Landroidx/media3/exoplayer/t1;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/dash/d$c;->b:Landroidx/media3/exoplayer/t1;

    new-instance p1, Lh3/b;

    invoke-direct {p1}, Lh3/b;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/dash/d$c;->c:Lh3/b;

    const-wide p1, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide p1, p0, Landroidx/media3/exoplayer/dash/d$c;->d:J

    return-void
.end method


# virtual methods
.method public a(Landroidx/media3/common/y;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/d$c;->a:Landroidx/media3/exoplayer/source/s;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/source/s;->a(Landroidx/media3/common/y;)V

    return-void
.end method

.method public b(Le2/c0;II)V
    .locals 0

    iget-object p3, p0, Landroidx/media3/exoplayer/dash/d$c;->a:Landroidx/media3/exoplayer/source/s;

    invoke-virtual {p3, p1, p2}, Landroidx/media3/exoplayer/source/s;->f(Le2/c0;I)V

    return-void
.end method

.method public synthetic c(Landroidx/media3/common/l;IZ)I
    .locals 0

    invoke-static {p0, p1, p2, p3}, Lz2/q0;->a(Lz2/r0;Landroidx/media3/common/l;IZ)I

    move-result p1

    return p1
.end method

.method public d(Landroidx/media3/common/l;IZI)I
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object p4, p0, Landroidx/media3/exoplayer/dash/d$c;->a:Landroidx/media3/exoplayer/source/s;

    invoke-virtual {p4, p1, p2, p3}, Landroidx/media3/exoplayer/source/s;->c(Landroidx/media3/common/l;IZ)I

    move-result p1

    return p1
.end method

.method public e(JIIILz2/r0$a;)V
    .locals 7
    .param p6    # Lz2/r0$a;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/d$c;->a:Landroidx/media3/exoplayer/source/s;

    move-wide v1, p1

    move v3, p3

    move v4, p4

    move v5, p5

    move-object v6, p6

    invoke-virtual/range {v0 .. v6}, Landroidx/media3/exoplayer/source/s;->e(JIIILz2/r0$a;)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/dash/d$c;->l()V

    return-void
.end method

.method public synthetic f(Le2/c0;I)V
    .locals 0

    invoke-static {p0, p1, p2}, Lz2/q0;->b(Lz2/r0;Le2/c0;I)V

    return-void
.end method

.method public final g()Lh3/b;
    .locals 4
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/d$c;->c:Lh3/b;

    invoke-virtual {v0}, Landroidx/media3/decoder/DecoderInputBuffer;->clear()V

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/d$c;->a:Landroidx/media3/exoplayer/source/s;

    iget-object v1, p0, Landroidx/media3/exoplayer/dash/d$c;->b:Landroidx/media3/exoplayer/t1;

    iget-object v2, p0, Landroidx/media3/exoplayer/dash/d$c;->c:Lh3/b;

    const/4 v3, 0x1

    const/4 v3, 0x0

    invoke-virtual {v0, v1, v2, v3, v3}, Landroidx/media3/exoplayer/source/s;->T(Landroidx/media3/exoplayer/t1;Landroidx/media3/decoder/DecoderInputBuffer;IZ)I

    move-result v0

    const/4 v1, -0x4

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/d$c;->c:Lh3/b;

    invoke-virtual {v0}, Landroidx/media3/decoder/DecoderInputBuffer;->c()V

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/d$c;->c:Lh3/b;

    return-object v0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    return-object v0
.end method

.method public h(J)Z
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/d$c;->e:Landroidx/media3/exoplayer/dash/d;

    invoke-virtual {v0, p1, p2}, Landroidx/media3/exoplayer/dash/d;->j(J)Z

    move-result p1

    return p1
.end method

.method public i(Lv2/e;)V
    .locals 5

    iget-wide v0, p0, Landroidx/media3/exoplayer/dash/d$c;->d:J

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v4, v0, v2

    if-eqz v4, :cond_0

    iget-wide v2, p1, Lv2/e;->h:J

    cmp-long v4, v2, v0

    if-lez v4, :cond_1

    :cond_0
    iget-wide v0, p1, Lv2/e;->h:J

    iput-wide v0, p0, Landroidx/media3/exoplayer/dash/d$c;->d:J

    :cond_1
    iget-object v0, p0, Landroidx/media3/exoplayer/dash/d$c;->e:Landroidx/media3/exoplayer/dash/d;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/dash/d;->m(Lv2/e;)V

    return-void
.end method

.method public j(Lv2/e;)Z
    .locals 5

    iget-wide v0, p0, Landroidx/media3/exoplayer/dash/d$c;->d:J

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v4, v0, v2

    if-eqz v4, :cond_0

    iget-wide v2, p1, Lv2/e;->g:J

    cmp-long p1, v0, v2

    if-gez p1, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x1

    const/4 p1, 0x0

    :goto_0
    iget-object v0, p0, Landroidx/media3/exoplayer/dash/d$c;->e:Landroidx/media3/exoplayer/dash/d;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/dash/d;->n(Z)Z

    move-result p1

    return p1
.end method

.method public final k(JJ)V
    .locals 1

    new-instance v0, Landroidx/media3/exoplayer/dash/d$a;

    invoke-direct {v0, p1, p2, p3, p4}, Landroidx/media3/exoplayer/dash/d$a;-><init>(JJ)V

    iget-object p1, p0, Landroidx/media3/exoplayer/dash/d$c;->e:Landroidx/media3/exoplayer/dash/d;

    invoke-static {p1}, Landroidx/media3/exoplayer/dash/d;->d(Landroidx/media3/exoplayer/dash/d;)Landroid/os/Handler;

    move-result-object p1

    iget-object p2, p0, Landroidx/media3/exoplayer/dash/d$c;->e:Landroidx/media3/exoplayer/dash/d;

    invoke-static {p2}, Landroidx/media3/exoplayer/dash/d;->d(Landroidx/media3/exoplayer/dash/d;)Landroid/os/Handler;

    move-result-object p2

    const/4 p3, 0x1

    invoke-virtual {p2, p3, v0}, Landroid/os/Handler;->obtainMessage(ILjava/lang/Object;)Landroid/os/Message;

    move-result-object p2

    invoke-virtual {p1, p2}, Landroid/os/Handler;->sendMessage(Landroid/os/Message;)Z

    return-void
.end method

.method public final l()V
    .locals 5

    :cond_0
    :goto_0
    iget-object v0, p0, Landroidx/media3/exoplayer/dash/d$c;->a:Landroidx/media3/exoplayer/source/s;

    const/4 v1, 0x1

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/source/s;->L(Z)Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-virtual {p0}, Landroidx/media3/exoplayer/dash/d$c;->g()Lh3/b;

    move-result-object v0

    if-nez v0, :cond_1

    goto :goto_0

    :cond_1
    iget-wide v2, v0, Landroidx/media3/decoder/DecoderInputBuffer;->f:J

    iget-object v4, p0, Landroidx/media3/exoplayer/dash/d$c;->e:Landroidx/media3/exoplayer/dash/d;

    invoke-static {v4}, Landroidx/media3/exoplayer/dash/d;->a(Landroidx/media3/exoplayer/dash/d;)Lj3/a;

    move-result-object v4

    invoke-virtual {v4, v0}, Lh3/c;->a(Lh3/b;)Landroidx/media3/common/Metadata;

    move-result-object v0

    if-nez v0, :cond_2

    goto :goto_0

    :cond_2
    invoke-virtual {v0, v1}, Landroidx/media3/common/Metadata;->f(I)Landroidx/media3/common/Metadata$Entry;

    move-result-object v0

    check-cast v0, Landroidx/media3/extractor/metadata/emsg/EventMessage;

    iget-object v1, v0, Landroidx/media3/extractor/metadata/emsg/EventMessage;->schemeIdUri:Ljava/lang/String;

    iget-object v4, v0, Landroidx/media3/extractor/metadata/emsg/EventMessage;->value:Ljava/lang/String;

    invoke-static {v1, v4}, Landroidx/media3/exoplayer/dash/d;->b(Ljava/lang/String;Ljava/lang/String;)Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-virtual {p0, v2, v3, v0}, Landroidx/media3/exoplayer/dash/d$c;->m(JLandroidx/media3/extractor/metadata/emsg/EventMessage;)V

    goto :goto_0

    :cond_3
    iget-object v0, p0, Landroidx/media3/exoplayer/dash/d$c;->a:Landroidx/media3/exoplayer/source/s;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/source/s;->s()V

    return-void
.end method

.method public final m(JLandroidx/media3/extractor/metadata/emsg/EventMessage;)V
    .locals 4

    invoke-static {p3}, Landroidx/media3/exoplayer/dash/d;->c(Landroidx/media3/extractor/metadata/emsg/EventMessage;)J

    move-result-wide v0

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long p3, v0, v2

    if-nez p3, :cond_0

    return-void

    :cond_0
    invoke-virtual {p0, p1, p2, v0, v1}, Landroidx/media3/exoplayer/dash/d$c;->k(JJ)V

    return-void
.end method

.method public n()V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/d$c;->a:Landroidx/media3/exoplayer/source/s;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/source/s;->U()V

    return-void
.end method
