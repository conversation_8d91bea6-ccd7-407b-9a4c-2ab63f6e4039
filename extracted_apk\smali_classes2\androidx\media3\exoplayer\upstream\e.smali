.class public interface abstract Landroidx/media3/exoplayer/upstream/e;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/upstream/e$a;
    }
.end annotation


# virtual methods
.method public abstract a()J
.end method

.method public abstract b()Lh2/o;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end method

.method public abstract c(Landroidx/media3/exoplayer/upstream/e$a;)V
.end method

.method public abstract d(Landroid/os/Handler;Landroidx/media3/exoplayer/upstream/e$a;)V
.end method

.method public abstract getBitrateEstimate()J
.end method
