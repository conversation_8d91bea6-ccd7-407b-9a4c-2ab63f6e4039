.class public final Landroidx/media3/exoplayer/r;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/y1;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/r$a;
    }
.end annotation


# instance fields
.field public final a:Landroidx/media3/exoplayer/c3;

.field public final b:Landroidx/media3/exoplayer/r$a;

.field public c:Landroidx/media3/exoplayer/w2;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public d:Landroidx/media3/exoplayer/y1;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public f:Z

.field public g:Z


# direct methods
.method public constructor <init>(Landroidx/media3/exoplayer/r$a;Le2/d;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/r;->b:Landroidx/media3/exoplayer/r$a;

    new-instance p1, Landroidx/media3/exoplayer/c3;

    invoke-direct {p1, p2}, Landroidx/media3/exoplayer/c3;-><init>(Le2/d;)V

    iput-object p1, p0, Landroidx/media3/exoplayer/r;->a:Landroidx/media3/exoplayer/c3;

    const/4 p1, 0x1

    iput-boolean p1, p0, Landroidx/media3/exoplayer/r;->f:Z

    return-void
.end method


# virtual methods
.method public a(Landroidx/media3/exoplayer/w2;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/r;->c:Landroidx/media3/exoplayer/w2;

    if-ne p1, v0, :cond_0

    const/4 p1, 0x1

    const/4 p1, 0x0

    iput-object p1, p0, Landroidx/media3/exoplayer/r;->d:Landroidx/media3/exoplayer/y1;

    iput-object p1, p0, Landroidx/media3/exoplayer/r;->c:Landroidx/media3/exoplayer/w2;

    const/4 p1, 0x1

    iput-boolean p1, p0, Landroidx/media3/exoplayer/r;->f:Z

    :cond_0
    return-void
.end method

.method public b(Landroidx/media3/common/g0;)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/r;->d:Landroidx/media3/exoplayer/y1;

    if-eqz v0, :cond_0

    invoke-interface {v0, p1}, Landroidx/media3/exoplayer/y1;->b(Landroidx/media3/common/g0;)V

    iget-object p1, p0, Landroidx/media3/exoplayer/r;->d:Landroidx/media3/exoplayer/y1;

    invoke-interface {p1}, Landroidx/media3/exoplayer/y1;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object p1

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/r;->a:Landroidx/media3/exoplayer/c3;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/c3;->b(Landroidx/media3/common/g0;)V

    return-void
.end method

.method public c(Landroidx/media3/exoplayer/w2;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    invoke-interface {p1}, Landroidx/media3/exoplayer/w2;->getMediaClock()Landroidx/media3/exoplayer/y1;

    move-result-object v0

    if-eqz v0, :cond_1

    iget-object v1, p0, Landroidx/media3/exoplayer/r;->d:Landroidx/media3/exoplayer/y1;

    if-eq v0, v1, :cond_1

    if-nez v1, :cond_0

    iput-object v0, p0, Landroidx/media3/exoplayer/r;->d:Landroidx/media3/exoplayer/y1;

    iput-object p1, p0, Landroidx/media3/exoplayer/r;->c:Landroidx/media3/exoplayer/w2;

    iget-object p1, p0, Landroidx/media3/exoplayer/r;->a:Landroidx/media3/exoplayer/c3;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/c3;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object p1

    invoke-interface {v0, p1}, Landroidx/media3/exoplayer/y1;->b(Landroidx/media3/common/g0;)V

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "Multiple renderer media clocks enabled."

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    const/16 v0, 0x3e8

    invoke-static {p1, v0}, Landroidx/media3/exoplayer/ExoPlaybackException;->createForUnexpected(Ljava/lang/RuntimeException;I)Landroidx/media3/exoplayer/ExoPlaybackException;

    move-result-object p1

    throw p1

    :cond_1
    :goto_0
    return-void
.end method

.method public d(J)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/r;->a:Landroidx/media3/exoplayer/c3;

    invoke-virtual {v0, p1, p2}, Landroidx/media3/exoplayer/c3;->a(J)V

    return-void
.end method

.method public final e(Z)Z
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/r;->c:Landroidx/media3/exoplayer/w2;

    if-eqz v0, :cond_2

    invoke-interface {v0}, Landroidx/media3/exoplayer/w2;->isEnded()Z

    move-result v0

    if-nez v0, :cond_2

    if-eqz p1, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/r;->c:Landroidx/media3/exoplayer/w2;

    invoke-interface {v0}, Landroidx/media3/exoplayer/w2;->getState()I

    move-result v0

    const/4 v1, 0x2

    if-ne v0, v1, :cond_2

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/r;->c:Landroidx/media3/exoplayer/w2;

    invoke-interface {v0}, Landroidx/media3/exoplayer/w2;->isReady()Z

    move-result v0

    if-nez v0, :cond_1

    if-nez p1, :cond_2

    iget-object p1, p0, Landroidx/media3/exoplayer/r;->c:Landroidx/media3/exoplayer/w2;

    invoke-interface {p1}, Landroidx/media3/exoplayer/w2;->hasReadStreamToEnd()Z

    move-result p1

    if-eqz p1, :cond_1

    goto :goto_0

    :cond_1
    const/4 p1, 0x1

    const/4 p1, 0x0

    goto :goto_1

    :cond_2
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1
.end method

.method public f()V
    .locals 1

    const/4 v0, 0x1

    iput-boolean v0, p0, Landroidx/media3/exoplayer/r;->g:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/r;->a:Landroidx/media3/exoplayer/c3;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c3;->c()V

    return-void
.end method

.method public g()Z
    .locals 1

    iget-boolean v0, p0, Landroidx/media3/exoplayer/r;->f:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/r;->a:Landroidx/media3/exoplayer/c3;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c3;->g()Z

    move-result v0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/r;->d:Landroidx/media3/exoplayer/y1;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/y1;

    invoke-interface {v0}, Landroidx/media3/exoplayer/y1;->g()Z

    move-result v0

    :goto_0
    return v0
.end method

.method public getPlaybackParameters()Landroidx/media3/common/g0;
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/r;->d:Landroidx/media3/exoplayer/y1;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Landroidx/media3/exoplayer/y1;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object v0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/r;->a:Landroidx/media3/exoplayer/c3;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c3;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object v0

    :goto_0
    return-object v0
.end method

.method public getPositionUs()J
    .locals 2

    iget-boolean v0, p0, Landroidx/media3/exoplayer/r;->f:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/r;->a:Landroidx/media3/exoplayer/c3;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c3;->getPositionUs()J

    move-result-wide v0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Landroidx/media3/exoplayer/r;->d:Landroidx/media3/exoplayer/y1;

    invoke-static {v0}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroidx/media3/exoplayer/y1;

    invoke-interface {v0}, Landroidx/media3/exoplayer/y1;->getPositionUs()J

    move-result-wide v0

    :goto_0
    return-wide v0
.end method

.method public h()V
    .locals 1

    const/4 v0, 0x1

    const/4 v0, 0x0

    iput-boolean v0, p0, Landroidx/media3/exoplayer/r;->g:Z

    iget-object v0, p0, Landroidx/media3/exoplayer/r;->a:Landroidx/media3/exoplayer/c3;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c3;->d()V

    return-void
.end method

.method public i(Z)J
    .locals 2

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/r;->j(Z)V

    invoke-virtual {p0}, Landroidx/media3/exoplayer/r;->getPositionUs()J

    move-result-wide v0

    return-wide v0
.end method

.method public final j(Z)V
    .locals 5

    invoke-virtual {p0, p1}, Landroidx/media3/exoplayer/r;->e(Z)Z

    move-result p1

    if-eqz p1, :cond_1

    const/4 p1, 0x1

    iput-boolean p1, p0, Landroidx/media3/exoplayer/r;->f:Z

    iget-boolean p1, p0, Landroidx/media3/exoplayer/r;->g:Z

    if-eqz p1, :cond_0

    iget-object p1, p0, Landroidx/media3/exoplayer/r;->a:Landroidx/media3/exoplayer/c3;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/c3;->c()V

    :cond_0
    return-void

    :cond_1
    iget-object p1, p0, Landroidx/media3/exoplayer/r;->d:Landroidx/media3/exoplayer/y1;

    invoke-static {p1}, Le2/a;->e(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/exoplayer/y1;

    invoke-interface {p1}, Landroidx/media3/exoplayer/y1;->getPositionUs()J

    move-result-wide v0

    iget-boolean v2, p0, Landroidx/media3/exoplayer/r;->f:Z

    if-eqz v2, :cond_3

    iget-object v2, p0, Landroidx/media3/exoplayer/r;->a:Landroidx/media3/exoplayer/c3;

    invoke-virtual {v2}, Landroidx/media3/exoplayer/c3;->getPositionUs()J

    move-result-wide v2

    cmp-long v4, v0, v2

    if-gez v4, :cond_2

    iget-object p1, p0, Landroidx/media3/exoplayer/r;->a:Landroidx/media3/exoplayer/c3;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/c3;->d()V

    return-void

    :cond_2
    const/4 v2, 0x1

    const/4 v2, 0x0

    iput-boolean v2, p0, Landroidx/media3/exoplayer/r;->f:Z

    iget-boolean v2, p0, Landroidx/media3/exoplayer/r;->g:Z

    if-eqz v2, :cond_3

    iget-object v2, p0, Landroidx/media3/exoplayer/r;->a:Landroidx/media3/exoplayer/c3;

    invoke-virtual {v2}, Landroidx/media3/exoplayer/c3;->c()V

    :cond_3
    iget-object v2, p0, Landroidx/media3/exoplayer/r;->a:Landroidx/media3/exoplayer/c3;

    invoke-virtual {v2, v0, v1}, Landroidx/media3/exoplayer/c3;->a(J)V

    invoke-interface {p1}, Landroidx/media3/exoplayer/y1;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object p1

    iget-object v0, p0, Landroidx/media3/exoplayer/r;->a:Landroidx/media3/exoplayer/c3;

    invoke-virtual {v0}, Landroidx/media3/exoplayer/c3;->getPlaybackParameters()Landroidx/media3/common/g0;

    move-result-object v0

    invoke-virtual {p1, v0}, Landroidx/media3/common/g0;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_4

    iget-object v0, p0, Landroidx/media3/exoplayer/r;->a:Landroidx/media3/exoplayer/c3;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/c3;->b(Landroidx/media3/common/g0;)V

    iget-object v0, p0, Landroidx/media3/exoplayer/r;->b:Landroidx/media3/exoplayer/r$a;

    invoke-interface {v0, p1}, Landroidx/media3/exoplayer/r$a;->onPlaybackParametersChanged(Landroidx/media3/common/g0;)V

    :cond_4
    return-void
.end method
