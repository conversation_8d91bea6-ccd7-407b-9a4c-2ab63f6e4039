.class public final Landroidx/media3/exoplayer/video/t;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/video/t$a;
    }
.end annotation


# instance fields
.field public final a:Landroidx/media3/exoplayer/video/t$a;

.field public final b:Landroidx/media3/exoplayer/video/p;

.field public final c:Landroidx/media3/exoplayer/video/p$a;

.field public final d:Le2/h0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Le2/h0<",
            "Landroidx/media3/common/t0;",
            ">;"
        }
    .end annotation
.end field

.field public final e:Le2/h0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Le2/h0<",
            "Ljava/lang/Long;",
            ">;"
        }
    .end annotation
.end field

.field public final f:Le2/q;

.field public g:Landroidx/media3/common/t0;

.field public h:J

.field public i:J


# direct methods
.method public constructor <init>(Landroidx/media3/exoplayer/video/t$a;Landroidx/media3/exoplayer/video/p;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/t;->a:Landroidx/media3/exoplayer/video/t$a;

    iput-object p2, p0, Landroidx/media3/exoplayer/video/t;->b:Landroidx/media3/exoplayer/video/p;

    new-instance p1, Landroidx/media3/exoplayer/video/p$a;

    invoke-direct {p1}, Landroidx/media3/exoplayer/video/p$a;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/t;->c:Landroidx/media3/exoplayer/video/p$a;

    new-instance p1, Le2/h0;

    invoke-direct {p1}, Le2/h0;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/t;->d:Le2/h0;

    new-instance p1, Le2/h0;

    invoke-direct {p1}, Le2/h0;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/t;->e:Le2/h0;

    new-instance p1, Le2/q;

    invoke-direct {p1}, Le2/q;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/video/t;->f:Le2/q;

    sget-object p1, Landroidx/media3/common/t0;->e:Landroidx/media3/common/t0;

    iput-object p1, p0, Landroidx/media3/exoplayer/video/t;->g:Landroidx/media3/common/t0;

    const-wide p1, -0x7fffffffffffffffL    # -4.9E-324

    iput-wide p1, p0, Landroidx/media3/exoplayer/video/t;->i:J

    return-void
.end method


# virtual methods
.method public final a()V
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/video/t;->f:Le2/q;

    invoke-virtual {v0}, Le2/q;->c()J

    move-result-wide v0

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Landroidx/media3/exoplayer/video/t;->a:Landroidx/media3/exoplayer/video/t$a;

    invoke-interface {v0}, Landroidx/media3/exoplayer/video/t$a;->b()V

    return-void
.end method

.method public b(J)Z
    .locals 5

    iget-wide v0, p0, Landroidx/media3/exoplayer/video/t;->i:J

    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v4, v0, v2

    if-eqz v4, :cond_0

    cmp-long v2, v0, p1

    if-ltz v2, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x1

    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public c()Z
    .locals 2

    iget-object v0, p0, Landroidx/media3/exoplayer/video/t;->b:Landroidx/media3/exoplayer/video/p;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroidx/media3/exoplayer/video/p;->d(Z)Z

    move-result v0

    return v0
.end method

.method public final d(J)Z
    .locals 4

    iget-object v0, p0, Landroidx/media3/exoplayer/video/t;->e:Le2/h0;

    invoke-virtual {v0, p1, p2}, Le2/h0;->j(J)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Long;

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Ljava/lang/Long;->longValue()J

    move-result-wide v0

    iget-wide v2, p0, Landroidx/media3/exoplayer/video/t;->h:J

    cmp-long p2, v0, v2

    if-eqz p2, :cond_0

    invoke-virtual {p1}, Ljava/lang/Long;->longValue()J

    move-result-wide p1

    iput-wide p1, p0, Landroidx/media3/exoplayer/video/t;->h:J

    const/4 p1, 0x1

    return p1

    :cond_0
    const/4 p1, 0x1

    const/4 p1, 0x0

    return p1
.end method

.method public final e(J)Z
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/t;->d:Le2/h0;

    invoke-virtual {v0, p1, p2}, Le2/h0;->j(J)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroidx/media3/common/t0;

    const/4 p2, 0x1

    const/4 p2, 0x0

    if-nez p1, :cond_0

    return p2

    :cond_0
    sget-object v0, Landroidx/media3/common/t0;->e:Landroidx/media3/common/t0;

    invoke-virtual {p1, v0}, Landroidx/media3/common/t0;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    iget-object v0, p0, Landroidx/media3/exoplayer/video/t;->g:Landroidx/media3/common/t0;

    invoke-virtual {p1, v0}, Landroidx/media3/common/t0;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    iput-object p1, p0, Landroidx/media3/exoplayer/video/t;->g:Landroidx/media3/common/t0;

    const/4 p1, 0x1

    return p1

    :cond_1
    return p2
.end method

.method public f(JJ)V
    .locals 15
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/ExoPlaybackException;
        }
    .end annotation

    move-object v0, p0

    :goto_0
    iget-object v1, v0, Landroidx/media3/exoplayer/video/t;->f:Le2/q;

    invoke-virtual {v1}, Le2/q;->b()Z

    move-result v1

    if-nez v1, :cond_5

    iget-object v1, v0, Landroidx/media3/exoplayer/video/t;->f:Le2/q;

    invoke-virtual {v1}, Le2/q;->a()J

    move-result-wide v13

    invoke-virtual {p0, v13, v14}, Landroidx/media3/exoplayer/video/t;->d(J)Z

    move-result v1

    if-eqz v1, :cond_0

    iget-object v1, v0, Landroidx/media3/exoplayer/video/t;->b:Landroidx/media3/exoplayer/video/p;

    invoke-virtual {v1}, Landroidx/media3/exoplayer/video/p;->j()V

    :cond_0
    iget-object v2, v0, Landroidx/media3/exoplayer/video/t;->b:Landroidx/media3/exoplayer/video/p;

    iget-wide v9, v0, Landroidx/media3/exoplayer/video/t;->h:J

    const/4 v11, 0x1

    const/4 v11, 0x0

    iget-object v12, v0, Landroidx/media3/exoplayer/video/t;->c:Landroidx/media3/exoplayer/video/p$a;

    move-wide v3, v13

    move-wide/from16 v5, p1

    move-wide/from16 v7, p3

    invoke-virtual/range {v2 .. v12}, Landroidx/media3/exoplayer/video/p;->c(JJJJZLandroidx/media3/exoplayer/video/p$a;)I

    move-result v1

    const/4 v2, 0x1

    if-eqz v1, :cond_3

    if-eq v1, v2, :cond_3

    const/4 v2, 0x2

    if-eq v1, v2, :cond_2

    const/4 v2, 0x3

    if-eq v1, v2, :cond_2

    const/4 v2, 0x4

    if-eq v1, v2, :cond_2

    const/4 v2, 0x5

    if-ne v1, v2, :cond_1

    return-void

    :cond_1
    new-instance v2, Ljava/lang/IllegalStateException;

    invoke-static {v1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v1

    invoke-direct {v2, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v2

    :cond_2
    iput-wide v13, v0, Landroidx/media3/exoplayer/video/t;->i:J

    invoke-virtual {p0}, Landroidx/media3/exoplayer/video/t;->a()V

    goto :goto_0

    :cond_3
    iput-wide v13, v0, Landroidx/media3/exoplayer/video/t;->i:J

    if-nez v1, :cond_4

    goto :goto_1

    :cond_4
    const/4 v2, 0x1

    const/4 v2, 0x0

    :goto_1
    invoke-virtual {p0, v2}, Landroidx/media3/exoplayer/video/t;->g(Z)V

    goto :goto_0

    :cond_5
    return-void
.end method

.method public final g(Z)V
    .locals 9

    iget-object v0, p0, Landroidx/media3/exoplayer/video/t;->f:Le2/q;

    invoke-virtual {v0}, Le2/q;->c()J

    move-result-wide v0

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Long;

    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    move-result-wide v4

    invoke-virtual {p0, v4, v5}, Landroidx/media3/exoplayer/video/t;->e(J)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Landroidx/media3/exoplayer/video/t;->a:Landroidx/media3/exoplayer/video/t$a;

    iget-object v1, p0, Landroidx/media3/exoplayer/video/t;->g:Landroidx/media3/common/t0;

    invoke-interface {v0, v1}, Landroidx/media3/exoplayer/video/t$a;->onVideoSizeChanged(Landroidx/media3/common/t0;)V

    :cond_0
    if-eqz p1, :cond_1

    const-wide/16 v0, -0x1

    :goto_0
    move-wide v2, v0

    goto :goto_1

    :cond_1
    iget-object p1, p0, Landroidx/media3/exoplayer/video/t;->c:Landroidx/media3/exoplayer/video/p$a;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/video/p$a;->g()J

    move-result-wide v0

    goto :goto_0

    :goto_1
    iget-object v1, p0, Landroidx/media3/exoplayer/video/t;->a:Landroidx/media3/exoplayer/video/t$a;

    iget-wide v6, p0, Landroidx/media3/exoplayer/video/t;->h:J

    iget-object p1, p0, Landroidx/media3/exoplayer/video/t;->b:Landroidx/media3/exoplayer/video/p;

    invoke-virtual {p1}, Landroidx/media3/exoplayer/video/p;->i()Z

    move-result v8

    invoke-interface/range {v1 .. v8}, Landroidx/media3/exoplayer/video/t$a;->i(JJJZ)V

    return-void
.end method

.method public h(F)V
    .locals 1

    const/4 v0, 0x1

    const/4 v0, 0x0

    cmpl-float v0, p1, v0

    if-lez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Le2/a;->a(Z)V

    iget-object v0, p0, Landroidx/media3/exoplayer/video/t;->b:Landroidx/media3/exoplayer/video/p;

    invoke-virtual {v0, p1}, Landroidx/media3/exoplayer/video/p;->r(F)V

    return-void
.end method
