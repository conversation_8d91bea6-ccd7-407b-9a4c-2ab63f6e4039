.class public interface abstract Lz2/y;
.super Ljava/lang/Object;


# static fields
.field public static final a:Lz2/y;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lz2/w;

    invoke-direct {v0}, Lz2/w;-><init>()V

    sput-object v0, Lz2/y;->a:Lz2/y;

    return-void
.end method


# virtual methods
.method public abstract a(Lt3/s$a;)Lz2/y;
.end method

.method public abstract b(Landroid/net/Uri;Ljava/util/Map;)[Lz2/s;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/net/Uri;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;>;)[",
            "Lz2/s;"
        }
    .end annotation
.end method

.method public abstract c(Z)Lz2/y;
.end method

.method public abstract createExtractors()[Lz2/s;
.end method
