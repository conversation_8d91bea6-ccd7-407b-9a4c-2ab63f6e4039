.class public final Landroidx/media3/exoplayer/util/b$c;
.super Ljava/lang/Object;

# interfaces
.implements Landroidx/media3/exoplayer/upstream/Loader$b;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/util/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroidx/media3/exoplayer/upstream/Loader$b<",
        "Landroidx/media3/exoplayer/upstream/Loader$d;",
        ">;"
    }
.end annotation


# instance fields
.field public final a:Landroidx/media3/exoplayer/util/b$b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field


# direct methods
.method public constructor <init>(Landroidx/media3/exoplayer/util/b$b;)V
    .locals 0
    .param p1    # Landroidx/media3/exoplayer/util/b$b;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/util/b$c;->a:Landroidx/media3/exoplayer/util/b$b;

    return-void
.end method


# virtual methods
.method public k(Landroidx/media3/exoplayer/upstream/Loader$d;JJLjava/io/IOException;I)Landroidx/media3/exoplayer/upstream/Loader$c;
    .locals 0

    iget-object p1, p0, Landroidx/media3/exoplayer/util/b$c;->a:Landroidx/media3/exoplayer/util/b$b;

    if-eqz p1, :cond_0

    invoke-interface {p1, p6}, Landroidx/media3/exoplayer/util/b$b;->a(Ljava/io/IOException;)V

    :cond_0
    sget-object p1, Landroidx/media3/exoplayer/upstream/Loader;->f:Landroidx/media3/exoplayer/upstream/Loader$c;

    return-object p1
.end method

.method public n(Landroidx/media3/exoplayer/upstream/Loader$d;JJ)V
    .locals 0

    iget-object p1, p0, Landroidx/media3/exoplayer/util/b$c;->a:Landroidx/media3/exoplayer/util/b$b;

    if-eqz p1, :cond_1

    invoke-static {}, Landroidx/media3/exoplayer/util/b;->k()Z

    move-result p1

    if-nez p1, :cond_0

    iget-object p1, p0, Landroidx/media3/exoplayer/util/b$c;->a:Landroidx/media3/exoplayer/util/b$b;

    new-instance p2, Ljava/io/IOException;

    new-instance p3, Ljava/util/ConcurrentModificationException;

    invoke-direct {p3}, Ljava/util/ConcurrentModificationException;-><init>()V

    invoke-direct {p2, p3}, Ljava/io/IOException;-><init>(Ljava/lang/Throwable;)V

    invoke-interface {p1, p2}, Landroidx/media3/exoplayer/util/b$b;->a(Ljava/io/IOException;)V

    goto :goto_0

    :cond_0
    iget-object p1, p0, Landroidx/media3/exoplayer/util/b$c;->a:Landroidx/media3/exoplayer/util/b$b;

    invoke-interface {p1}, Landroidx/media3/exoplayer/util/b$b;->b()V

    :cond_1
    :goto_0
    return-void
.end method

.method public o(Landroidx/media3/exoplayer/upstream/Loader$d;JJZ)V
    .locals 0

    return-void
.end method
