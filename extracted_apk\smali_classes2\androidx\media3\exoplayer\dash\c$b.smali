.class public final Landroidx/media3/exoplayer/dash/c$b;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/dash/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final a:Lv2/f;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final b:Lm2/j;

.field public final c:Lm2/b;

.field public final d:Ll2/f;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final e:J

.field public final f:J


# direct methods
.method public constructor <init>(JLm2/j;Lm2/b;Lv2/f;JLl2/f;)V
    .locals 0
    .param p5    # Lv2/f;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p8    # Ll2/f;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-wide p1, p0, Landroidx/media3/exoplayer/dash/c$b;->e:J

    iput-object p3, p0, Landroidx/media3/exoplayer/dash/c$b;->b:Lm2/j;

    iput-object p4, p0, Landroidx/media3/exoplayer/dash/c$b;->c:Lm2/b;

    iput-wide p6, p0, Landroidx/media3/exoplayer/dash/c$b;->f:J

    iput-object p5, p0, Landroidx/media3/exoplayer/dash/c$b;->a:Lv2/f;

    iput-object p8, p0, Landroidx/media3/exoplayer/dash/c$b;->d:Ll2/f;

    return-void
.end method

.method public static synthetic a(Landroidx/media3/exoplayer/dash/c$b;)J
    .locals 2

    iget-wide v0, p0, Landroidx/media3/exoplayer/dash/c$b;->e:J

    return-wide v0
.end method


# virtual methods
.method public b(JLm2/j;)Landroidx/media3/exoplayer/dash/c$b;
    .locals 20
    .annotation build Landroidx/annotation/CheckResult;
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/source/BehindLiveWindowException;
        }
    .end annotation

    move-object/from16 v0, p0

    move-wide/from16 v2, p1

    iget-object v1, v0, Landroidx/media3/exoplayer/dash/c$b;->b:Lm2/j;

    invoke-virtual {v1}, Lm2/j;->k()Ll2/f;

    move-result-object v9

    invoke-virtual/range {p3 .. p3}, Lm2/j;->k()Ll2/f;

    move-result-object v10

    if-nez v9, :cond_0

    new-instance v10, Landroidx/media3/exoplayer/dash/c$b;

    iget-object v5, v0, Landroidx/media3/exoplayer/dash/c$b;->c:Lm2/b;

    iget-object v6, v0, Landroidx/media3/exoplayer/dash/c$b;->a:Lv2/f;

    iget-wide v7, v0, Landroidx/media3/exoplayer/dash/c$b;->f:J

    move-object v1, v10

    move-wide/from16 v2, p1

    move-object/from16 v4, p3

    invoke-direct/range {v1 .. v9}, Landroidx/media3/exoplayer/dash/c$b;-><init>(JLm2/j;Lm2/b;Lv2/f;JLl2/f;)V

    return-object v10

    :cond_0
    invoke-interface {v9}, Ll2/f;->g()Z

    move-result v1

    if-nez v1, :cond_1

    new-instance v11, Landroidx/media3/exoplayer/dash/c$b;

    iget-object v5, v0, Landroidx/media3/exoplayer/dash/c$b;->c:Lm2/b;

    iget-object v6, v0, Landroidx/media3/exoplayer/dash/c$b;->a:Lv2/f;

    iget-wide v7, v0, Landroidx/media3/exoplayer/dash/c$b;->f:J

    move-object v1, v11

    move-wide/from16 v2, p1

    move-object/from16 v4, p3

    move-object v9, v10

    invoke-direct/range {v1 .. v9}, Landroidx/media3/exoplayer/dash/c$b;-><init>(JLm2/j;Lm2/b;Lv2/f;JLl2/f;)V

    return-object v11

    :cond_1
    invoke-interface {v9, v2, v3}, Ll2/f;->f(J)J

    move-result-wide v4

    const-wide/16 v6, 0x0

    cmp-long v1, v4, v6

    if-nez v1, :cond_2

    new-instance v11, Landroidx/media3/exoplayer/dash/c$b;

    iget-object v5, v0, Landroidx/media3/exoplayer/dash/c$b;->c:Lm2/b;

    iget-object v6, v0, Landroidx/media3/exoplayer/dash/c$b;->a:Lv2/f;

    iget-wide v7, v0, Landroidx/media3/exoplayer/dash/c$b;->f:J

    move-object v1, v11

    move-wide/from16 v2, p1

    move-object/from16 v4, p3

    move-object v9, v10

    invoke-direct/range {v1 .. v9}, Landroidx/media3/exoplayer/dash/c$b;-><init>(JLm2/j;Lm2/b;Lv2/f;JLl2/f;)V

    return-object v11

    :cond_2
    invoke-static {v10}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    invoke-interface {v9}, Ll2/f;->h()J

    move-result-wide v6

    invoke-interface {v9, v6, v7}, Ll2/f;->getTimeUs(J)J

    move-result-wide v11

    add-long/2addr v4, v6

    const-wide/16 v13, 0x1

    sub-long v13, v4, v13

    invoke-interface {v9, v13, v14}, Ll2/f;->getTimeUs(J)J

    move-result-wide v15

    invoke-interface {v9, v13, v14, v2, v3}, Ll2/f;->a(JJ)J

    move-result-wide v13

    add-long/2addr v15, v13

    invoke-interface {v10}, Ll2/f;->h()J

    move-result-wide v13

    move-object v1, v9

    invoke-interface {v10, v13, v14}, Ll2/f;->getTimeUs(J)J

    move-result-wide v8

    move-wide/from16 v17, v6

    iget-wide v6, v0, Landroidx/media3/exoplayer/dash/c$b;->f:J

    cmp-long v19, v15, v8

    if-nez v19, :cond_3

    :goto_0
    sub-long/2addr v4, v13

    add-long/2addr v6, v4

    :goto_1
    move-wide v7, v6

    goto :goto_2

    :cond_3
    if-ltz v19, :cond_5

    cmp-long v4, v8, v11

    if-gez v4, :cond_4

    invoke-interface {v10, v11, v12, v2, v3}, Ll2/f;->e(JJ)J

    move-result-wide v4

    sub-long v4, v4, v17

    sub-long/2addr v6, v4

    goto :goto_1

    :cond_4
    invoke-interface {v1, v8, v9, v2, v3}, Ll2/f;->e(JJ)J

    move-result-wide v4

    goto :goto_0

    :goto_2
    new-instance v11, Landroidx/media3/exoplayer/dash/c$b;

    iget-object v5, v0, Landroidx/media3/exoplayer/dash/c$b;->c:Lm2/b;

    iget-object v6, v0, Landroidx/media3/exoplayer/dash/c$b;->a:Lv2/f;

    move-object v1, v11

    move-wide/from16 v2, p1

    move-object/from16 v4, p3

    move-object v9, v10

    invoke-direct/range {v1 .. v9}, Landroidx/media3/exoplayer/dash/c$b;-><init>(JLm2/j;Lm2/b;Lv2/f;JLl2/f;)V

    return-object v11

    :cond_5
    new-instance v1, Landroidx/media3/exoplayer/source/BehindLiveWindowException;

    invoke-direct {v1}, Landroidx/media3/exoplayer/source/BehindLiveWindowException;-><init>()V

    throw v1
.end method

.method public c(Ll2/f;)Landroidx/media3/exoplayer/dash/c$b;
    .locals 10
    .annotation build Landroidx/annotation/CheckResult;
    .end annotation

    new-instance v9, Landroidx/media3/exoplayer/dash/c$b;

    iget-wide v1, p0, Landroidx/media3/exoplayer/dash/c$b;->e:J

    iget-object v3, p0, Landroidx/media3/exoplayer/dash/c$b;->b:Lm2/j;

    iget-object v4, p0, Landroidx/media3/exoplayer/dash/c$b;->c:Lm2/b;

    iget-object v5, p0, Landroidx/media3/exoplayer/dash/c$b;->a:Lv2/f;

    iget-wide v6, p0, Landroidx/media3/exoplayer/dash/c$b;->f:J

    move-object v0, v9

    move-object v8, p1

    invoke-direct/range {v0 .. v8}, Landroidx/media3/exoplayer/dash/c$b;-><init>(JLm2/j;Lm2/b;Lv2/f;JLl2/f;)V

    return-object v9
.end method

.method public d(Lm2/b;)Landroidx/media3/exoplayer/dash/c$b;
    .locals 10
    .annotation build Landroidx/annotation/CheckResult;
    .end annotation

    new-instance v9, Landroidx/media3/exoplayer/dash/c$b;

    iget-wide v1, p0, Landroidx/media3/exoplayer/dash/c$b;->e:J

    iget-object v3, p0, Landroidx/media3/exoplayer/dash/c$b;->b:Lm2/j;

    iget-object v5, p0, Landroidx/media3/exoplayer/dash/c$b;->a:Lv2/f;

    iget-wide v6, p0, Landroidx/media3/exoplayer/dash/c$b;->f:J

    iget-object v8, p0, Landroidx/media3/exoplayer/dash/c$b;->d:Ll2/f;

    move-object v0, v9

    move-object v4, p1

    invoke-direct/range {v0 .. v8}, Landroidx/media3/exoplayer/dash/c$b;-><init>(JLm2/j;Lm2/b;Lv2/f;JLl2/f;)V

    return-object v9
.end method

.method public e(J)J
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/c$b;->d:Ll2/f;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ll2/f;

    iget-wide v1, p0, Landroidx/media3/exoplayer/dash/c$b;->e:J

    invoke-interface {v0, v1, v2, p1, p2}, Ll2/f;->b(JJ)J

    move-result-wide p1

    iget-wide v0, p0, Landroidx/media3/exoplayer/dash/c$b;->f:J

    add-long/2addr p1, v0

    return-wide p1
.end method

.method public f()J
    .locals 4

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/c$b;->d:Ll2/f;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ll2/f;

    invoke-interface {v0}, Ll2/f;->h()J

    move-result-wide v0

    iget-wide v2, p0, Landroidx/media3/exoplayer/dash/c$b;->f:J

    add-long/2addr v0, v2

    return-wide v0
.end method

.method public g(J)J
    .locals 5

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/dash/c$b;->e(J)J

    move-result-wide v0

    iget-object v2, p0, Landroidx/media3/exoplayer/dash/c$b;->d:Ll2/f;

    invoke-static {v2}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ll2/f;

    iget-wide v3, p0, Landroidx/media3/exoplayer/dash/c$b;->e:J

    invoke-interface {v2, v3, v4, p1, p2}, Ll2/f;->i(JJ)J

    move-result-wide p1

    add-long/2addr v0, p1

    const-wide/16 p1, 0x1

    sub-long/2addr v0, p1

    return-wide v0
.end method

.method public h()J
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/c$b;->d:Ll2/f;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ll2/f;

    iget-wide v1, p0, Landroidx/media3/exoplayer/dash/c$b;->e:J

    invoke-interface {v0, v1, v2}, Ll2/f;->f(J)J

    move-result-wide v0

    return-wide v0
.end method

.method public i(J)J
    .locals 5

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/dash/c$b;->k(J)J

    move-result-wide v0

    iget-object v2, p0, Landroidx/media3/exoplayer/dash/c$b;->d:Ll2/f;

    invoke-static {v2}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ll2/f;

    iget-wide v3, p0, Landroidx/media3/exoplayer/dash/c$b;->f:J

    sub-long/2addr p1, v3

    iget-wide v3, p0, Landroidx/media3/exoplayer/dash/c$b;->e:J

    invoke-interface {v2, p1, p2, v3, v4}, Ll2/f;->a(JJ)J

    move-result-wide p1

    add-long/2addr v0, p1

    return-wide v0
.end method

.method public j(J)J
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/c$b;->d:Ll2/f;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ll2/f;

    iget-wide v1, p0, Landroidx/media3/exoplayer/dash/c$b;->e:J

    invoke-interface {v0, p1, p2, v1, v2}, Ll2/f;->e(JJ)J

    move-result-wide p1

    iget-wide v0, p0, Landroidx/media3/exoplayer/dash/c$b;->f:J

    add-long/2addr p1, v0

    return-wide p1
.end method

.method public k(J)J
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/c$b;->d:Ll2/f;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ll2/f;

    iget-wide v1, p0, Landroidx/media3/exoplayer/dash/c$b;->f:J

    sub-long/2addr p1, v1

    invoke-interface {v0, p1, p2}, Ll2/f;->getTimeUs(J)J

    move-result-wide p1

    return-wide p1
.end method

.method public l(J)Lm2/i;
    .locals 3

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/c$b;->d:Ll2/f;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ll2/f;

    iget-wide v1, p0, Landroidx/media3/exoplayer/dash/c$b;->f:J

    sub-long/2addr p1, v1

    invoke-interface {v0, p1, p2}, Ll2/f;->d(J)Lm2/i;

    move-result-object p1

    return-object p1
.end method

.method public m(JJ)Z
    .locals 4

    iget-object v0, p0, Landroidx/media3/exoplayer/dash/c$b;->d:Ll2/f;

    invoke-static {v0}, Le2/a;->i(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ll2/f;

    invoke-interface {v0}, Ll2/f;->g()Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    return v1

    :cond_0
    const-wide v2, -0x7fffffffffffffffL    # -4.9E-324

    cmp-long v0, p3, v2

    if-eqz v0, :cond_2

    invoke-virtual {p0, p1, p2}, Landroidx/media3/exoplayer/dash/c$b;->i(J)J

    move-result-wide p1

    cmp-long v0, p1, p3

    if-gtz v0, :cond_1

    goto :goto_0

    :cond_1
    const/4 v1, 0x1

    const/4 v1, 0x0

    :cond_2
    :goto_0
    return v1
.end method
