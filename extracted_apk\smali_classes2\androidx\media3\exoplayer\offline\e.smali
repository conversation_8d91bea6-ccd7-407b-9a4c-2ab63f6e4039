.class public interface abstract Landroidx/media3/exoplayer/offline/e;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/io/Closeable;


# virtual methods
.method public abstract N()Landroidx/media3/exoplayer/offline/c;
.end method

.method public abstract close()V
.end method

.method public abstract getPosition()I
.end method

.method public abstract moveToNext()Z
.end method

.method public abstract moveToPosition(I)Z
.end method
