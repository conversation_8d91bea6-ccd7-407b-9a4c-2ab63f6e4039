.class public Landroidx/media3/common/p0$c;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/common/p0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "c"
.end annotation


# instance fields
.field public A:Ljava/util/HashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashMap<",
            "Landroidx/media3/common/n0;",
            "Landroidx/media3/common/o0;",
            ">;"
        }
    .end annotation
.end field

.field public B:Ljava/util/HashSet;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashSet<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field

.field public a:I

.field public b:I

.field public c:I

.field public d:I

.field public e:I

.field public f:I

.field public g:I

.field public h:I

.field public i:I

.field public j:I

.field public k:Z

.field public l:Lcom/google/common/collect/ImmutableList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/common/collect/ImmutableList<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public m:I

.field public n:Lcom/google/common/collect/ImmutableList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/common/collect/ImmutableList<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public o:I

.field public p:I

.field public q:I

.field public r:Lcom/google/common/collect/ImmutableList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/common/collect/ImmutableList<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public s:Landroidx/media3/common/p0$b;

.field public t:Lcom/google/common/collect/ImmutableList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/google/common/collect/ImmutableList<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field public u:I

.field public v:I

.field public w:Z

.field public x:Z

.field public y:Z

.field public z:Z


# direct methods
.method public constructor <init>()V
    .locals 3
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const v0, 0x7fffffff

    iput v0, p0, Landroidx/media3/common/p0$c;->a:I

    iput v0, p0, Landroidx/media3/common/p0$c;->b:I

    iput v0, p0, Landroidx/media3/common/p0$c;->c:I

    iput v0, p0, Landroidx/media3/common/p0$c;->d:I

    iput v0, p0, Landroidx/media3/common/p0$c;->i:I

    iput v0, p0, Landroidx/media3/common/p0$c;->j:I

    const/4 v1, 0x1

    iput-boolean v1, p0, Landroidx/media3/common/p0$c;->k:Z

    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object v1

    iput-object v1, p0, Landroidx/media3/common/p0$c;->l:Lcom/google/common/collect/ImmutableList;

    const/4 v1, 0x1

    const/4 v1, 0x0

    iput v1, p0, Landroidx/media3/common/p0$c;->m:I

    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object v2

    iput-object v2, p0, Landroidx/media3/common/p0$c;->n:Lcom/google/common/collect/ImmutableList;

    iput v1, p0, Landroidx/media3/common/p0$c;->o:I

    iput v0, p0, Landroidx/media3/common/p0$c;->p:I

    iput v0, p0, Landroidx/media3/common/p0$c;->q:I

    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/p0$c;->r:Lcom/google/common/collect/ImmutableList;

    sget-object v0, Landroidx/media3/common/p0$b;->d:Landroidx/media3/common/p0$b;

    iput-object v0, p0, Landroidx/media3/common/p0$c;->s:Landroidx/media3/common/p0$b;

    invoke-static {}, Lcom/google/common/collect/ImmutableList;->of()Lcom/google/common/collect/ImmutableList;

    move-result-object v0

    iput-object v0, p0, Landroidx/media3/common/p0$c;->t:Lcom/google/common/collect/ImmutableList;

    iput v1, p0, Landroidx/media3/common/p0$c;->u:I

    iput v1, p0, Landroidx/media3/common/p0$c;->v:I

    iput-boolean v1, p0, Landroidx/media3/common/p0$c;->w:Z

    iput-boolean v1, p0, Landroidx/media3/common/p0$c;->x:Z

    iput-boolean v1, p0, Landroidx/media3/common/p0$c;->y:Z

    iput-boolean v1, p0, Landroidx/media3/common/p0$c;->z:Z

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Landroidx/media3/common/p0$c;->A:Ljava/util/HashMap;

    new-instance v0, Ljava/util/HashSet;

    invoke-direct {v0}, Ljava/util/HashSet;-><init>()V

    iput-object v0, p0, Landroidx/media3/common/p0$c;->B:Ljava/util/HashSet;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    invoke-direct {p0}, Landroidx/media3/common/p0$c;-><init>()V

    invoke-virtual {p0, p1}, Landroidx/media3/common/p0$c;->I(Landroid/content/Context;)Landroidx/media3/common/p0$c;

    const/4 v0, 0x1

    invoke-virtual {p0, p1, v0}, Landroidx/media3/common/p0$c;->M(Landroid/content/Context;Z)Landroidx/media3/common/p0$c;

    return-void
.end method

.method public constructor <init>(Landroidx/media3/common/p0;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    invoke-virtual {p0, p1}, Landroidx/media3/common/p0$c;->E(Landroidx/media3/common/p0;)V

    return-void
.end method

.method public static synthetic A(Landroidx/media3/common/p0$c;)Ljava/util/HashMap;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/p0$c;->A:Ljava/util/HashMap;

    return-object p0
.end method

.method public static synthetic B(Landroidx/media3/common/p0$c;)Ljava/util/HashSet;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/p0$c;->B:Ljava/util/HashSet;

    return-object p0
.end method

.method public static synthetic a(Landroidx/media3/common/p0$c;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/p0$c;->a:I

    return p0
.end method

.method public static synthetic b(Landroidx/media3/common/p0$c;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/p0$c;->b:I

    return p0
.end method

.method public static synthetic c(Landroidx/media3/common/p0$c;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/p0$c;->c:I

    return p0
.end method

.method public static synthetic d(Landroidx/media3/common/p0$c;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/p0$c;->d:I

    return p0
.end method

.method public static synthetic e(Landroidx/media3/common/p0$c;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/p0$c;->e:I

    return p0
.end method

.method public static synthetic f(Landroidx/media3/common/p0$c;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/p0$c;->f:I

    return p0
.end method

.method public static synthetic g(Landroidx/media3/common/p0$c;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/p0$c;->g:I

    return p0
.end method

.method public static synthetic h(Landroidx/media3/common/p0$c;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/p0$c;->h:I

    return p0
.end method

.method public static synthetic i(Landroidx/media3/common/p0$c;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/p0$c;->i:I

    return p0
.end method

.method public static synthetic j(Landroidx/media3/common/p0$c;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/p0$c;->j:I

    return p0
.end method

.method public static synthetic k(Landroidx/media3/common/p0$c;)Z
    .locals 0

    iget-boolean p0, p0, Landroidx/media3/common/p0$c;->k:Z

    return p0
.end method

.method public static synthetic l(Landroidx/media3/common/p0$c;)Lcom/google/common/collect/ImmutableList;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/p0$c;->l:Lcom/google/common/collect/ImmutableList;

    return-object p0
.end method

.method public static synthetic m(Landroidx/media3/common/p0$c;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/p0$c;->m:I

    return p0
.end method

.method public static synthetic n(Landroidx/media3/common/p0$c;)Lcom/google/common/collect/ImmutableList;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/p0$c;->n:Lcom/google/common/collect/ImmutableList;

    return-object p0
.end method

.method public static synthetic o(Landroidx/media3/common/p0$c;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/p0$c;->o:I

    return p0
.end method

.method public static synthetic p(Landroidx/media3/common/p0$c;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/p0$c;->p:I

    return p0
.end method

.method public static synthetic q(Landroidx/media3/common/p0$c;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/p0$c;->q:I

    return p0
.end method

.method public static synthetic r(Landroidx/media3/common/p0$c;)Lcom/google/common/collect/ImmutableList;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/p0$c;->r:Lcom/google/common/collect/ImmutableList;

    return-object p0
.end method

.method public static synthetic s(Landroidx/media3/common/p0$c;)Landroidx/media3/common/p0$b;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/p0$c;->s:Landroidx/media3/common/p0$b;

    return-object p0
.end method

.method public static synthetic t(Landroidx/media3/common/p0$c;)Lcom/google/common/collect/ImmutableList;
    .locals 0

    iget-object p0, p0, Landroidx/media3/common/p0$c;->t:Lcom/google/common/collect/ImmutableList;

    return-object p0
.end method

.method public static synthetic u(Landroidx/media3/common/p0$c;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/p0$c;->u:I

    return p0
.end method

.method public static synthetic v(Landroidx/media3/common/p0$c;)I
    .locals 0

    iget p0, p0, Landroidx/media3/common/p0$c;->v:I

    return p0
.end method

.method public static synthetic w(Landroidx/media3/common/p0$c;)Z
    .locals 0

    iget-boolean p0, p0, Landroidx/media3/common/p0$c;->w:Z

    return p0
.end method

.method public static synthetic x(Landroidx/media3/common/p0$c;)Z
    .locals 0

    iget-boolean p0, p0, Landroidx/media3/common/p0$c;->x:Z

    return p0
.end method

.method public static synthetic y(Landroidx/media3/common/p0$c;)Z
    .locals 0

    iget-boolean p0, p0, Landroidx/media3/common/p0$c;->y:Z

    return p0
.end method

.method public static synthetic z(Landroidx/media3/common/p0$c;)Z
    .locals 0

    iget-boolean p0, p0, Landroidx/media3/common/p0$c;->z:Z

    return p0
.end method


# virtual methods
.method public C()Landroidx/media3/common/p0;
    .locals 1

    new-instance v0, Landroidx/media3/common/p0;

    invoke-direct {v0, p0}, Landroidx/media3/common/p0;-><init>(Landroidx/media3/common/p0$c;)V

    return-object v0
.end method

.method public D(I)Landroidx/media3/common/p0$c;
    .locals 2

    iget-object v0, p0, Landroidx/media3/common/p0$c;->A:Ljava/util/HashMap;

    invoke-virtual {v0}, Ljava/util/HashMap;->values()Ljava/util/Collection;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroidx/media3/common/o0;

    invoke-virtual {v1}, Landroidx/media3/common/o0;->a()I

    move-result v1

    if-ne v1, p1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->remove()V

    goto :goto_0

    :cond_1
    return-object p0
.end method

.method public final E(Landroidx/media3/common/p0;)V
    .locals 2

    iget v0, p1, Landroidx/media3/common/p0;->a:I

    iput v0, p0, Landroidx/media3/common/p0$c;->a:I

    iget v0, p1, Landroidx/media3/common/p0;->b:I

    iput v0, p0, Landroidx/media3/common/p0$c;->b:I

    iget v0, p1, Landroidx/media3/common/p0;->c:I

    iput v0, p0, Landroidx/media3/common/p0$c;->c:I

    iget v0, p1, Landroidx/media3/common/p0;->d:I

    iput v0, p0, Landroidx/media3/common/p0$c;->d:I

    iget v0, p1, Landroidx/media3/common/p0;->e:I

    iput v0, p0, Landroidx/media3/common/p0$c;->e:I

    iget v0, p1, Landroidx/media3/common/p0;->f:I

    iput v0, p0, Landroidx/media3/common/p0$c;->f:I

    iget v0, p1, Landroidx/media3/common/p0;->g:I

    iput v0, p0, Landroidx/media3/common/p0$c;->g:I

    iget v0, p1, Landroidx/media3/common/p0;->h:I

    iput v0, p0, Landroidx/media3/common/p0$c;->h:I

    iget v0, p1, Landroidx/media3/common/p0;->i:I

    iput v0, p0, Landroidx/media3/common/p0$c;->i:I

    iget v0, p1, Landroidx/media3/common/p0;->j:I

    iput v0, p0, Landroidx/media3/common/p0$c;->j:I

    iget-boolean v0, p1, Landroidx/media3/common/p0;->k:Z

    iput-boolean v0, p0, Landroidx/media3/common/p0$c;->k:Z

    iget-object v0, p1, Landroidx/media3/common/p0;->l:Lcom/google/common/collect/ImmutableList;

    iput-object v0, p0, Landroidx/media3/common/p0$c;->l:Lcom/google/common/collect/ImmutableList;

    iget v0, p1, Landroidx/media3/common/p0;->m:I

    iput v0, p0, Landroidx/media3/common/p0$c;->m:I

    iget-object v0, p1, Landroidx/media3/common/p0;->n:Lcom/google/common/collect/ImmutableList;

    iput-object v0, p0, Landroidx/media3/common/p0$c;->n:Lcom/google/common/collect/ImmutableList;

    iget v0, p1, Landroidx/media3/common/p0;->o:I

    iput v0, p0, Landroidx/media3/common/p0$c;->o:I

    iget v0, p1, Landroidx/media3/common/p0;->p:I

    iput v0, p0, Landroidx/media3/common/p0$c;->p:I

    iget v0, p1, Landroidx/media3/common/p0;->q:I

    iput v0, p0, Landroidx/media3/common/p0$c;->q:I

    iget-object v0, p1, Landroidx/media3/common/p0;->r:Lcom/google/common/collect/ImmutableList;

    iput-object v0, p0, Landroidx/media3/common/p0$c;->r:Lcom/google/common/collect/ImmutableList;

    iget-object v0, p1, Landroidx/media3/common/p0;->s:Landroidx/media3/common/p0$b;

    iput-object v0, p0, Landroidx/media3/common/p0$c;->s:Landroidx/media3/common/p0$b;

    iget-object v0, p1, Landroidx/media3/common/p0;->t:Lcom/google/common/collect/ImmutableList;

    iput-object v0, p0, Landroidx/media3/common/p0$c;->t:Lcom/google/common/collect/ImmutableList;

    iget v0, p1, Landroidx/media3/common/p0;->u:I

    iput v0, p0, Landroidx/media3/common/p0$c;->u:I

    iget v0, p1, Landroidx/media3/common/p0;->v:I

    iput v0, p0, Landroidx/media3/common/p0$c;->v:I

    iget-boolean v0, p1, Landroidx/media3/common/p0;->w:Z

    iput-boolean v0, p0, Landroidx/media3/common/p0$c;->w:Z

    iget-boolean v0, p1, Landroidx/media3/common/p0;->x:Z

    iput-boolean v0, p0, Landroidx/media3/common/p0$c;->x:Z

    iget-boolean v0, p1, Landroidx/media3/common/p0;->y:Z

    iput-boolean v0, p0, Landroidx/media3/common/p0$c;->y:Z

    iget-boolean v0, p1, Landroidx/media3/common/p0;->z:Z

    iput-boolean v0, p0, Landroidx/media3/common/p0$c;->z:Z

    new-instance v0, Ljava/util/HashSet;

    iget-object v1, p1, Landroidx/media3/common/p0;->B:Lcom/google/common/collect/ImmutableSet;

    invoke-direct {v0, v1}, Ljava/util/HashSet;-><init>(Ljava/util/Collection;)V

    iput-object v0, p0, Landroidx/media3/common/p0$c;->B:Ljava/util/HashSet;

    new-instance v0, Ljava/util/HashMap;

    iget-object p1, p1, Landroidx/media3/common/p0;->A:Lcom/google/common/collect/ImmutableMap;

    invoke-direct {v0, p1}, Ljava/util/HashMap;-><init>(Ljava/util/Map;)V

    iput-object v0, p0, Landroidx/media3/common/p0$c;->A:Ljava/util/HashMap;

    return-void
.end method

.method public F(Landroidx/media3/common/p0;)Landroidx/media3/common/p0$c;
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/common/p0$c;->E(Landroidx/media3/common/p0;)V

    return-object p0
.end method

.method public G(I)Landroidx/media3/common/p0$c;
    .locals 0

    iput p1, p0, Landroidx/media3/common/p0$c;->v:I

    return-object p0
.end method

.method public H(Landroidx/media3/common/o0;)Landroidx/media3/common/p0$c;
    .locals 2

    invoke-virtual {p1}, Landroidx/media3/common/o0;->a()I

    move-result v0

    invoke-virtual {p0, v0}, Landroidx/media3/common/p0$c;->D(I)Landroidx/media3/common/p0$c;

    iget-object v0, p0, Landroidx/media3/common/p0$c;->A:Ljava/util/HashMap;

    iget-object v1, p1, Landroidx/media3/common/o0;->a:Landroidx/media3/common/n0;

    invoke-virtual {v0, v1, p1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object p0
.end method

.method public I(Landroid/content/Context;)Landroidx/media3/common/p0$c;
    .locals 2

    sget v0, Le2/u0;->a:I

    const/16 v1, 0x13

    if-lt v0, v1, :cond_0

    invoke-virtual {p0, p1}, Landroidx/media3/common/p0$c;->J(Landroid/content/Context;)V

    :cond_0
    return-object p0
.end method

.method public final J(Landroid/content/Context;)V
    .locals 2
    .annotation build Landroidx/annotation/RequiresApi;
        value = 0x13
    .end annotation

    sget v0, Le2/u0;->a:I

    const/16 v1, 0x17

    if-ge v0, v1, :cond_0

    invoke-static {}, Landroid/os/Looper;->myLooper()Landroid/os/Looper;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    const-string v0, "captioning"

    invoke-virtual {p1, v0}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Landroid/view/accessibility/CaptioningManager;

    if-eqz p1, :cond_2

    invoke-virtual {p1}, Landroid/view/accessibility/CaptioningManager;->isEnabled()Z

    move-result v0

    if-nez v0, :cond_1

    goto :goto_0

    :cond_1
    const/16 v0, 0x440

    iput v0, p0, Landroidx/media3/common/p0$c;->u:I

    invoke-virtual {p1}, Landroid/view/accessibility/CaptioningManager;->getLocale()Ljava/util/Locale;

    move-result-object p1

    if-eqz p1, :cond_2

    invoke-static {p1}, Le2/u0;->e0(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lcom/google/common/collect/ImmutableList;->of(Ljava/lang/Object;)Lcom/google/common/collect/ImmutableList;

    move-result-object p1

    iput-object p1, p0, Landroidx/media3/common/p0$c;->t:Lcom/google/common/collect/ImmutableList;

    :cond_2
    :goto_0
    return-void
.end method

.method public K(IZ)Landroidx/media3/common/p0$c;
    .locals 0

    if-eqz p2, :cond_0

    iget-object p2, p0, Landroidx/media3/common/p0$c;->B:Ljava/util/HashSet;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_0
    iget-object p2, p0, Landroidx/media3/common/p0$c;->B:Ljava/util/HashSet;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/util/HashSet;->remove(Ljava/lang/Object;)Z

    :goto_0
    return-object p0
.end method

.method public L(IIZ)Landroidx/media3/common/p0$c;
    .locals 0

    iput p1, p0, Landroidx/media3/common/p0$c;->i:I

    iput p2, p0, Landroidx/media3/common/p0$c;->j:I

    iput-boolean p3, p0, Landroidx/media3/common/p0$c;->k:Z

    return-object p0
.end method

.method public M(Landroid/content/Context;Z)Landroidx/media3/common/p0$c;
    .locals 1

    invoke-static {p1}, Le2/u0;->T(Landroid/content/Context;)Landroid/graphics/Point;

    move-result-object p1

    iget v0, p1, Landroid/graphics/Point;->x:I

    iget p1, p1, Landroid/graphics/Point;->y:I

    invoke-virtual {p0, v0, p1, p2}, Landroidx/media3/common/p0$c;->L(IIZ)Landroidx/media3/common/p0$c;

    move-result-object p1

    return-object p1
.end method
