.class public interface abstract Landroidx/media3/exoplayer/upstream/m;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/upstream/m$b;,
        Landroidx/media3/exoplayer/upstream/m$a;,
        Landroidx/media3/exoplayer/upstream/m$c;
    }
.end annotation


# virtual methods
.method public abstract a(I)I
.end method

.method public abstract b(J)V
.end method

.method public abstract c(Landroidx/media3/exoplayer/upstream/m$c;)J
.end method

.method public abstract d(Landroidx/media3/exoplayer/upstream/m$a;Landroidx/media3/exoplayer/upstream/m$c;)Landroidx/media3/exoplayer/upstream/m$b;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end method
