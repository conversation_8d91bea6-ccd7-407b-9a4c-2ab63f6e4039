.class public final Landroidx/compose/ui/window/g;
.super Landroidx/compose/ui/window/h;


# annotations
.annotation build Landroidx/annotation/RequiresApi;
    value = 0x1d
.end annotation

.annotation runtime L<PERSON>lin/Metadata;
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Landroidx/compose/ui/window/h;-><init>()V

    return-void
.end method
