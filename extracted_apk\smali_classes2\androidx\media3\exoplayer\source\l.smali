.class public interface abstract Landroidx/media3/exoplayer/source/l;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/source/l$c;,
        Landroidx/media3/exoplayer/source/l$b;,
        Landroidx/media3/exoplayer/source/l$a;
    }
.end annotation


# virtual methods
.method public abstract a()Landroidx/media3/common/b0;
.end method

.method public abstract c()Z
.end method

.method public abstract d()Landroidx/media3/common/m0;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end method

.method public abstract e(Landroid/os/Handler;Landroidx/media3/exoplayer/source/m;)V
.end method

.method public abstract f(Landroidx/media3/exoplayer/source/m;)V
.end method

.method public abstract h(Landroidx/media3/exoplayer/source/l$b;Landroidx/media3/exoplayer/upstream/b;J)Landroidx/media3/exoplayer/source/k;
.end method

.method public abstract i(Landroidx/media3/exoplayer/source/l$c;Lh2/o;Lj2/x3;)V
    .param p2    # Lh2/o;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract j(Landroid/os/Handler;Landroidx/media3/exoplayer/drm/b;)V
.end method

.method public abstract k(Landroidx/media3/exoplayer/drm/b;)V
.end method

.method public abstract l(Landroidx/media3/exoplayer/source/k;)V
.end method

.method public abstract m(Landroidx/media3/exoplayer/source/l$c;)V
.end method

.method public abstract maybeThrowSourceInfoRefreshError()V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract n(Landroidx/media3/common/b0;)V
.end method

.method public abstract o(Landroidx/media3/exoplayer/source/l$c;)V
.end method

.method public abstract p(Landroidx/media3/exoplayer/source/l$c;)V
.end method

.method public abstract q(Landroidx/media3/common/b0;)Z
.end method
