.class public final synthetic Landroidx/media3/exoplayer/upstream/i;
.super Ljava/lang/Object;

# interfaces
.implements Le2/t$c;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/upstream/j;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/upstream/j;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/upstream/i;->a:Landroidx/media3/exoplayer/upstream/j;

    return-void
.end method


# virtual methods
.method public final a(I)V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/upstream/i;->a:Landroidx/media3/exoplayer/upstream/j;

    invoke-static {v0, p1}, Landroidx/media3/exoplayer/upstream/j;->i(Landroidx/media3/exoplayer/upstream/j;I)V

    return-void
.end method
