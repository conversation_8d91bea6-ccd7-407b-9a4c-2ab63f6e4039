<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.hisavana.mediation.ad.TStoreMarkView android:id="@id/store_mark_view" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_alignParentTop="true" android:layout_alignParentStart="true" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <com.hisavana.mediation.ad.TAdChoicesView android:id="@id/adChoicesView" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="@id/store_mark_view" app:layout_constraintStart_toEndOf="@id/store_mark_view" app:layout_constraintTop_toTopOf="@id/store_mark_view" />
    <androidx.cardview.widget.CardView android:id="@id/ad_card_view" android:layout_width="wrap_content" android:layout_height="wrap_content" app:cardCornerRadius="12.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/store_mark_view">
        <com.hisavana.mediation.ad.TIconView android:id="@id/native_ad_icon" android:layout_width="82.0dip" android:layout_height="82.0dip" />
    </androidx.cardview.widget.CardView>
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:id="@id/native_ad_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginBottom="5.0dip" android:lines="2" android:layout_marginStart="8.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toEndOf="@id/ad_card_view" app:layout_constraintStart_toStartOf="@id/ad_card_view" app:layout_constraintTop_toBottomOf="@id/ad_card_view" style="@style/robot_medium" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/text_02" android:ellipsize="end" android:id="@id/native_ad_body" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="12.0dip" android:maxLines="1" android:layout_marginStart="8.0dip" android:layout_marginEnd="4.0dip" app:layout_constraintEnd_toEndOf="@id/ad_card_view" app:layout_constraintStart_toStartOf="@id/ad_card_view" app:layout_constraintTop_toBottomOf="@id/native_ad_title" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="14.0sp" android:textColor="@color/white" android:gravity="center" android:layout_gravity="center_vertical" android:id="@id/call_to_action" android:background="@drawable/libui_main_btn_normal" android:layout_width="82.0dip" android:layout_height="32.0dip" android:layout_marginTop="4.0dip" android:textAllCaps="false" app:layout_constraintEnd_toEndOf="@id/ad_card_view" app:layout_constraintStart_toStartOf="@id/ad_card_view" app:layout_constraintTop_toBottomOf="@id/native_ad_body" />
</androidx.constraintlayout.widget.ConstraintLayout>
