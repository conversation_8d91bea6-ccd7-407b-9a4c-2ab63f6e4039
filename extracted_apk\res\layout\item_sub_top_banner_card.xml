<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginEnd="@dimen/dp_8"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.google.android.material.imageview.ShapeableImageView android:id="@id/bg" android:layout_width="fill_parent" android:layout_height="48.0dip" android:src="@drawable/bg_blur" android:scaleType="centerCrop" app:layout_constraintBottom_toBottomOf="parent" app:shapeAppearanceOverlay="@style/roundStyle_4" />
    <com.transsion.baseui.widget.RoundedConstraintLayout android:id="@id/contentIVContainer" android:layout_width="40.0dip" android:layout_height="56.0dip" android:layout_marginBottom="8.0dip" android:layout_marginStart="8.0dip" app:cornerRadius="4.0dip" app:layout_constraintBottom_toBottomOf="@id/bg" app:layout_constraintStart_toStartOf="parent">
        <ImageView android:id="@id/contentIV" android:layout_width="40.0dip" android:layout_height="56.0dip" android:scaleType="centerCrop" />
    </com.transsion.baseui.widget.RoundedConstraintLayout>
    <TextView android:textSize="@dimen/text_size_14" android:textColor="@color/white_80" android:ellipsize="end" android:id="@id/titleTV" android:paddingLeft="@dimen/dp_4" android:paddingTop="8.0dip" android:paddingRight="@dimen/dp_4" android:paddingBottom="0.0dip" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:includeFontPadding="false" app:layout_constraintBottom_toTopOf="@id/desTV" app:layout_constraintEnd_toStartOf="@id/playIV" app:layout_constraintStart_toEndOf="@id/contentIVContainer" app:layout_constraintTop_toTopOf="@id/bg" app:layout_constraintVertical_chainStyle="packed" style="@style/style_medium_text" />
    <TextView android:textSize="@dimen/text_size_12" android:textColor="@color/white_60" android:ellipsize="end" android:gravity="bottom" android:id="@id/desTV" android:paddingLeft="@dimen/dp_4" android:paddingRight="@dimen/dp_4" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:includeFontPadding="false" app:layout_constraintBottom_toBottomOf="@id/contentIVContainer" app:layout_constraintEnd_toStartOf="@id/playIV" app:layout_constraintStart_toEndOf="@id/contentIVContainer" app:layout_constraintTop_toBottomOf="@id/titleTV" style="@style/style_medium_text" />
    <ImageView android:id="@id/playIV" android:layout_width="24.0dip" android:layout_height="24.0dip" android:src="@drawable/ic_trending_play" android:layout_marginEnd="8.0dip" app:layout_constraintBottom_toBottomOf="@id/bg" app:layout_constraintEnd_toEndOf="@id/bg" app:layout_constraintTop_toTopOf="@id/bg" />
</androidx.constraintlayout.widget.ConstraintLayout>
