.class public interface abstract Landroidx/media3/exoplayer/video/f0;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/video/f0$a;
    }
.end annotation


# virtual methods
.method public abstract B(Landroidx/media3/common/y;)V
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end method

.method public abstract b(Ljava/lang/String;)V
.end method

.method public abstract e(Ljava/lang/Exception;)V
.end method

.method public abstract g(Ljava/lang/Object;J)V
.end method

.method public abstract j(JI)V
.end method

.method public abstract o(Landroidx/media3/exoplayer/n;)V
.end method

.method public abstract onDroppedFrames(IJ)V
.end method

.method public abstract onVideoDecoderInitialized(Ljava/lang/String;JJ)V
.end method

.method public abstract onVideoSizeChanged(Landroidx/media3/common/t0;)V
.end method

.method public abstract w(Landroidx/media3/common/y;Landroidx/media3/exoplayer/o;)V
    .param p2    # Landroidx/media3/exoplayer/o;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract x(Landroidx/media3/exoplayer/n;)V
.end method
