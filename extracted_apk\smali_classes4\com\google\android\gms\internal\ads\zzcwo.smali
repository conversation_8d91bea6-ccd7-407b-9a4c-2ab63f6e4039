.class final Lcom/google/android/gms/internal/ads/zzcwo;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/android/gms/internal/ads/zzgej;


# direct methods
.method public constructor <init>(Lcom/google/android/gms/internal/ads/zzcwp;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final zza(Ljava/lang/Throwable;)V
    .locals 0

    return-void
.end method

.method public final synthetic zzb(Ljava/lang/Object;)V
    .locals 0

    check-cast p1, Lcom/google/android/gms/internal/ads/zzcwh;

    invoke-virtual {p1}, Lcom/google/android/gms/internal/ads/zzcwh;->zzq()V

    return-void
.end method
