.class public final Landroidx/media3/common/b0;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/common/b0$e;,
        Landroidx/media3/common/b0$h;,
        Landroidx/media3/common/b0$g;,
        Landroidx/media3/common/b0$i;,
        Landroidx/media3/common/b0$c;,
        Landroidx/media3/common/b0$d;,
        Landroidx/media3/common/b0$j;,
        Landroidx/media3/common/b0$k;,
        Landroidx/media3/common/b0$b;,
        Landroidx/media3/common/b0$f;
    }
.end annotation


# static fields
.field public static final i:Landroidx/media3/common/b0;

.field public static final j:Ljava/lang/String;

.field public static final k:Ljava/lang/String;

.field public static final l:Ljava/lang/String;

.field public static final m:Ljava/lang/String;

.field public static final n:Ljava/lang/String;

.field public static final o:Ljava/lang/String;

.field public static final p:Landroidx/media3/common/i;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/media3/common/i<",
            "Landroidx/media3/common/b0;",
            ">;"
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field


# instance fields
.field public final a:Ljava/lang/String;

.field public final b:Landroidx/media3/common/b0$h;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation
.end field

.field public final c:Landroidx/media3/common/b0$h;
    .annotation build Landroidx/annotation/Nullable;
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public final d:Landroidx/media3/common/b0$g;

.field public final e:Landroidx/media3/common/d0;

.field public final f:Landroidx/media3/common/b0$d;

.field public final g:Landroidx/media3/common/b0$e;
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public final h:Landroidx/media3/common/b0$i;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Landroidx/media3/common/b0$c;

    invoke-direct {v0}, Landroidx/media3/common/b0$c;-><init>()V

    invoke-virtual {v0}, Landroidx/media3/common/b0$c;->a()Landroidx/media3/common/b0;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/b0;->i:Landroidx/media3/common/b0;

    const/4 v0, 0x1

    const/4 v0, 0x0

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/b0;->j:Ljava/lang/String;

    const/4 v0, 0x1

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/b0;->k:Ljava/lang/String;

    const/4 v0, 0x2

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/b0;->l:Ljava/lang/String;

    const/4 v0, 0x3

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/b0;->m:Ljava/lang/String;

    const/4 v0, 0x4

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/b0;->n:Ljava/lang/String;

    const/4 v0, 0x5

    invoke-static {v0}, Le2/u0;->E0(I)Ljava/lang/String;

    move-result-object v0

    sput-object v0, Landroidx/media3/common/b0;->o:Ljava/lang/String;

    new-instance v0, Landroidx/media3/common/b;

    invoke-direct {v0}, Landroidx/media3/common/b;-><init>()V

    sput-object v0, Landroidx/media3/common/b0;->p:Landroidx/media3/common/i;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Landroidx/media3/common/b0$e;Landroidx/media3/common/b0$h;Landroidx/media3/common/b0$g;Landroidx/media3/common/d0;Landroidx/media3/common/b0$i;)V
    .locals 0
    .param p3    # Landroidx/media3/common/b0$h;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/common/b0;->a:Ljava/lang/String;

    iput-object p3, p0, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    iput-object p3, p0, Landroidx/media3/common/b0;->c:Landroidx/media3/common/b0$h;

    iput-object p4, p0, Landroidx/media3/common/b0;->d:Landroidx/media3/common/b0$g;

    iput-object p5, p0, Landroidx/media3/common/b0;->e:Landroidx/media3/common/d0;

    iput-object p2, p0, Landroidx/media3/common/b0;->f:Landroidx/media3/common/b0$d;

    iput-object p2, p0, Landroidx/media3/common/b0;->g:Landroidx/media3/common/b0$e;

    iput-object p6, p0, Landroidx/media3/common/b0;->h:Landroidx/media3/common/b0$i;

    return-void
.end method

.method public synthetic constructor <init>(Ljava/lang/String;Landroidx/media3/common/b0$e;Landroidx/media3/common/b0$h;Landroidx/media3/common/b0$g;Landroidx/media3/common/d0;Landroidx/media3/common/b0$i;Landroidx/media3/common/b0$a;)V
    .locals 0

    invoke-direct/range {p0 .. p6}, Landroidx/media3/common/b0;-><init>(Ljava/lang/String;Landroidx/media3/common/b0$e;Landroidx/media3/common/b0$h;Landroidx/media3/common/b0$g;Landroidx/media3/common/d0;Landroidx/media3/common/b0$i;)V

    return-void
.end method

.method public static b(Ljava/lang/String;)Landroidx/media3/common/b0;
    .locals 1

    new-instance v0, Landroidx/media3/common/b0$c;

    invoke-direct {v0}, Landroidx/media3/common/b0$c;-><init>()V

    invoke-virtual {v0, p0}, Landroidx/media3/common/b0$c;->i(Ljava/lang/String;)Landroidx/media3/common/b0$c;

    move-result-object p0

    invoke-virtual {p0}, Landroidx/media3/common/b0$c;->a()Landroidx/media3/common/b0;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public a()Landroidx/media3/common/b0$c;
    .locals 2

    new-instance v0, Landroidx/media3/common/b0$c;

    const/4 v1, 0x1

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Landroidx/media3/common/b0$c;-><init>(Landroidx/media3/common/b0;Landroidx/media3/common/b0$a;)V

    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4
    .param p1    # Ljava/lang/Object;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Landroidx/media3/common/b0;

    const/4 v2, 0x1

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Landroidx/media3/common/b0;

    iget-object v1, p0, Landroidx/media3/common/b0;->a:Ljava/lang/String;

    iget-object v3, p1, Landroidx/media3/common/b0;->a:Ljava/lang/String;

    invoke-static {v1, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Landroidx/media3/common/b0;->f:Landroidx/media3/common/b0$d;

    iget-object v3, p1, Landroidx/media3/common/b0;->f:Landroidx/media3/common/b0$d;

    invoke-virtual {v1, v3}, Landroidx/media3/common/b0$d;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    iget-object v3, p1, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    invoke-static {v1, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Landroidx/media3/common/b0;->d:Landroidx/media3/common/b0$g;

    iget-object v3, p1, Landroidx/media3/common/b0;->d:Landroidx/media3/common/b0$g;

    invoke-static {v1, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Landroidx/media3/common/b0;->e:Landroidx/media3/common/d0;

    iget-object v3, p1, Landroidx/media3/common/b0;->e:Landroidx/media3/common/d0;

    invoke-static {v1, v3}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Landroidx/media3/common/b0;->h:Landroidx/media3/common/b0$i;

    iget-object p1, p1, Landroidx/media3/common/b0;->h:Landroidx/media3/common/b0$i;

    invoke-static {v1, p1}, Le2/u0;->c(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    const/4 v0, 0x1

    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public hashCode()I
    .locals 2

    iget-object v0, p0, Landroidx/media3/common/b0;->a:Ljava/lang/String;

    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v0

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Landroidx/media3/common/b0;->b:Landroidx/media3/common/b0$h;

    if-eqz v1, :cond_0

    invoke-virtual {v1}, Landroidx/media3/common/b0$h;->hashCode()I

    move-result v1

    goto :goto_0

    :cond_0
    const/4 v1, 0x1

    const/4 v1, 0x0

    :goto_0
    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Landroidx/media3/common/b0;->d:Landroidx/media3/common/b0$g;

    invoke-virtual {v1}, Landroidx/media3/common/b0$g;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Landroidx/media3/common/b0;->f:Landroidx/media3/common/b0$d;

    invoke-virtual {v1}, Landroidx/media3/common/b0$d;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Landroidx/media3/common/b0;->e:Landroidx/media3/common/d0;

    invoke-virtual {v1}, Landroidx/media3/common/d0;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    mul-int/lit8 v0, v0, 0x1f

    iget-object v1, p0, Landroidx/media3/common/b0;->h:Landroidx/media3/common/b0$i;

    invoke-virtual {v1}, Landroidx/media3/common/b0$i;->hashCode()I

    move-result v1

    add-int/2addr v0, v1

    return v0
.end method
