<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView android:textSize="16.0sp" android:textColor="@color/text_01" android:ellipsize="end" android:gravity="start|bottom|center" android:id="@id/tvMovieTitle" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="8.0dip" android:maxLines="1" android:textAlignment="viewStart" android:layout_marginStart="12.0dip" app:layout_constrainedWidth="true" app:layout_constraintBottom_toTopOf="@id/ll_score" app:layout_constraintEnd_toStartOf="@id/tvMovieInfo" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintHorizontal_chainStyle="packed" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_goneMarginTop="8.0dip" style="@style/style_import_text" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white_60" android:id="@id/tvMovieInfo" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/str_info" android:layout_marginStart="8.0dip" android:layout_marginEnd="12.0dip" app:drawableEndCompat="@mipmap/movie_detail_ic_arrow_right_white" app:layout_constraintBottom_toBottomOf="@id/tvMovieTitle" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/tvMovieTitle" app:layout_constraintTop_toTopOf="@id/tvMovieTitle" style="@style/style_regular_text" />
    <LinearLayout android:gravity="center" android:orientation="horizontal" android:id="@id/ll_score" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="4.0dip" android:layout_marginBottom="3.0dip" app:layout_constraintBottom_toBottomOf="@id/tvMovieTitle" app:layout_constraintStart_toStartOf="@id/tvMovieTitle" app:layout_constraintTop_toBottomOf="@id/tvMovieTitle">
        <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivMovieContent" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/ic_tag_edu" android:tint="@color/gray_40" app:layout_constraintEnd_toStartOf="@id/tvMovieContent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/tvMovieContent" style="@style/style_regular_text" />
        <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_category" android:layout_width="wrap_content" android:layout_height="wrap_content" app:text="School" />
        <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_genre" android:layout_width="wrap_content" android:layout_height="wrap_content" app:text="Science" />
        <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_duration" android:layout_width="wrap_content" android:layout_height="wrap_content" app:text="2h27m" />
        <com.transsion.moviedetail.view.CustomTextViewGroup android:id="@id/tv_students" android:layout_width="wrap_content" android:layout_height="wrap_content" app:text="228 students" />
    </LinearLayout>
    <View android:id="@id/v_detail_hot_zone" android:layout_width="0.0dip" android:layout_height="0.0dip" android:layout_marginTop="-8.0dip" android:layout_marginBottom="-8.0dip" app:layout_constraintBottom_toBottomOf="@id/ll_score" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/tvMovieTitle" />
    <androidx.appcompat.widget.AppCompatTextView android:textSize="12.0sp" android:textColor="@color/white_60" android:ellipsize="end" android:id="@id/tvDes" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="12.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="12.0dip" android:maxLines="2" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ll_score" style="@style/style_regular_text" />
    <FrameLayout android:id="@id/extension_container" android:layout_width="fill_parent" android:layout_height="32.0dip" android:layout_marginTop="8.0dip" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvDes" />
</androidx.constraintlayout.widget.ConstraintLayout>
