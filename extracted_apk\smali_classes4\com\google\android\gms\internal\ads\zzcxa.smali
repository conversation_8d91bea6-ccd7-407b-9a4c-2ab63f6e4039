.class public final Lcom/google/android/gms/internal/ads/zzcxa;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/android/gms/internal/ads/zzhhd;


# instance fields
.field private final zza:Lcom/google/android/gms/internal/ads/zzcwx;


# direct methods
.method public constructor <init>(Lcom/google/android/gms/internal/ads/zzcwx;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/google/android/gms/internal/ads/zzcxa;->zza:Lcom/google/android/gms/internal/ads/zzcwx;

    return-void
.end method


# virtual methods
.method public final zza()Lcom/google/android/gms/internal/ads/zzfgp;
    .locals 1

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzcxa;->zza:Lcom/google/android/gms/internal/ads/zzcwx;

    invoke-virtual {v0}, Lcom/google/android/gms/internal/ads/zzcwx;->zzb()Lcom/google/android/gms/internal/ads/zzfgp;

    move-result-object v0

    return-object v0
.end method

.method public final synthetic zzb()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzcxa;->zza:Lcom/google/android/gms/internal/ads/zzcwx;

    invoke-virtual {v0}, Lcom/google/android/gms/internal/ads/zzcwx;->zzb()Lcom/google/android/gms/internal/ads/zzfgp;

    move-result-object v0

    return-object v0
.end method
