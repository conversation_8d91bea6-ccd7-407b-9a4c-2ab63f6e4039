.class public interface abstract Landroidx/media3/exoplayer/video/g0;
.super Ljava/lang/Object;


# virtual methods
.method public abstract a(Landroidx/media3/exoplayer/video/p;)V
.end method

.method public abstract c(Landroidx/media3/exoplayer/video/o;)V
.end method

.method public abstract d(Le2/d;)V
.end method

.method public abstract e(Ljava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroidx/media3/common/p;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract f()Landroidx/media3/exoplayer/video/p;
.end method

.method public abstract g(Landroid/view/Surface;Le2/e0;)V
.end method

.method public abstract h(Landroidx/media3/common/y;)V
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Landroidx/media3/exoplayer/video/VideoSink$VideoSinkException;
        }
    .end annotation
.end method

.method public abstract isInitialized()Z
.end method

.method public abstract j()V
.end method

.method public abstract k()Landroidx/media3/exoplayer/video/VideoSink;
.end method

.method public abstract l(J)V
.end method

.method public abstract release()V
.end method
