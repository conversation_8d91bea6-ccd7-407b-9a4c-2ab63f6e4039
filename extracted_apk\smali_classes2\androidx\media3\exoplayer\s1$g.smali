.class public final Landroidx/media3/exoplayer/s1$g;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/exoplayer/s1;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "g"
.end annotation


# instance fields
.field public final a:Landroidx/media3/exoplayer/source/l$b;

.field public final b:J

.field public final c:J

.field public final d:Z

.field public final e:Z

.field public final f:Z


# direct methods
.method public constructor <init>(Landroidx/media3/exoplayer/source/l$b;JJZZZ)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/s1$g;->a:Landroidx/media3/exoplayer/source/l$b;

    iput-wide p2, p0, Landroidx/media3/exoplayer/s1$g;->b:J

    iput-wide p4, p0, Landroidx/media3/exoplayer/s1$g;->c:J

    iput-boolean p6, p0, Landroidx/media3/exoplayer/s1$g;->d:Z

    iput-boolean p7, p0, Landroidx/media3/exoplayer/s1$g;->e:Z

    iput-boolean p8, p0, Landroidx/media3/exoplayer/s1$g;->f:Z

    return-void
.end method
