.class public Landroidx/media3/container/XmpData$a;
.super Ljava/lang/Object;

# interfaces
.implements Landroid/os/Parcelable$Creator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/container/XmpData;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Landroid/os/Parcelable$Creator<",
        "Landroidx/media3/container/XmpData;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Landroid/os/Parcel;)Landroidx/media3/container/XmpData;
    .locals 2

    new-instance v0, Landroidx/media3/container/XmpData;

    const/4 v1, 0x1

    const/4 v1, 0x0

    invoke-direct {v0, p1, v1}, Landroidx/media3/container/XmpData;-><init>(Landroid/os/Parcel;Landroidx/media3/container/XmpData$a;)V

    return-object v0
.end method

.method public b(I)[Landroidx/media3/container/XmpData;
    .locals 0

    new-array p1, p1, [Landroidx/media3/container/XmpData;

    return-object p1
.end method

.method public bridge synthetic createFromParcel(Landroid/os/Parcel;)Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/container/XmpData$a;->a(Landroid/os/Parcel;)Landroidx/media3/container/XmpData;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic newArray(I)[Ljava/lang/Object;
    .locals 0

    invoke-virtual {p0, p1}, Landroidx/media3/container/XmpData$a;->b(I)[Landroidx/media3/container/XmpData;

    move-result-object p1

    return-object p1
.end method
