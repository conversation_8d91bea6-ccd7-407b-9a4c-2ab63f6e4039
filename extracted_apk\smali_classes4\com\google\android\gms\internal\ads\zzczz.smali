.class public final Lcom/google/android/gms/internal/ads/zzczz;
.super Ljava/lang/Object;


# instance fields
.field private final zza:Lcom/google/android/gms/internal/ads/zzfgy;

.field private final zzb:Lcom/google/android/gms/internal/ads/zzfgm;

.field private final zzc:Lcom/google/android/gms/internal/ads/zzemh;


# direct methods
.method public constructor <init>(Lcom/google/android/gms/internal/ads/zzfgy;Lcom/google/android/gms/internal/ads/zzfgm;Lcom/google/android/gms/internal/ads/zzemh;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/google/android/gms/internal/ads/zzczz;->zza:Lcom/google/android/gms/internal/ads/zzfgy;

    iput-object p2, p0, Lcom/google/android/gms/internal/ads/zzczz;->zzb:Lcom/google/android/gms/internal/ads/zzfgm;

    iput-object p3, p0, Lcom/google/android/gms/internal/ads/zzczz;->zzc:Lcom/google/android/gms/internal/ads/zzemh;

    return-void
.end method


# virtual methods
.method public final zza()Lcom/google/android/gms/internal/ads/zzemh;
    .locals 1

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzczz;->zzc:Lcom/google/android/gms/internal/ads/zzemh;

    return-object v0
.end method

.method public final zzb()Lcom/google/android/gms/internal/ads/zzfgm;
    .locals 1

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzczz;->zzb:Lcom/google/android/gms/internal/ads/zzfgm;

    return-object v0
.end method

.method public final zzc()Lcom/google/android/gms/internal/ads/zzfgy;
    .locals 1

    iget-object v0, p0, Lcom/google/android/gms/internal/ads/zzczz;->zza:Lcom/google/android/gms/internal/ads/zzfgy;

    return-object v0
.end method
