.class public interface abstract Lv0/e;
.super Ljava/lang/Object;

# interfaces
.implements Lv0/n;


# annotations
.annotation runtime L<PERSON>lin/Metadata;
.end annotation

.annotation build Lkotlin/jvm/internal/SourceDebugExtension;
.end annotation


# virtual methods
.method public abstract G0(F)F
.end method

.method public abstract N0(F)F
.end method

.method public abstract V(F)J
.end method

.method public abstract X0(J)J
.end method

.method public abstract getDensity()F
.end method

.method public abstract i0(F)I
.end method

.method public abstract o0(J)F
.end method
