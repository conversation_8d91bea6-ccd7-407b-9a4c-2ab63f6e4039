.class public final synthetic Lse/n;
.super Ljava/lang/Object;

# interfaces
.implements Lkc/h;


# instance fields
.field public final synthetic a:Lkc/b0;


# direct methods
.method public synthetic constructor <init>(Lkc/b0;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lse/n;->a:Lkc/b0;

    return-void
.end method


# virtual methods
.method public final a(Lkc/e;)Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lse/n;->a:Lkc/b0;

    invoke-static {v0, p1}, Lcom/google/firebase/remoteconfig/RemoteConfigRegistrar;->a(Lkc/b0;Lkc/e;)Lse/m;

    move-result-object p1

    return-object p1
.end method
