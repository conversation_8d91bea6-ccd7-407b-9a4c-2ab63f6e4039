.class public final Landroidx/media3/common/d$e;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/media3/common/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "e"
.end annotation


# instance fields
.field public a:I

.field public b:I

.field public c:I

.field public d:I

.field public e:I


# direct methods
.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    const/4 v0, 0x0

    iput v0, p0, Landroidx/media3/common/d$e;->a:I

    iput v0, p0, Landroidx/media3/common/d$e;->b:I

    const/4 v1, 0x1

    iput v1, p0, Landroidx/media3/common/d$e;->c:I

    iput v1, p0, Landroidx/media3/common/d$e;->d:I

    iput v0, p0, Landroidx/media3/common/d$e;->e:I

    return-void
.end method


# virtual methods
.method public a()Landroidx/media3/common/d;
    .locals 8

    new-instance v7, Landroidx/media3/common/d;

    iget v1, p0, Landroidx/media3/common/d$e;->a:I

    iget v2, p0, Landroidx/media3/common/d$e;->b:I

    iget v3, p0, Landroidx/media3/common/d$e;->c:I

    iget v4, p0, Landroidx/media3/common/d$e;->d:I

    iget v5, p0, Landroidx/media3/common/d$e;->e:I

    const/4 v6, 0x1

    const/4 v6, 0x0

    move-object v0, v7

    invoke-direct/range {v0 .. v6}, Landroidx/media3/common/d;-><init>(IIIIILandroidx/media3/common/d$a;)V

    return-object v7
.end method
