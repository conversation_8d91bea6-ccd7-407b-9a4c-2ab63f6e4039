.class public final Lcom/google/android/gms/internal/ads/zzcyq;
.super Ljava/lang/Object;

# interfaces
.implements Lcom/google/android/gms/internal/ads/zzhhd;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static zza()Lcom/google/android/gms/internal/ads/zzcyq;
    .locals 1

    invoke-static {}, Lcom/google/android/gms/internal/ads/zzcyp;->zza()Lcom/google/android/gms/internal/ads/zzcyq;

    move-result-object v0

    return-object v0
.end method


# virtual methods
.method public final synthetic zzb()Ljava/lang/Object;
    .locals 1

    new-instance v0, Lcom/google/android/gms/internal/ads/zzcyo;

    invoke-direct {v0}, Lcom/google/android/gms/internal/ads/zzcyo;-><init>()V

    return-object v0
.end method
