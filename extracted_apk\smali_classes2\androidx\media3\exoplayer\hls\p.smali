.class public final synthetic Landroidx/media3/exoplayer/hls/p;
.super Ljava/lang/Object;

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Landroidx/media3/exoplayer/hls/q;


# direct methods
.method public synthetic constructor <init>(Landroidx/media3/exoplayer/hls/q;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Landroidx/media3/exoplayer/hls/p;->a:Landroidx/media3/exoplayer/hls/q;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    iget-object v0, p0, Landroidx/media3/exoplayer/hls/p;->a:Landroidx/media3/exoplayer/hls/q;

    invoke-static {v0}, Landroidx/media3/exoplayer/hls/q;->d(Landroidx/media3/exoplayer/hls/q;)V

    return-void
.end method
