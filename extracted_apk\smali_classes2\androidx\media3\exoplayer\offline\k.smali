.class public final Landroidx/media3/exoplayer/offline/k;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/media3/exoplayer/offline/k$a;
    }
.end annotation


# instance fields
.field public final a:Landroidx/core/app/NotificationCompat$m;


# direct methods
.method public constructor <init>(Landroid/content/Context;Ljava/lang/String;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Landroidx/core/app/NotificationCompat$m;

    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object p1

    invoke-direct {v0, p1, p2}, Landroidx/core/app/NotificationCompat$m;-><init>(Landroid/content/Context;Ljava/lang/String;)V

    iput-object v0, p0, Landroidx/media3/exoplayer/offline/k;->a:Landroidx/core/app/NotificationCompat$m;

    return-void
.end method


# virtual methods
.method public final a(Landroid/content/Context;ILandroid/app/PendingIntent;Ljava/lang/String;IIIZZZ)Landroid/app/Notification;
    .locals 1
    .param p3    # Landroid/app/PendingIntent;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p5    # I
        .annotation build Landroidx/annotation/StringRes;
        .end annotation
    .end param

    iget-object v0, p0, Landroidx/media3/exoplayer/offline/k;->a:Landroidx/core/app/NotificationCompat$m;

    invoke-virtual {v0, p2}, Landroidx/core/app/NotificationCompat$m;->K(I)Landroidx/core/app/NotificationCompat$m;

    iget-object p2, p0, Landroidx/media3/exoplayer/offline/k;->a:Landroidx/core/app/NotificationCompat$m;

    const/4 v0, 0x1

    const/4 v0, 0x0

    if-nez p5, :cond_0

    move-object p1, v0

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object p1

    invoke-virtual {p1, p5}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    move-result-object p1

    :goto_0
    invoke-virtual {p2, p1}, Landroidx/core/app/NotificationCompat$m;->r(Ljava/lang/CharSequence;)Landroidx/core/app/NotificationCompat$m;

    iget-object p1, p0, Landroidx/media3/exoplayer/offline/k;->a:Landroidx/core/app/NotificationCompat$m;

    invoke-virtual {p1, p3}, Landroidx/core/app/NotificationCompat$m;->p(Landroid/app/PendingIntent;)Landroidx/core/app/NotificationCompat$m;

    iget-object p1, p0, Landroidx/media3/exoplayer/offline/k;->a:Landroidx/core/app/NotificationCompat$m;

    if-nez p4, :cond_1

    goto :goto_1

    :cond_1
    new-instance p2, Landroidx/core/app/NotificationCompat$k;

    invoke-direct {p2}, Landroidx/core/app/NotificationCompat$k;-><init>()V

    invoke-virtual {p2, p4}, Landroidx/core/app/NotificationCompat$k;->q(Ljava/lang/CharSequence;)Landroidx/core/app/NotificationCompat$k;

    move-result-object v0

    :goto_1
    invoke-virtual {p1, v0}, Landroidx/core/app/NotificationCompat$m;->N(Landroidx/core/app/NotificationCompat$p;)Landroidx/core/app/NotificationCompat$m;

    iget-object p1, p0, Landroidx/media3/exoplayer/offline/k;->a:Landroidx/core/app/NotificationCompat$m;

    invoke-virtual {p1, p6, p7, p8}, Landroidx/core/app/NotificationCompat$m;->I(IIZ)Landroidx/core/app/NotificationCompat$m;

    iget-object p1, p0, Landroidx/media3/exoplayer/offline/k;->a:Landroidx/core/app/NotificationCompat$m;

    invoke-virtual {p1, p9}, Landroidx/core/app/NotificationCompat$m;->F(Z)Landroidx/core/app/NotificationCompat$m;

    iget-object p1, p0, Landroidx/media3/exoplayer/offline/k;->a:Landroidx/core/app/NotificationCompat$m;

    invoke-virtual {p1, p10}, Landroidx/core/app/NotificationCompat$m;->J(Z)Landroidx/core/app/NotificationCompat$m;

    sget p1, Le2/u0;->a:I

    const/16 p2, 0x1f

    if-lt p1, p2, :cond_2

    iget-object p1, p0, Landroidx/media3/exoplayer/offline/k;->a:Landroidx/core/app/NotificationCompat$m;

    invoke-static {p1}, Landroidx/media3/exoplayer/offline/k$a;->a(Landroidx/core/app/NotificationCompat$m;)V

    :cond_2
    iget-object p1, p0, Landroidx/media3/exoplayer/offline/k;->a:Landroidx/core/app/NotificationCompat$m;

    invoke-virtual {p1}, Landroidx/core/app/NotificationCompat$m;->c()Landroid/app/Notification;

    move-result-object p1

    return-object p1
.end method

.method public b(Landroid/content/Context;ILandroid/app/PendingIntent;Ljava/lang/String;Ljava/util/List;I)Landroid/app/Notification;
    .locals 21
    .param p3    # Landroid/app/PendingIntent;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .param p4    # Ljava/lang/String;
        .annotation build Landroidx/annotation/Nullable;
        .end annotation
    .end param
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            "I",
            "Landroid/app/PendingIntent;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Landroidx/media3/exoplayer/offline/c;",
            ">;I)",
            "Landroid/app/Notification;"
        }
    .end annotation

    const/4 v0, 0x1

    const/4 v0, 0x0

    const/4 v1, 0x1

    const/4 v1, 0x0

    const/4 v2, 0x1

    const/4 v3, 0x1

    const/4 v3, 0x0

    const/4 v4, 0x1

    const/4 v4, 0x0

    const/4 v5, 0x1

    const/4 v5, 0x0

    const/4 v6, 0x1

    const/4 v6, 0x0

    const/4 v7, 0x1

    const/4 v7, 0x0

    const/4 v8, 0x1

    const/4 v8, 0x0

    const/4 v9, 0x1

    :goto_0
    invoke-interface/range {p5 .. p5}, Ljava/util/List;->size()I

    move-result v10

    if-ge v3, v10, :cond_5

    move-object/from16 v10, p5

    invoke-interface {v10, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v11

    check-cast v11, Landroidx/media3/exoplayer/offline/c;

    iget v12, v11, Landroidx/media3/exoplayer/offline/c;->b:I

    if-eqz v12, :cond_4

    const/4 v13, 0x2

    if-eq v12, v13, :cond_1

    const/4 v13, 0x5

    if-eq v12, v13, :cond_0

    const/4 v13, 0x7

    if-eq v12, v13, :cond_1

    goto :goto_2

    :cond_0
    const/4 v7, 0x1

    goto :goto_2

    :cond_1
    invoke-virtual {v11}, Landroidx/media3/exoplayer/offline/c;->b()F

    move-result v4

    const/high16 v12, -0x40800000    # -1.0f

    cmpl-float v12, v4, v12

    if-eqz v12, :cond_2

    add-float/2addr v0, v4

    const/4 v9, 0x1

    const/4 v9, 0x0

    :cond_2
    invoke-virtual {v11}, Landroidx/media3/exoplayer/offline/c;->a()J

    move-result-wide v11

    const-wide/16 v13, 0x0

    cmp-long v4, v11, v13

    if-lez v4, :cond_3

    const/4 v4, 0x1

    goto :goto_1

    :cond_3
    const/4 v4, 0x1

    const/4 v4, 0x0

    :goto_1
    or-int/2addr v6, v4

    add-int/lit8 v8, v8, 0x1

    const/4 v4, 0x1

    goto :goto_2

    :cond_4
    const/4 v5, 0x1

    :goto_2
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_5
    if-eqz v4, :cond_6

    sget v3, Landroidx/media3/exoplayer/R$string;->exo_download_downloading:I

    :goto_3
    move v15, v3

    const/4 v3, 0x1

    goto :goto_5

    :cond_6
    if-eqz v5, :cond_9

    if-eqz p6, :cond_9

    and-int/lit8 v3, p6, 0x2

    if-eqz v3, :cond_7

    sget v3, Landroidx/media3/exoplayer/R$string;->exo_download_paused_for_wifi:I

    :goto_4
    move v15, v3

    const/4 v3, 0x1

    const/4 v3, 0x0

    goto :goto_5

    :cond_7
    and-int/lit8 v3, p6, 0x1

    if-eqz v3, :cond_8

    sget v3, Landroidx/media3/exoplayer/R$string;->exo_download_paused_for_network:I

    goto :goto_4

    :cond_8
    sget v3, Landroidx/media3/exoplayer/R$string;->exo_download_paused:I

    goto :goto_4

    :cond_9
    if-eqz v7, :cond_a

    sget v3, Landroidx/media3/exoplayer/R$string;->exo_download_removing:I

    goto :goto_3

    :cond_a
    const/4 v3, 0x1

    const/4 v15, 0x1

    const/4 v15, 0x0

    :goto_5
    if-eqz v3, :cond_d

    const/16 v3, 0x64

    if-eqz v4, :cond_c

    int-to-float v4, v8

    div-float/2addr v0, v4

    float-to-int v0, v0

    if-eqz v9, :cond_b

    if-eqz v6, :cond_b

    const/4 v1, 0x1

    :cond_b
    move/from16 v17, v0

    move/from16 v18, v1

    const/16 v16, 0x64

    goto :goto_6

    :cond_c
    const/16 v16, 0x64

    const/16 v17, 0x0

    const/16 v18, 0x1

    goto :goto_6

    :cond_d
    const/16 v16, 0x0

    const/16 v17, 0x0

    const/16 v18, 0x0

    :goto_6
    const/16 v19, 0x1

    const/16 v20, 0x0

    move-object/from16 v10, p0

    move-object/from16 v11, p1

    move/from16 v12, p2

    move-object/from16 v13, p3

    move-object/from16 v14, p4

    invoke-virtual/range {v10 .. v20}, Landroidx/media3/exoplayer/offline/k;->a(Landroid/content/Context;ILandroid/app/PendingIntent;Ljava/lang/String;IIIZZZ)Landroid/app/Notification;

    move-result-object v0

    return-object v0
.end method
